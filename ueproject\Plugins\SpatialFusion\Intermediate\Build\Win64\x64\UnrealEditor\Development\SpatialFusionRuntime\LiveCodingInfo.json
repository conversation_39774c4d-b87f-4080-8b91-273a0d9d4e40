{"RemapUnityFiles": {"Module.SpatialFusionRuntime.1.cpp.obj": ["InfluenceMapManager2D.gen.cpp.obj", "InfluenceMapManager3D.gen.cpp.obj", "InfluenceMapManagerBase.gen.cpp.obj"], "Module.SpatialFusionRuntime.2.cpp.obj": ["PropagationRuleAsset.gen.cpp.obj", "PropagationRuleEngine.gen.cpp.obj", "PropagationRuleSet.gen.cpp.obj", "PropagationRuleTypes.gen.cpp.obj", "PropagationStrategy.gen.cpp.obj"], "Module.SpatialFusionRuntime.3.cpp.obj": ["SpatialFusionRuntime.init.gen.cpp.obj", "SpatialFusionTypes.gen.cpp.obj", "PerModuleInline.gen.cpp.obj", "InfluenceGrid2D.cpp.obj", "InfluenceGrid3D.cpp.obj", "InfluenceGridInterface.cpp.obj", "SpatialFusionRuntime.cpp.obj", "InfluenceMapManager.cpp.obj", "InfluenceMapManager2D.cpp.obj", "InfluenceMapManager3D.cpp.obj", "InfluenceMapManagerBase.cpp.obj", "PropagationAsyncTask.cpp.obj", "PropagationRuleAsset.cpp.obj", "PropagationRuleEngine.cpp.obj", "PropagationRuleSet.cpp.obj", "PropagationRuleTypes.cpp.obj", "PropagationStrategy.cpp.obj"]}}