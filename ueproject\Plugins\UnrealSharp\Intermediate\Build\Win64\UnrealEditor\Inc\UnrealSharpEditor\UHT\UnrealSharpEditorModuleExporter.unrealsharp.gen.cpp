#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpEditor\UnrealSharpEditorModuleExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UFUnrealSharpEditorModuleExporter
{
    static const FCSExportedFunction UnrealSharpBind_InitializeUnrealSharpEditorCallbacks;
    static const FCSExportedFunction UnrealSharpBind_GetProjectPaths;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFUnrealSharpEditorModuleExporter::UnrealSharpBind_InitializeUnrealSharpEditorCallbacks = FCSExportedFunction("FUnrealSharpEditorModuleExporter", "InitializeUnrealSharpEditorCallbacks", (void*)&UFUnrealSharpEditorModuleExporter::InitializeUnrealSharpEditorCallbacks, GetFunctionSize(UFUnrealSharpEditorModuleExporter::InitializeUnrealSharpEditorCallbacks));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFUnrealSharpEditorModuleExporter::UnrealSharpBind_GetProjectPaths = FCSExportedFunction("FUnrealSharpEditorModuleExporter", "GetProjectPaths", (void*)&UFUnrealSharpEditorModuleExporter::GetProjectPaths, GetFunctionSize(UFUnrealSharpEditorModuleExporter::GetProjectPaths));

