// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "NiagaraDataInterface.h"
#include "NiagaraCommon.h"
#include "VectorVM.h"
#include "NiagaraInfluenceMapDataInterface.generated.h"

// Forward declarations
class UInfluenceMapManagerBase;
class UInfluenceMapManager2D;
class UInfluenceMapManager3D;
class FNiagaraSystemInstance;

/**
 * Specialized Niagara Data Interface for Influence Maps
 * Optimized for influence map operations with better performance
 */
UCLASS(EditInlineNew, Category = "SpatialFusion", meta = (DisplayName = "Influence Map Data Interface"))
class SPATIALFUSIONRUNTIME_API UNiagaraInfluenceMapDataInterface : public UNiagaraDataInterface
{
	GENERATED_BODY()

public:
	UNiagaraInfluenceMapDataInterface();

	//~ UNiagaraDataInterface interface
	virtual void PostInitProperties() override;
	virtual void GetFunctions(TArray<FNiagaraFunctionSignature>& OutFunctions) override;
	virtual void GetVMExternalFunction(const FVMExternalFunctionBindingInfo& BindingInfo, void* InstanceData, FVMExternalFunction &OutFunc) override;
	virtual bool InitPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance) override;
	virtual void DestroyPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance) override;
	virtual int32 PerInstanceDataSize() const override;
	virtual bool PerInstanceDataPassedToRenderThread() const override { return false; }
	virtual bool HasPreSimulateTick() const override { return true; }
	virtual void PreSimulateTick(void* PerInstanceData, float DeltaSeconds) override;
	virtual bool Equals(const UNiagaraDataInterface* Other) const override;
	virtual bool CanExecuteOnTarget(ENiagaraSimTarget Target) const override { return Target == ENiagaraSimTarget::CPUSim; }

	// Configuration properties

	/** Reference to the influence map manager */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	TObjectPtr<UInfluenceMapManagerBase> InfluenceMapManager;

	/** Primary value key to sample */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	FName PrimaryValueKey = TEXT("Default");

	/** Secondary value key for dual-channel operations */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	FName SecondaryValueKey = TEXT("Secondary");

	/** Whether to use world space coordinates */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	bool bUseWorldSpace = true;

	/** Default value for out-of-bounds sampling */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	float DefaultValue = 0.0f;

	/** Whether to use bilinear interpolation */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	bool bUseInterpolation = true;

	/** Gradient calculation step size */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	float GradientStepSize = 1.0f;

	/** Whether to normalize gradients */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	bool bNormalizeGradients = false;

	/** Threshold for binary operations */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	float Threshold = 0.5f;

	/** Smoothing factor for temporal operations */
	UPROPERTY(EditAnywhere, Category = "Influence Map")
	float SmoothingFactor = 0.1f;

	// Function names
	static const FName SampleInfluenceName;
	static const FName SampleInfluenceBilinearName;
	static const FName SampleInfluenceGradientName;
	static const FName SampleInfluenceLaplacianName;
	static const FName SampleDualChannelName;
	static const FName SampleInfluenceArrayName;
	static const FName GetInfluenceStatisticsName;
	static const FName IsAboveThresholdName;
	static const FName GetLocalMaximaName;
	static const FName GetLocalMinimaName;
	static const FName SampleInfluenceRadialName;
	static const FName SampleInfluenceDirectionalName;
	static const FName GetInfluenceHistogramName;

protected:
	/**
	 * Per-instance data for influence map operations
	 */
	struct FNiagaraInfluenceMapInstanceData
	{
		/** Cached influence map manager */
		TWeakObjectPtr<UInfluenceMapManagerBase> CachedInfluenceMapManager;

		/** Cached 2D manager for optimized 2D operations */
		TWeakObjectPtr<UInfluenceMapManager2D> CachedInfluenceMapManager2D;

		/** Cached 3D manager for optimized 3D operations */
		TWeakObjectPtr<UInfluenceMapManager3D> CachedInfluenceMapManager3D;

		/** Grid properties cache */
		FVector GridDimensions = FVector::ZeroVector;
		float CellSize = 100.0f;
		FVector WorldOrigin = FVector::ZeroVector;
		bool bIs3D = false;

		/** Performance cache */
		TArray<float> CachedGridData;
		bool bGridDataCached = false;
		float LastCacheTime = 0.0f;

		/** Statistics cache */
		float MinValue = 0.0f;
		float MaxValue = 0.0f;
		float AverageValue = 0.0f;
		bool bStatsCached = false;

		/** Update cache */
		void UpdateCache(UNiagaraInfluenceMapDataInterface* Interface);
		void UpdateGridDataCache(const FName& ValueKey);
		void UpdateStatisticsCache(const FName& ValueKey);
		bool NeedsCacheUpdate(float CurrentTime) const;
	};

	// VM function implementations

	/** Sample influence value at position */
	void SampleInfluence(FVectorVMExternalFunctionContext& Context);

	/** Sample influence with bilinear interpolation */
	void SampleInfluenceBilinear(FVectorVMExternalFunctionContext& Context);

	/** Sample influence gradient */
	void SampleInfluenceGradient(FVectorVMExternalFunctionContext& Context);

	/** Sample influence Laplacian (second derivative) */
	void SampleInfluenceLaplacian(FVectorVMExternalFunctionContext& Context);

	/** Sample dual channel values */
	void SampleDualChannel(FVectorVMExternalFunctionContext& Context);

	/** Sample influence from array of positions */
	void SampleInfluenceArray(FVectorVMExternalFunctionContext& Context);

	/** Get influence statistics */
	void GetInfluenceStatistics(FVectorVMExternalFunctionContext& Context);

	/** Check if value is above threshold */
	void IsAboveThreshold(FVectorVMExternalFunctionContext& Context);

	/** Get local maxima */
	void GetLocalMaxima(FVectorVMExternalFunctionContext& Context);

	/** Get local minima */
	void GetLocalMinima(FVectorVMExternalFunctionContext& Context);

	/** Sample influence in radial pattern */
	void SampleInfluenceRadial(FVectorVMExternalFunctionContext& Context);

	/** Sample influence in directional pattern */
	void SampleInfluenceDirectional(FVectorVMExternalFunctionContext& Context);

	/** Get influence histogram */
	void GetInfluenceHistogram(FVectorVMExternalFunctionContext& Context);

	// Helper functions

	/**
	 * Sample influence with high-performance interpolation
	 * @param Position - Position to sample
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return Interpolated value
	 */
	float SampleInfluenceOptimized(const FVector& Position, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Calculate gradient using central differences
	 * @param Position - Position to calculate gradient at
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return Gradient vector
	 */
	FVector CalculateGradient(const FVector& Position, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Calculate Laplacian (divergence of gradient)
	 * @param Position - Position to calculate Laplacian at
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return Laplacian value
	 */
	float CalculateLaplacian(const FVector& Position, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Check if position is a local maximum
	 * @param Position - Position to check
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return True if local maximum
	 */
	bool IsLocalMaximum(const FVector& Position, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Check if position is a local minimum
	 * @param Position - Position to check
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return True if local minimum
	 */
	bool IsLocalMinimum(const FVector& Position, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Sample influence in a radial pattern around a center
	 * @param Center - Center position
	 * @param Radius - Sampling radius
	 * @param NumSamples - Number of samples
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return Average value in radius
	 */
	float SampleRadialPattern(const FVector& Center, float Radius, int32 NumSamples, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Sample influence in a directional pattern
	 * @param Position - Start position
	 * @param Direction - Sampling direction
	 * @param Distance - Sampling distance
	 * @param NumSamples - Number of samples
	 * @param ValueKey - Value key
	 * @param InstanceData - Instance data
	 * @return Average value along direction
	 */
	float SampleDirectionalPattern(const FVector& Position, const FVector& Direction, float Distance, int32 NumSamples, const FName& ValueKey, const FNiagaraInfluenceMapInstanceData* InstanceData) const;

	/**
	 * Get function signature for influence map functions
	 * @param FunctionName - Function name
	 * @return Function signature
	 */
	FNiagaraFunctionSignature GetInfluenceMapFunctionSignature(const FName& FunctionName) const;
};
