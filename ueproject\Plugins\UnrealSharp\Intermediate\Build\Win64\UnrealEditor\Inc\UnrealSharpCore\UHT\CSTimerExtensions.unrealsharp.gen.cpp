#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\CSTimerExtensions.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UCSTimerExtensions
{
    static const FCSExportedFunction UnrealSharpBind_SetTimerForNextTick;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UCSTimerExtensions::UnrealSharpBind_SetTimerForNextTick = FCSExportedFunction("CSTimerExtensions", "SetTimerForNextTick", (void*)&UCSTimerExtensions::SetTimerForNextTick, GetFunctionSize(UCSTimerExtensions::SetTimerForNextTick));

