// Copyright Epic Games, Inc. All Rights Reserved.

#include "PropagationRuleEngine.h"
#include "PropagationRuleSet.h"
#include "PropagationRuleAsset.h"
#include "InfluenceMapManagerBase.h"
#include "SpatialFusionTypes.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"

//////////////////////////////////////////////////////////////////////////
// UPropagationRuleEngine

UPropagationRuleEngine::UPropagationRuleEngine()
{
	// Set default values
	bIsEnabled = true;
	bUseOptimizations = true;
	bCollectStatistics = true;
	MaxExecutionTimePerFrame = 5.0f;
	CurrentSimulationTime = 0.0f;

	// Initialize statistics
	CurrentStats.Reset();
}

FRuleExecutionStats UPropagationRuleEngine::ExecuteRulesForAllCells(float DeltaTime)
{
	if (!bIsEnabled || !CurrentRuleSet || !GridManager)
	{
		return CurrentStats;
	}

	CurrentSimulationTime += DeltaTime;
	double StartTime = FPlatformTime::Seconds();

	// Reset frame statistics
	FRuleExecutionStats FrameStats;
	FrameStats.Reset();

	// Get all cell positions from grid manager
	TArray<FVector> AllCellPositions;
	
	// This is a simplified implementation - in practice, you'd get this from the grid manager
	FVector GridDimensions = GridManager->GetGridDimensions();
	float CellSize = GridManager->GetCellSize();
	FVector WorldOrigin = GridManager->GetWorldOrigin();

	for (int32 X = 0; X < static_cast<int32>(GridDimensions.X); X++)
	{
		for (int32 Y = 0; Y < static_cast<int32>(GridDimensions.Y); Y++)
		{
			for (int32 Z = 0; Z < static_cast<int32>(GridDimensions.Z); Z++)
			{
				FVector CellPosition = WorldOrigin + FVector(X, Y, Z) * CellSize;
				AllCellPositions.Add(CellPosition);

				// Check execution time limit
				if (ShouldThrottleExecution(StartTime))
				{
					break;
				}
			}
			if (ShouldThrottleExecution(StartTime))
			{
				break;
			}
		}
		if (ShouldThrottleExecution(StartTime))
		{
			break;
		}
	}

	// Execute rules for collected cells
	FrameStats = ExecuteRulesForCells(AllCellPositions, DeltaTime);

	// Update cumulative statistics
	if (bCollectStatistics)
	{
		CurrentStats.TotalRulesExecuted += FrameStats.TotalRulesExecuted;
		CurrentStats.TotalExecutionTime += FrameStats.TotalExecutionTime;
		CurrentStats.CellsProcessed += FrameStats.CellsProcessed;
		CurrentStats.RuleFailures += FrameStats.RuleFailures;
		CurrentStats.ConditionEvaluations += FrameStats.ConditionEvaluations;
		CurrentStats.ActionExecutions += FrameStats.ActionExecutions;
		CurrentStats.UpdateAverages();

		// Merge rule performance metrics
		for (const auto& MetricPair : FrameStats.RulePerformanceMetrics)
		{
			if (CurrentStats.RulePerformanceMetrics.Contains(MetricPair.Key))
			{
				CurrentStats.RulePerformanceMetrics[MetricPair.Key] += MetricPair.Value;
			}
			else
			{
				CurrentStats.RulePerformanceMetrics.Add(MetricPair.Key, MetricPair.Value);
			}
		}
	}

	return FrameStats;
}

FRuleExecutionStats UPropagationRuleEngine::ExecuteRulesForCells(const TArray<FVector>& CellPositions, float DeltaTime)
{
	FRuleExecutionStats Stats;
	Stats.Reset();

	if (!bIsEnabled || !CurrentRuleSet || !GridManager)
	{
		return Stats;
	}

	double StartTime = FPlatformTime::Seconds();

	for (const FVector& CellPosition : CellPositions)
	{
		int32 RulesExecuted = ExecuteRulesForCell(CellPosition, DeltaTime);
		Stats.TotalRulesExecuted += RulesExecuted;
		Stats.CellsProcessed++;

		// Check execution time limit
		if (ShouldThrottleExecution(StartTime))
		{
			break;
		}
	}

	Stats.TotalExecutionTime = static_cast<float>((FPlatformTime::Seconds() - StartTime) * 1000.0);
	Stats.UpdateAverages();

	return Stats;
}

int32 UPropagationRuleEngine::ExecuteRulesForCell(const FVector& CellPosition, float DeltaTime)
{
	if (!bIsEnabled || !CurrentRuleSet || !GridManager)
	{
		return 0;
	}

	// Create propagation context
	FPropagationContext Context = CreatePropagationContext(CellPosition, DeltaTime);

	// Execute rule set
	int32 RulesExecuted = CurrentRuleSet->ExecuteRules(Context);

	// Apply context changes back to grid
	ApplyContextChangesToGrid(Context);

	// Update last execution time
	LastExecutionTimes.Add(CellPosition, CurrentSimulationTime);

	return RulesExecuted;
}

void UPropagationRuleEngine::SetRuleSet(UPropagationRuleSet* NewRuleSet)
{
	CurrentRuleSet = NewRuleSet;
	
	// Clear cache when rule set changes
	if (bUseOptimizations)
	{
		ClearExecutionCache();
	}
}

FRuleExecutionStats UPropagationRuleEngine::GetExecutionStatistics() const
{
	return CurrentStats;
}

void UPropagationRuleEngine::ResetStatistics()
{
	CurrentStats.Reset();
	LastExecutionTimes.Empty();
	RuleExecutionCounts.Empty();
}

void UPropagationRuleEngine::SetEnabled(bool bEnabled)
{
	bIsEnabled = bEnabled;
}

bool UPropagationRuleEngine::CanRuleExecuteForCell(UPropagationRuleAsset* Rule, const FVector& CellPosition) const
{
	if (!Rule || !Rule->bIsEnabled || !GridManager)
	{
		return false;
	}

	// Create context for testing
	FPropagationContext TestContext = CreatePropagationContext(CellPosition, 0.0f);
	TestContext.bIsPreview = true;

	return Rule->ShouldExecute(TestContext);
}

FPropagationContext UPropagationRuleEngine::PreviewRuleExecution(const FVector& CellPosition, float DeltaTime) const
{
	FPropagationContext PreviewContext = CreatePropagationContext(CellPosition, DeltaTime);
	PreviewContext.bIsPreview = true;

	return PreviewContext;
}

TMap<FName, float> UPropagationRuleEngine::GetRulePerformanceMetrics() const
{
	return CurrentStats.RulePerformanceMetrics;
}

void UPropagationRuleEngine::ClearExecutionCache()
{
	ExecutionCache.Empty();
}

void UPropagationRuleEngine::SetMaxExecutionTimePerFrame(float MaxTime)
{
	MaxExecutionTimePerFrame = FMath::Max(0.1f, MaxTime);
}

FPropagationContext UPropagationRuleEngine::CreatePropagationContext(const FVector& CellPosition, float DeltaTime) const
{
	FPropagationContext Context;
	Context.CellPosition = CellPosition;
	Context.CurrentTime = CurrentSimulationTime;
	Context.DeltaTime = DeltaTime;
	Context.GridManager = GridManager;

	// Get last execution time for this cell
	if (const float* LastTime = LastExecutionTimes.Find(CellPosition))
	{
		Context.TimeSinceLastExecution = CurrentSimulationTime - *LastTime;
	}
	else
	{
		Context.TimeSinceLastExecution = FLT_MAX; // Never executed before
	}

	// Get cell values from grid manager
	// This is a simplified implementation - you'd need to implement GetAllCellValues in the grid manager
	Context.SetCellValue(TEXT("Default"), GridManager->GetCellValueAtWorldPosition(CellPosition, TEXT("Default")));

	// Get neighbor positions and values
	Context.NeighborPositions = GetNeighborPositions(CellPosition);
	Context.NeighborValues.SetNum(Context.NeighborPositions.Num());
	Context.NeighborTags.SetNum(Context.NeighborPositions.Num());

	for (int32 i = 0; i < Context.NeighborPositions.Num(); i++)
	{
		const FVector& NeighborPos = Context.NeighborPositions[i];

		// Get neighbor values
		FInfluenceCellData& NeighborData = Context.NeighborValues[i];
		NeighborData.Values.Add(TEXT("Default"), GridManager->GetCellValueAtWorldPosition(NeighborPos, TEXT("Default")));

		// Get neighbor tags (simplified - you'd need to implement tag support in grid manager)
		FGameplayTagContainer& NeighborTagContainer = Context.NeighborTags[i];
		// NeighborTagContainer = GridManager->GetCellTagsAtWorldPosition(NeighborPos);
	}

	return Context;
}

TArray<FVector> UPropagationRuleEngine::GetNeighborPositions(const FVector& CellPosition) const
{
	TArray<FVector> NeighborPositions;
	
	if (!GridManager)
	{
		return NeighborPositions;
	}

	float CellSize = GridManager->GetCellSize();
	FVector GridDimensions = GridManager->GetGridDimensions();

	// Get 8-connected neighbors in 2D (or 26-connected in 3D)
	for (int32 X = -1; X <= 1; X++)
	{
		for (int32 Y = -1; Y <= 1; Y++)
		{
			for (int32 Z = (GridDimensions.Z > 1 ? -1 : 0); Z <= (GridDimensions.Z > 1 ? 1 : 0); Z++)
			{
				if (X == 0 && Y == 0 && Z == 0)
				{
					continue; // Skip center cell
				}

				FVector NeighborPos = CellPosition + FVector(X, Y, Z) * CellSize;
				
				// Check if neighbor is within grid bounds
				// This is simplified - you'd need proper bounds checking
				NeighborPositions.Add(NeighborPos);
			}
		}
	}

	return NeighborPositions;
}

bool UPropagationRuleEngine::ExecuteRule(UPropagationRuleAsset* Rule, FPropagationContext& Context)
{
	if (!Rule)
	{
		return false;
	}

	double RuleStartTime = FPlatformTime::Seconds();
	bool bSuccess = false;

	// Check cache first
	bool bCachedResult = false;
	if (bUseOptimizations && CheckExecutionCache(Rule, Context, bCachedResult))
	{
		bSuccess = bCachedResult;
	}
	else
	{
		// Execute rule
		bSuccess = Rule->ExecuteRule(Context);

		// Update cache
		if (bUseOptimizations)
		{
			UpdateExecutionCache(Rule, Context, bSuccess);
		}
	}

	// Update performance statistics
	float ExecutionTime = static_cast<float>((FPlatformTime::Seconds() - RuleStartTime) * 1000.0);
	UpdatePerformanceStats(Rule, ExecutionTime, bSuccess);

	return bSuccess;
}

bool UPropagationRuleEngine::CheckExecutionCache(UPropagationRuleAsset* Rule, const FPropagationContext& Context, bool& OutCachedResult) const
{
	if (!Rule || !bUseOptimizations)
	{
		return false;
	}

	uint32 ContextHash = CalculateContextHash(Context);
	uint32 CacheKey = HashCombine(GetTypeHash(Rule), ContextHash);

	if (const FRuleExecutionCacheEntry* CacheEntry = ExecutionCache.Find(CacheKey))
	{
		if (CacheEntry->IsValid(CurrentSimulationTime, ContextHash))
		{
			OutCachedResult = CacheEntry->bResult;
			return true;
		}
	}

	return false;
}

void UPropagationRuleEngine::UpdateExecutionCache(UPropagationRuleAsset* Rule, const FPropagationContext& Context, bool Result)
{
	if (!Rule || !bUseOptimizations)
	{
		return;
	}

	uint32 ContextHash = CalculateContextHash(Context);
	uint32 CacheKey = HashCombine(GetTypeHash(Rule), ContextHash);

	FRuleExecutionCacheEntry& CacheEntry = ExecutionCache.FindOrAdd(CacheKey);
	CacheEntry.bResult = Result;
	CacheEntry.CacheTime = CurrentSimulationTime;
	CacheEntry.CacheDuration = Rule->bCacheConditionEvaluations ? Rule->ConditionCacheDuration : 0.1f;
	CacheEntry.ContextHash = ContextHash;
}

uint32 UPropagationRuleEngine::CalculateContextHash(const FPropagationContext& Context) const
{
	uint32 Hash = GetTypeHash(Context.CellPosition);
	Hash = HashCombine(Hash, GetTypeHash(Context.CurrentTime));
	
	// Hash cell values
	for (const auto& ValuePair : Context.CellValues)
	{
		Hash = HashCombine(Hash, GetTypeHash(ValuePair.Key));
		Hash = HashCombine(Hash, GetTypeHash(ValuePair.Value));
	}

	// Hash cell tags
	for (const FGameplayTag& Tag : Context.CellTags)
	{
		Hash = HashCombine(Hash, GetTypeHash(Tag));
	}

	return Hash;
}

void UPropagationRuleEngine::UpdatePerformanceStats(UPropagationRuleAsset* Rule, float ExecutionTime, bool bSuccess)
{
	if (!bCollectStatistics || !Rule)
	{
		return;
	}

	// Update rule-specific metrics
	FName RuleName = Rule->RuleName;
	if (CurrentStats.RulePerformanceMetrics.Contains(RuleName))
	{
		CurrentStats.RulePerformanceMetrics[RuleName] += ExecutionTime;
	}
	else
	{
		CurrentStats.RulePerformanceMetrics.Add(RuleName, ExecutionTime);
	}

	// Update general statistics
	if (bSuccess)
	{
		CurrentStats.ActionExecutions++;
	}
	else
	{
		CurrentStats.RuleFailures++;
	}

	CurrentStats.ConditionEvaluations++;
}

bool UPropagationRuleEngine::ShouldThrottleExecution(double StartTime) const
{
	double CurrentTime = FPlatformTime::Seconds();
	double ElapsedTime = (CurrentTime - StartTime) * 1000.0; // Convert to milliseconds
	
	return ElapsedTime >= MaxExecutionTimePerFrame;
}

void UPropagationRuleEngine::ApplyContextChangesToGrid(const FPropagationContext& Context)
{
	if (!GridManager || Context.bIsPreview)
	{
		return;
	}

	// Apply cell value changes
	for (const auto& ValuePair : Context.CellValues)
	{
		GridManager->SetCellValueAtWorldPosition(Context.CellPosition, ValuePair.Key, ValuePair.Value);
	}

	// Apply cell tag changes (if grid manager supports tags)
	// This would need to be implemented in the grid manager
	// GridManager->SetCellTagsAtWorldPosition(Context.CellPosition, Context.CellTags);
}

bool UPropagationRuleEngine::ValidateRuleConstraints(UPropagationRuleAsset* Rule, const FPropagationContext& Context) const
{
	if (!Rule)
	{
		return false;
	}

	// Check maximum executions per cell
	if (Rule->MaxExecutionsPerCell > 0)
	{
		TPair<FVector, FName> CellRulePair(Context.CellPosition, Rule->RuleName);
		if (const int32* ExecutionCount = RuleExecutionCounts.Find(CellRulePair))
		{
			if (*ExecutionCount >= Rule->MaxExecutionsPerCell)
			{
				return false;
			}
		}
	}

	// Check maximum total executions
	if (Rule->MaxTotalExecutions > 0)
	{
		int32 TotalExecutions = 0;
		for (const auto& CountPair : RuleExecutionCounts)
		{
			if (CountPair.Key.Value == Rule->RuleName)
			{
				TotalExecutions += CountPair.Value;
			}
		}
		
		if (TotalExecutions >= Rule->MaxTotalExecutions)
		{
			return false;
		}
	}

	return true;
}

#if WITH_EDITOR
FString UPropagationRuleEngine::GenerateExecutionReport() const
{
	FString Report;
	Report += TEXT("=== Propagation Rule Engine Execution Report ===\n");
	Report += FString::Printf(TEXT("Total Rules Executed: %d\n"), CurrentStats.TotalRulesExecuted);
	Report += FString::Printf(TEXT("Total Execution Time: %.2f ms\n"), CurrentStats.TotalExecutionTime);
	Report += FString::Printf(TEXT("Average Time Per Rule: %.2f ms\n"), CurrentStats.AverageExecutionTimePerRule);
	Report += FString::Printf(TEXT("Cells Processed: %d\n"), CurrentStats.CellsProcessed);
	Report += FString::Printf(TEXT("Rule Failures: %d\n"), CurrentStats.RuleFailures);
	Report += FString::Printf(TEXT("Condition Evaluations: %d\n"), CurrentStats.ConditionEvaluations);
	Report += FString::Printf(TEXT("Action Executions: %d\n"), CurrentStats.ActionExecutions);
	
	Report += TEXT("\n=== Rule Performance Metrics ===\n");
	for (const auto& MetricPair : CurrentStats.RulePerformanceMetrics)
	{
		Report += FString::Printf(TEXT("%s: %.2f ms\n"), *MetricPair.Key.ToString(), MetricPair.Value);
	}

	return Report;
}

TMap<FName, FString> UPropagationRuleEngine::VisualizeRuleExecutionForCell(const FVector& CellPosition) const
{
	TMap<FName, FString> VisualizationData;

	if (!CurrentRuleSet)
	{
		VisualizationData.Add(TEXT("Error"), TEXT("No rule set assigned"));
		return VisualizationData;
	}

	// Create context for visualization
	FPropagationContext Context = CreatePropagationContext(CellPosition, 0.0f);
	Context.bIsPreview = true;

	// Check each rule
	TArray<UPropagationRuleAsset*> ApplicableRules = CurrentRuleSet->GetApplicableRules(Context);
	
	VisualizationData.Add(TEXT("CellPosition"), CellPosition.ToString());
	VisualizationData.Add(TEXT("ApplicableRules"), FString::FromInt(ApplicableRules.Num()));

	for (int32 i = 0; i < ApplicableRules.Num(); i++)
	{
		UPropagationRuleAsset* Rule = ApplicableRules[i];
		if (Rule)
		{
			FString RuleKey = FString::Printf(TEXT("Rule_%d"), i);
			FString RuleInfo = FString::Printf(TEXT("%s (Priority: %d, CanExecute: %s)"), 
				*Rule->RuleName.ToString(), 
				Rule->GetPriorityValue(),
				Rule->ShouldExecute(Context) ? TEXT("Yes") : TEXT("No"));
			
			VisualizationData.Add(*RuleKey, RuleInfo);
		}
	}

	return VisualizationData;
}
#endif
