/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.38.33130/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.22621.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.22621.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
/NATVIS:"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/Niagara.natvis"
"E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/Build/Win64/x64/HorizonBrigadeEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/Module.SpatialFusionRuntime.1.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/Module.SpatialFusionRuntime.2.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/Module.SpatialFusionRuntime.3.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/Module.SpatialFusionRuntime.4.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/InfluenceMapManager.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/InfluenceMapManager2D.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/InfluenceMapManager3D.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/InfluenceMapManagerBase.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationAsyncTask.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationRuleAsset.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationRuleEngine.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationRuleExamples.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationRuleSet.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationRuleTypes.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/PropagationStrategy.cpp.obj"
"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Intermediate/Build/Win64/x64/UnrealEditor/Development/SpatialFusionRuntime/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/UnrealEditor-Niagara.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Binaries/Win64/UnrealEditor-SpatialFusionRuntime.dll"
/PDB:"E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/SpatialFusion/Binaries/Win64/UnrealEditor-SpatialFusionRuntime.pdb"
/ignore:4078