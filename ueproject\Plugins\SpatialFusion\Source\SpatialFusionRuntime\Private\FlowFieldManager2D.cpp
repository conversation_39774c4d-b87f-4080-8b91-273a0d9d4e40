// Copyright Epic Games, Inc. All Rights Reserved.

#include "FlowFieldManager2D.h"
#include "FlowFieldAlgorithm.h"
#include "InfluenceMapManagerBase.h"

UFlowFieldManager2D::UFlowFieldManager2D()
{
	GridDimensions2D = FVector2D::ZeroVector;
}

FFlowFieldResult UFlowFieldManager2D::CalculateFlowFieldInternal(const FFlowFieldQuery& Query)
{
	if (!GridManager)
	{
		FFlowFieldResult Result;
		Result.bSuccess = false;
		Result.ErrorMessage = TEXT("No grid manager set");
		return Result;
	}

	if (GridManager->Is3DGrid())
	{
		FFlowFieldResult Result;
		Result.bSuccess = false;
		Result.ErrorMessage = TEXT("2D Flow Field Manager cannot work with 3D grids");
		return Result;
	}

	// Update grid dimensions
	FVector GridDims = GridManager->GetGridDimensions();
	GridDimensions2D = FVector2D(GridDims.X, GridDims.Y);

	// Get algorithm
	UFlowFieldAlgorithmRegistry* Registry = UFlowFieldAlgorithmRegistry::GetGlobalRegistry();
	UFlowFieldAlgorithm* Algorithm = Registry->GetAlgorithm(Query.Params.Method);
	
	if (!Algorithm)
	{
		FFlowFieldResult Result;
		Result.bSuccess = false;
		Result.ErrorMessage = FString::Printf(TEXT("Algorithm not found for method: %d"), (int32)Query.Params.Method);
		return Result;
	}

	// Calculate flow field
	FFlowFieldResult Result = Algorithm->CalculateFlowField(GridManager, Query, FlowField2D);

	// Update performance metrics
	if (Result.bSuccess)
	{
		UpdatePerformanceMetrics2D();
	}

	return Result;
}

FVector2D UFlowFieldManager2D::GetFlowDirection2D(const FVector2D& Position2D) const
{
	if (!IsValidPosition2D(Position2D))
	{
		return FVector2D::ZeroVector;
	}

	// Use interpolation for smooth flow directions
	return InterpolateFlowDirection2D(Position2D);
}

float UFlowFieldManager2D::GetDistance2D(const FVector2D& Position2D) const
{
	int32 Index = Position2DToIndex(Position2D);
	if (Index >= 0 && Index < FlowField2D.Num())
	{
		return FlowField2D[Index].Distance;
	}
	return FLT_MAX;
}

bool UFlowFieldManager2D::AddGoal2D(const FVector2D& Position2D, float Weight, float Radius, const FGameplayTag& GoalTag)
{
	if (!IsValidPosition2D(Position2D))
	{
		return false;
	}

	FFlowFieldGoal Goal;
	Goal.WorldPosition = FVector(Position2D.X, Position2D.Y, 0.0f);
	Goal.GridPosition = Goal.WorldPosition; // Assume world == grid for now
	Goal.Weight = Weight;
	Goal.Radius = Radius;
	Goal.GoalTag = GoalTag;
	Goal.bIsActive = true;

	return AddGoal(Goal);
}

TArray<TArray<FVector2D>> UFlowFieldManager2D::GetFlowField2D() const
{
	TArray<TArray<FVector2D>> FlowField2DArray;
	
	int32 Width = (int32)GridDimensions2D.X;
	int32 Height = (int32)GridDimensions2D.Y;
	
	FlowField2DArray.SetNum(Height);
	for (int32 Y = 0; Y < Height; Y++)
	{
		FlowField2DArray[Y].SetNum(Width);
		for (int32 X = 0; X < Width; X++)
		{
			int32 Index = Y * Width + X;
			if (Index < FlowField2D.Num())
			{
				FVector FlowDir = FlowField2D[Index].FlowDirection;
				FlowField2DArray[Y][X] = FVector2D(FlowDir.X, FlowDir.Y);
			}
			else
			{
				FlowField2DArray[Y][X] = FVector2D::ZeroVector;
			}
		}
	}
	
	return FlowField2DArray;
}

TArray<TArray<float>> UFlowFieldManager2D::GetDistanceField2D() const
{
	TArray<TArray<float>> DistanceField2DArray;
	
	int32 Width = (int32)GridDimensions2D.X;
	int32 Height = (int32)GridDimensions2D.Y;
	
	DistanceField2DArray.SetNum(Height);
	for (int32 Y = 0; Y < Height; Y++)
	{
		DistanceField2DArray[Y].SetNum(Width);
		for (int32 X = 0; X < Width; X++)
		{
			int32 Index = Y * Width + X;
			if (Index < FlowField2D.Num())
			{
				DistanceField2DArray[Y][X] = FlowField2D[Index].Distance;
			}
			else
			{
				DistanceField2DArray[Y][X] = FLT_MAX;
			}
		}
	}
	
	return DistanceField2DArray;
}

TArray<FVector2D> UFlowFieldManager2D::CalculatePath2D(const FVector2D& StartPosition2D, float MaxPathLength) const
{
	TArray<FVector2D> Path;
	
	if (!IsValidPosition2D(StartPosition2D))
	{
		return Path;
	}

	FVector2D CurrentPos = StartPosition2D;
	float TotalDistance = 0.0f;
	int32 MaxSteps = 1000; // Prevent infinite loops
	
	for (int32 Step = 0; Step < MaxSteps; Step++)
	{
		Path.Add(CurrentPos);
		
		// Get flow direction at current position
		FVector2D FlowDir = GetFlowDirection2D(CurrentPos);
		
		// Check if we've reached a goal or have no valid direction
		if (FlowDir.IsNearlyZero() || GetDistance2D(CurrentPos) < 1.0f)
		{
			break;
		}
		
		// Move along flow direction
		FVector2D NextPos = CurrentPos + FlowDir;
		float StepDistance = FVector2D::Distance(CurrentPos, NextPos);
		TotalDistance += StepDistance;
		
		// Check distance limit
		if (TotalDistance > MaxPathLength)
		{
			break;
		}
		
		CurrentPos = NextPos;
		
		// Check if we've left the valid area
		if (!IsValidPosition2D(CurrentPos))
		{
			break;
		}
	}
	
	return Path;
}

TArray<FVector2D> UFlowFieldManager2D::GetPositionsWithinDistance2D(float MaxDistance) const
{
	TArray<FVector2D> Positions;
	
	int32 Width = (int32)GridDimensions2D.X;
	int32 Height = (int32)GridDimensions2D.Y;
	
	for (int32 Y = 0; Y < Height; Y++)
	{
		for (int32 X = 0; X < Width; X++)
		{
			int32 Index = Y * Width + X;
			if (Index < FlowField2D.Num() && FlowField2D[Index].Distance <= MaxDistance)
			{
				Positions.Add(FVector2D(X, Y));
			}
		}
	}
	
	return Positions;
}

TArray<FVector2D> UFlowFieldManager2D::SampleFlowField2D(const TArray<FVector2D>& Positions) const
{
	TArray<FVector2D> FlowDirections;
	FlowDirections.Reserve(Positions.Num());
	
	for (const FVector2D& Position : Positions)
	{
		FlowDirections.Add(GetFlowDirection2D(Position));
	}
	
	return FlowDirections;
}

FVector2D UFlowFieldManager2D::GetFlowGradient2D(const FVector2D& Position2D) const
{
	if (!IsValidPosition2D(Position2D))
	{
		return FVector2D::ZeroVector;
	}

	// Calculate gradient using finite differences
	float CenterDistance = GetDistance2D(Position2D);
	float RightDistance = GetDistance2D(Position2D + FVector2D(1, 0));
	float LeftDistance = GetDistance2D(Position2D + FVector2D(-1, 0));
	float UpDistance = GetDistance2D(Position2D + FVector2D(0, 1));
	float DownDistance = GetDistance2D(Position2D + FVector2D(0, -1));

	float GradientX = (RightDistance - LeftDistance) / 2.0f;
	float GradientY = (UpDistance - DownDistance) / 2.0f;

	return FVector2D(GradientX, GradientY);
}

bool UFlowFieldManager2D::HasClearPath2D(const FVector2D& StartPosition2D, float MaxDistance) const
{
	if (!IsValidPosition2D(StartPosition2D))
	{
		return false;
	}

	float DistanceToGoal = GetDistance2D(StartPosition2D);
	return DistanceToGoal < FLT_MAX && DistanceToGoal <= MaxDistance;
}

FVector2D UFlowFieldManager2D::GetGroupMovementDirection2D(const TArray<FVector2D>& GroupPositions, float GroupRadius) const
{
	if (GroupPositions.Num() == 0)
	{
		return FVector2D::ZeroVector;
	}

	// Calculate group center
	FVector2D GroupCenter = FVector2D::ZeroVector;
	for (const FVector2D& Position : GroupPositions)
	{
		GroupCenter += Position;
	}
	GroupCenter /= GroupPositions.Num();

	// Sample flow field around group center
	TArray<FVector2D> SamplePositions;
	int32 NumSamples = 8;
	for (int32 i = 0; i < NumSamples; i++)
	{
		float Angle = (2.0f * PI * i) / NumSamples;
		FVector2D SamplePos = GroupCenter + FVector2D(FMath::Cos(Angle), FMath::Sin(Angle)) * GroupRadius;
		SamplePositions.Add(SamplePos);
	}

	// Average flow directions
	FVector2D AverageFlow = FVector2D::ZeroVector;
	int32 ValidSamples = 0;

	for (const FVector2D& SamplePos : SamplePositions)
	{
		FVector2D FlowDir = GetFlowDirection2D(SamplePos);
		if (!FlowDir.IsNearlyZero())
		{
			AverageFlow += FlowDir;
			ValidSamples++;
		}
	}

	if (ValidSamples > 0)
	{
		AverageFlow /= ValidSamples;
		AverageFlow.Normalize();
	}

	return AverageFlow;
}

int32 UFlowFieldManager2D::SetMovementCostInArea2D(const FVector2D& MinPosition, const FVector2D& MaxPosition, float Cost)
{
	if (!GridManager)
	{
		return 0;
	}

	int32 CellsAffected = 0;
	int32 MinX = FMath::FloorToInt(FMath::Min(MinPosition.X, MaxPosition.X));
	int32 MaxX = FMath::FloorToInt(FMath::Max(MinPosition.X, MaxPosition.X));
	int32 MinY = FMath::FloorToInt(FMath::Min(MinPosition.Y, MaxPosition.Y));
	int32 MaxY = FMath::FloorToInt(FMath::Max(MinPosition.Y, MaxPosition.Y));

	for (int32 Y = MinY; Y <= MaxY; Y++)
	{
		for (int32 X = MinX; X <= MaxX; X++)
		{
			FVector2D Position(X, Y);
			if (IsValidPosition2D(Position))
			{
				FVector WorldPos(X, Y, 0);
				if (GridManager->SetCellValueAtWorldPosition(WorldPos, TEXT("MovementCost"), Cost))
				{
					CellsAffected++;
				}
			}
		}
	}

	// Invalidate flow field if costs changed
	if (CellsAffected > 0)
	{
		InvalidateFlowField();
	}

	return CellsAffected;
}

int32 UFlowFieldManager2D::SetBlockedInCircle2D(const FVector2D& Center, float Radius, bool bBlocked)
{
	if (!GridManager)
	{
		return 0;
	}

	int32 CellsAffected = 0;
	int32 IntRadius = FMath::CeilToInt(Radius);

	for (int32 Y = -IntRadius; Y <= IntRadius; Y++)
	{
		for (int32 X = -IntRadius; X <= IntRadius; X++)
		{
			FVector2D Offset(X, Y);
			if (Offset.Size() <= Radius)
			{
				FVector2D Position = Center + Offset;
				if (IsValidPosition2D(Position))
				{
					FVector WorldPos(Position.X, Position.Y, 0);
					if (GridManager->SetCellBlockedAtWorldPosition(WorldPos, bBlocked))
					{
						CellsAffected++;
					}
				}
			}
		}
	}

	// Invalidate flow field if blocking changed
	if (CellsAffected > 0)
	{
		InvalidateFlowField();
	}

	return CellsAffected;
}

TMap<FName, float> UFlowFieldManager2D::GetFlowFieldStats2D() const
{
	TMap<FName, float> Stats;

	if (FlowField2D.Num() == 0)
	{
		return Stats;
	}

	// Calculate statistics
	int32 ValidCells = 0;
	int32 BlockedCells = 0;
	int32 GoalCells = 0;
	float TotalDistance = 0.0f;
	float MaxDistance = 0.0f;
	float MinDistance = FLT_MAX;

	for (const FFlowFieldCell& Cell : FlowField2D)
	{
		if (Cell.bIsBlocked)
		{
			BlockedCells++;
		}
		else if (Cell.bIsGoal)
		{
			GoalCells++;
		}
		else if (Cell.Distance < FLT_MAX)
		{
			ValidCells++;
			TotalDistance += Cell.Distance;
			MaxDistance = FMath::Max(MaxDistance, Cell.Distance);
			MinDistance = FMath::Min(MinDistance, Cell.Distance);
		}
	}

	Stats.Add(TEXT("TotalCells"), FlowField2D.Num());
	Stats.Add(TEXT("ValidCells"), ValidCells);
	Stats.Add(TEXT("BlockedCells"), BlockedCells);
	Stats.Add(TEXT("GoalCells"), GoalCells);
	Stats.Add(TEXT("AverageDistance"), ValidCells > 0 ? TotalDistance / ValidCells : 0.0f);
	Stats.Add(TEXT("MaxDistance"), MaxDistance);
	Stats.Add(TEXT("MinDistance"), MinDistance == FLT_MAX ? 0.0f : MinDistance);
	Stats.Add(TEXT("Coverage"), FlowField2D.Num() > 0 ? (float)ValidCells / FlowField2D.Num() : 0.0f);

	return Stats;
}

int32 UFlowFieldManager2D::Position2DToIndex(const FVector2D& Position2D) const
{
	int32 X = FMath::FloorToInt(Position2D.X);
	int32 Y = FMath::FloorToInt(Position2D.Y);

	if (X < 0 || X >= GridDimensions2D.X || Y < 0 || Y >= GridDimensions2D.Y)
	{
		return -1;
	}

	return Y * (int32)GridDimensions2D.X + X;
}

FVector2D UFlowFieldManager2D::IndexToPosition2D(int32 Index) const
{
	if (Index < 0 || GridDimensions2D.X <= 0)
	{
		return FVector2D(-1, -1);
	}

	int32 Width = (int32)GridDimensions2D.X;
	int32 X = Index % Width;
	int32 Y = Index / Width;

	return FVector2D(X, Y);
}

bool UFlowFieldManager2D::IsValidPosition2D(const FVector2D& Position2D) const
{
	return Position2D.X >= 0 && Position2D.X < GridDimensions2D.X &&
		   Position2D.Y >= 0 && Position2D.Y < GridDimensions2D.Y;
}

TArray<FVector2D> UFlowFieldManager2D::GetNeighbors2D(const FVector2D& Position2D, bool bIncludeDiagonals) const
{
	TArray<FVector2D> Neighbors;

	// Cardinal directions
	TArray<FVector2D> Offsets = {
		FVector2D(1, 0), FVector2D(-1, 0),
		FVector2D(0, 1), FVector2D(0, -1)
	};

	// Add diagonals if requested
	if (bIncludeDiagonals)
	{
		Offsets.Append({
			FVector2D(1, 1), FVector2D(1, -1),
			FVector2D(-1, 1), FVector2D(-1, -1)
		});
	}

	for (const FVector2D& Offset : Offsets)
	{
		FVector2D NeighborPos = Position2D + Offset;
		if (IsValidPosition2D(NeighborPos))
		{
			Neighbors.Add(NeighborPos);
		}
	}

	return Neighbors;
}

FVector2D UFlowFieldManager2D::InterpolateFlowDirection2D(const FVector2D& Position2D) const
{
	// Get the four surrounding grid cells
	int32 X0 = FMath::FloorToInt(Position2D.X);
	int32 Y0 = FMath::FloorToInt(Position2D.Y);
	int32 X1 = X0 + 1;
	int32 Y1 = Y0 + 1;

	// Calculate interpolation weights
	float FracX = Position2D.X - X0;
	float FracY = Position2D.Y - Y0;

	// Get flow directions at grid corners
	FVector2D Flow00 = FVector2D::ZeroVector;
	FVector2D Flow10 = FVector2D::ZeroVector;
	FVector2D Flow01 = FVector2D::ZeroVector;
	FVector2D Flow11 = FVector2D::ZeroVector;

	int32 Index00 = Position2DToIndex(FVector2D(X0, Y0));
	int32 Index10 = Position2DToIndex(FVector2D(X1, Y0));
	int32 Index01 = Position2DToIndex(FVector2D(X0, Y1));
	int32 Index11 = Position2DToIndex(FVector2D(X1, Y1));

	if (Index00 >= 0 && Index00 < FlowField2D.Num())
	{
		FVector Flow = FlowField2D[Index00].FlowDirection;
		Flow00 = FVector2D(Flow.X, Flow.Y);
	}
	if (Index10 >= 0 && Index10 < FlowField2D.Num())
	{
		FVector Flow = FlowField2D[Index10].FlowDirection;
		Flow10 = FVector2D(Flow.X, Flow.Y);
	}
	if (Index01 >= 0 && Index01 < FlowField2D.Num())
	{
		FVector Flow = FlowField2D[Index01].FlowDirection;
		Flow01 = FVector2D(Flow.X, Flow.Y);
	}
	if (Index11 >= 0 && Index11 < FlowField2D.Num())
	{
		FVector Flow = FlowField2D[Index11].FlowDirection;
		Flow11 = FVector2D(Flow.X, Flow.Y);
	}

	// Bilinear interpolation
	FVector2D FlowX0 = FMath::Lerp(Flow00, Flow10, FracX);
	FVector2D FlowX1 = FMath::Lerp(Flow01, Flow11, FracX);
	FVector2D InterpolatedFlow = FMath::Lerp(FlowX0, FlowX1, FracY);

	return InterpolatedFlow.GetSafeNormal();
}

void UFlowFieldManager2D::UpdatePerformanceMetrics2D()
{
	// Update base performance metrics
	UpdatePerformanceMetrics(LastResult);

	// Add 2D-specific metrics
	PerformanceMetrics.Add(TEXT("GridWidth"), GridDimensions2D.X);
	PerformanceMetrics.Add(TEXT("GridHeight"), GridDimensions2D.Y);
	PerformanceMetrics.Add(TEXT("FlowFieldSize"), FlowField2D.Num());
}
