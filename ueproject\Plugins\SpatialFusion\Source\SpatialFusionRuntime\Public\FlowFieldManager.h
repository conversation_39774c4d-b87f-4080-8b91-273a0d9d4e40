// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "FlowFieldTypes.h"
#include "InfluenceMapManagerBase.h"
#include "FlowFieldManager.generated.h"

// Forward declarations
template<typename TCoordinate> class TInfluenceMapManager;
class UFlowFieldAlgorithm;

/**
 * Abstract base class for flow field management
 * Provides unified interface for 2D and 3D flow field calculations
 */
UCLASS(Abstract, BlueprintType)
class SPATIALFUSIONRUNTIME_API UFlowFieldManagerBase : public UObject
{
	GENERATED_BODY()

public:
	UFlowFieldManagerBase();

	/**
	 * Initialize the flow field manager with a grid
	 * @param GridManager - Grid manager to use for calculations
	 * @return True if initialization was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool InitializeWithGrid(UInfluenceMapManagerBase* GridManager);

	/**
	 * Calculate flow field for given goals
	 * @param Query - Flow field query with goals and parameters
	 * @return Calculation result
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual FFlowFieldResult CalculateFlowField(const FFlowFieldQuery& Query);

	/**
	 * Get flow direction at world position
	 * @param WorldPosition - Position to query
	 * @return Flow direction vector (normalized)
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual FVector GetFlowDirectionAtPosition(const FVector& WorldPosition) const;

	/**
	 * Get distance to nearest goal at world position
	 * @param WorldPosition - Position to query
	 * @return Distance to nearest goal
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual float GetDistanceAtPosition(const FVector& WorldPosition) const;

	/**
	 * Add a goal to the flow field
	 * @param Goal - Goal to add
	 * @return True if goal was added successfully
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool AddGoal(const FFlowFieldGoal& Goal);

	/**
	 * Remove a goal by tag
	 * @param GoalTag - Tag of goal to remove
	 * @return True if goal was removed
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool RemoveGoal(const FGameplayTag& GoalTag);

	/**
	 * Clear all goals
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual void ClearGoals();

	/**
	 * Update flow field (called automatically based on update frequency)
	 * @param DeltaTime - Time since last update
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual void UpdateFlowField(float DeltaTime);

	/**
	 * Force immediate recalculation of flow field
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual void ForceRecalculation();

	/**
	 * Set flow field parameters
	 * @param NewParams - New parameters to use
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual void SetFlowFieldParams(const FFlowFieldParams& NewParams);

	/**
	 * Get current flow field parameters
	 * @return Current parameters
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual FFlowFieldParams GetFlowFieldParams() const;

	/**
	 * Check if flow field is valid and up-to-date
	 * @return True if flow field is valid
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool IsFlowFieldValid() const;

	/**
	 * Get all current goals
	 * @return Array of current goals
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual TArray<FFlowFieldGoal> GetCurrentGoals() const;

	/**
	 * Set movement cost at world position
	 * @param WorldPosition - Position to modify
	 * @param Cost - New movement cost
	 * @return True if cost was set successfully
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool SetMovementCostAtPosition(const FVector& WorldPosition, float Cost);

	/**
	 * Get movement cost at world position
	 * @param WorldPosition - Position to query
	 * @return Movement cost at position
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual float GetMovementCostAtPosition(const FVector& WorldPosition) const;

	/**
	 * Set blocked state at world position
	 * @param WorldPosition - Position to modify
	 * @param bBlocked - Whether position should be blocked
	 * @return True if state was set successfully
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool SetBlockedAtPosition(const FVector& WorldPosition, bool bBlocked);

	/**
	 * Check if position is blocked
	 * @param WorldPosition - Position to check
	 * @return True if position is blocked
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool IsPositionBlocked(const FVector& WorldPosition) const;

	/**
	 * Get flow field data for visualization
	 * @return Array of flow field cells
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual TArray<FFlowFieldCell> GetFlowFieldData() const;

	/**
	 * Get performance statistics
	 * @return Map of performance metrics
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual TMap<FName, float> GetPerformanceStats() const;

	/**
	 * Enable or disable flow field updates
	 * @param bEnabled - Whether updates should be enabled
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual void SetUpdateEnabled(bool bEnabled);

	/**
	 * Check if updates are enabled
	 * @return True if updates are enabled
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	virtual bool IsUpdateEnabled() const;

	// Events
	
	/** Called when flow field calculation completes */
	UPROPERTY(BlueprintAssignable, Category = "Flow Field Events")
	FOnFlowFieldCalculated OnFlowFieldCalculated;

	/** Called when goals change */
	UPROPERTY(BlueprintAssignable, Category = "Flow Field Events")
	FOnFlowFieldGoalsChanged OnGoalsChanged;

	/** Called when flow field becomes invalid */
	UPROPERTY(BlueprintAssignable, Category = "Flow Field Events")
	FOnFlowFieldInvalidated OnFlowFieldInvalidated;

protected:
	/** Current flow field parameters */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	FFlowFieldParams CurrentParams;

	/** Current goals */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	TArray<FFlowFieldGoal> CurrentGoals;

	/** Whether flow field is currently valid */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	bool bIsFlowFieldValid = false;

	/** Whether updates are enabled */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	bool bUpdateEnabled = true;

	/** Time since last update */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	float TimeSinceLastUpdate = 0.0f;

	/** Last calculation result */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	FFlowFieldResult LastResult;

	/** Performance metrics */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field")
	TMap<FName, float> PerformanceMetrics;

	/** Reference to the grid manager */
	UPROPERTY()
	TObjectPtr<UInfluenceMapManagerBase> GridManager;

	/**
	 * Internal calculation implementation (to be overridden by derived classes)
	 * @param Query - Flow field query
	 * @return Calculation result
	 */
	virtual FFlowFieldResult CalculateFlowFieldInternal(const FFlowFieldQuery& Query) { return FFlowFieldResult(); }

	/**
	 * Check if recalculation is needed
	 * @return True if recalculation is needed
	 */
	virtual bool ShouldRecalculate() const;

	/**
	 * Update performance metrics
	 * @param Result - Calculation result to update metrics from
	 */
	virtual void UpdatePerformanceMetrics(const FFlowFieldResult& Result);

	/**
	 * Invalidate current flow field
	 */
	virtual void InvalidateFlowField();
};

// Event delegate declarations
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFlowFieldCalculated, const FFlowFieldResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnFlowFieldGoalsChanged);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnFlowFieldInvalidated);
