{"Version": "1.2", "Data": {"Source": "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\private\\propagationruleengine.cpp", "ProvidedModule": "", "PCH": "e:\\unreal projects\\horizonbrigade\\ueproject\\intermediate\\build\\win64\\x64\\horizonbrigadeeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\spatialfusionruntime\\definitions.spatialfusionruntime.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\propagationruleengine.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\propagationruleset.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\propagationruleasset.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\propagationruletypes.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\spatialfusiontypes.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\spatialfusiontypes.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\propagationruletypes.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\propagationruleasset.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\propagationruleset.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\propagationruleengine.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\influencemapmanagerbase.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\propagationstrategy.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\propagationstrategy.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\influencemapmanagerbase.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}