// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "InfluenceMapTypes.h"
#include "InfluenceGrid2D.h"
#include "InfluenceGrid3D.h"
#include "InfluenceMapManager.h"

/**
 * Test suite for core influence map data structures
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FInfluenceMapCoreTest, "SpatialFusion.Core.InfluenceMapCore", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FInfluenceMapCoreTest::RunTest(const FString& Parameters)
{
	// Test FInfluenceCell basic functionality
	{
		FInfluenceCell Cell;
		TestEqual("Default cell value should be 0", Cell.GetValue(TEXT("Default")), 0.0f);
		
		Cell.SetValue(TEXT("Test"), 42.0f);
		TestEqual("Set value should be retrievable", Cell.GetValue(TEXT("Test")), 42.0f);
		
		Cell.AddValue(TEXT("Test"), 8.0f);
		TestEqual("Add value should accumulate", Cell.GetValue(TEXT("Test")), 50.0f);
		
		Cell.MultiplyValue(TEXT("Test"), 2.0f);
		TestEqual("Multiply value should scale", Cell.GetValue(TEXT("Test")), 100.0f);
		
		Cell.SetBlocked(true);
		TestTrue("Cell should be blocked", Cell.IsBlocked());
		
		FGameplayTag TestTag = FGameplayTag::RequestGameplayTag(TEXT("Test.Tag"));
		Cell.AddTag(TestTag);
		TestTrue("Cell should have tag", Cell.HasTag(TestTag));
		
		Cell.RemoveTag(TestTag);
		TestFalse("Cell should not have tag after removal", Cell.HasTag(TestTag));
	}

	// Test FInfluenceGrid2D functionality
	{
		FInfluenceGrid2D Grid(10, 10, 100.0f);
		TestEqual("Grid width should be 10", Grid.GetWidth(), 10);
		TestEqual("Grid height should be 10", Grid.GetHeight(), 10);
		TestEqual("Grid cell size should be 100", Grid.GetCellSize(), 100.0f);
		
		// Test coordinate conversion
		FVector2D WorldPos(250.0f, 350.0f);
		FIntPoint GridPos = Grid.WorldToGrid(WorldPos);
		TestEqual("World to grid X should be 2", GridPos.X, 2);
		TestEqual("World to grid Y should be 3", GridPos.Y, 3);
		
		FVector2D ConvertedBack = Grid.GridToWorld(GridPos);
		TestEqual("Grid to world X should be 200", ConvertedBack.X, 200.0f);
		TestEqual("Grid to world Y should be 300", ConvertedBack.Y, 300.0f);
		
		// Test bounds checking
		TestTrue("Position (5,5) should be valid", Grid.IsValidPosition(FIntPoint(5, 5)));
		TestFalse("Position (-1,5) should be invalid", Grid.IsValidPosition(FIntPoint(-1, 5)));
		TestFalse("Position (10,5) should be invalid", Grid.IsValidPosition(FIntPoint(10, 5)));
		
		// Test cell access
		FInfluenceCell* Cell = Grid.GetCell(FIntPoint(5, 5));
		TestNotNull("Cell should not be null", Cell);
		
		Cell->SetValue(TEXT("Test"), 123.0f);
		TestEqual("Cell value should be set", Grid.GetCellValue(FIntPoint(5, 5), TEXT("Test")), 123.0f);
		
		Grid.SetCellValue(FIntPoint(3, 3), TEXT("Test"), 456.0f);
		TestEqual("Grid set value should work", Grid.GetCellValue(FIntPoint(3, 3), TEXT("Test")), 456.0f);
	}

	// Test FInfluenceGrid3D functionality
	{
		FInfluenceGrid3D Grid(5, 5, 5, 50.0f);
		TestEqual("3D Grid width should be 5", Grid.GetWidth(), 5);
		TestEqual("3D Grid height should be 5", Grid.GetHeight(), 5);
		TestEqual("3D Grid depth should be 5", Grid.GetDepth(), 5);
		
		// Test 3D coordinate conversion
		FVector WorldPos(125.0f, 175.0f, 225.0f);
		FIntVector GridPos = Grid.WorldToGrid(WorldPos);
		TestEqual("3D World to grid X should be 2", GridPos.X, 2);
		TestEqual("3D World to grid Y should be 3", GridPos.Y, 3);
		TestEqual("3D World to grid Z should be 4", GridPos.Z, 4);
		
		FVector ConvertedBack = Grid.GridToWorld(GridPos);
		TestEqual("3D Grid to world X should be 100", ConvertedBack.X, 100.0f);
		TestEqual("3D Grid to world Y should be 150", ConvertedBack.Y, 150.0f);
		TestEqual("3D Grid to world Z should be 200", ConvertedBack.Z, 200.0f);
		
		// Test 3D bounds checking
		TestTrue("3D Position (2,2,2) should be valid", Grid.IsValidPosition(FIntVector(2, 2, 2)));
		TestFalse("3D Position (5,2,2) should be invalid", Grid.IsValidPosition(FIntVector(5, 2, 2)));
		
		// Test 3D cell access
		FInfluenceCell* Cell = Grid.GetCell(FIntVector(2, 2, 2));
		TestNotNull("3D Cell should not be null", Cell);
		
		Cell->SetValue(TEXT("Test3D"), 789.0f);
		TestEqual("3D Cell value should be set", Grid.GetCellValue(FIntVector(2, 2, 2), TEXT("Test3D")), 789.0f);
	}

	return true;
}

/**
 * Test suite for influence map manager template functionality
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FInfluenceMapManagerTest, "SpatialFusion.Core.InfluenceMapManager", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FInfluenceMapManagerTest::RunTest(const FString& Parameters)
{
	// Test 2D manager
	{
		TInfluenceMapManager<FVector2D> Manager2D;
		Manager2D.Initialize(FVector2D(10, 10), 100.0f, FVector2D::ZeroVector);
		
		TestTrue("2D Manager should be initialized", Manager2D.IsInitialized());
		TestEqual("2D Manager grid size X should be 10", Manager2D.GetGridDimensions().X, 10.0f);
		TestEqual("2D Manager grid size Y should be 10", Manager2D.GetGridDimensions().Y, 10.0f);
		
		// Test value operations
		FVector2D TestPos(250.0f, 350.0f);
		Manager2D.SetCellValueAtWorldPosition(TestPos, TEXT("Test"), 100.0f);
		TestEqual("2D Manager should set value", Manager2D.GetCellValueAtWorldPosition(TestPos, TEXT("Test")), 100.0f);
		
		Manager2D.AddCellValueAtWorldPosition(TestPos, TEXT("Test"), 50.0f);
		TestEqual("2D Manager should add value", Manager2D.GetCellValueAtWorldPosition(TestPos, TEXT("Test")), 150.0f);
		
		// Test propagation
		FInfluencePropagationParams PropParams;
		PropParams.SourcePosition = TestPos;
		PropParams.ValueKey = TEXT("Test");
		PropParams.Strength = 200.0f;
		PropParams.Radius = 300.0f;
		PropParams.Falloff = EInfluenceFalloff::Linear;
		
		Manager2D.PropagateInfluence(PropParams);
		
		// Check that influence propagated to nearby cells
		FVector2D NearbyPos(350.0f, 350.0f); // 100 units away
		float NearbyValue = Manager2D.GetCellValueAtWorldPosition(NearbyPos, TEXT("Test"));
		TestTrue("Nearby cell should have influence", NearbyValue > 0.0f);
		TestTrue("Nearby cell should have less influence than source", NearbyValue < 200.0f);
	}

	// Test 3D manager
	{
		TInfluenceMapManager<FVector> Manager3D;
		Manager3D.Initialize(FVector(5, 5, 5), 50.0f, FVector::ZeroVector);
		
		TestTrue("3D Manager should be initialized", Manager3D.IsInitialized());
		TestEqual("3D Manager grid size Z should be 5", Manager3D.GetGridDimensions().Z, 5.0f);
		
		// Test 3D propagation
		FVector TestPos3D(125.0f, 125.0f, 125.0f);
		FInfluencePropagationParams PropParams3D;
		PropParams3D.SourcePosition = TestPos3D;
		PropParams3D.ValueKey = TEXT("Test3D");
		PropParams3D.Strength = 100.0f;
		PropParams3D.Radius = 150.0f;
		PropParams3D.Falloff = EInfluenceFalloff::Exponential;
		
		Manager3D.PropagateInfluence(PropParams3D);
		
		float SourceValue = Manager3D.GetCellValueAtWorldPosition(TestPos3D, TEXT("Test3D"));
		TestTrue("3D Source should have influence", SourceValue > 0.0f);
		
		FVector NearbyPos3D(175.0f, 125.0f, 125.0f); // 50 units away in X
		float NearbyValue3D = Manager3D.GetCellValueAtWorldPosition(NearbyPos3D, TEXT("Test3D"));
		TestTrue("3D Nearby cell should have influence", NearbyValue3D > 0.0f);
		TestTrue("3D Nearby cell should have less influence than source", NearbyValue3D < SourceValue);
	}

	return true;
}

/**
 * Test suite for influence propagation algorithms
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FInfluencePropagationTest, "SpatialFusion.Core.InfluencePropagation", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FInfluencePropagationTest::RunTest(const FString& Parameters)
{
	TInfluenceMapManager<FVector2D> Manager;
	Manager.Initialize(FVector2D(20, 20), 50.0f, FVector2D::ZeroVector);

	// Test different falloff types
	{
		FVector2D SourcePos(500.0f, 500.0f); // Center of 20x20 grid with 50 unit cells
		
		// Test Linear falloff
		FInfluencePropagationParams LinearParams;
		LinearParams.SourcePosition = SourcePos;
		LinearParams.ValueKey = TEXT("Linear");
		LinearParams.Strength = 100.0f;
		LinearParams.Radius = 200.0f;
		LinearParams.Falloff = EInfluenceFalloff::Linear;
		
		Manager.PropagateInfluence(LinearParams);
		
		float SourceValue = Manager.GetCellValueAtWorldPosition(SourcePos, TEXT("Linear"));
		float HalfwayValue = Manager.GetCellValueAtWorldPosition(SourcePos + FVector2D(100.0f, 0.0f), TEXT("Linear"));
		float EdgeValue = Manager.GetCellValueAtWorldPosition(SourcePos + FVector2D(200.0f, 0.0f), TEXT("Linear"));
		
		TestTrue("Linear: Source should have max value", SourceValue > 90.0f);
		TestTrue("Linear: Halfway should have medium value", HalfwayValue > 40.0f && HalfwayValue < 60.0f);
		TestTrue("Linear: Edge should have minimal value", EdgeValue < 10.0f);
		
		// Test Exponential falloff
		Manager.ClearAllValues(); // Clear previous test
		
		FInfluencePropagationParams ExpParams;
		ExpParams.SourcePosition = SourcePos;
		ExpParams.ValueKey = TEXT("Exponential");
		ExpParams.Strength = 100.0f;
		ExpParams.Radius = 200.0f;
		ExpParams.Falloff = EInfluenceFalloff::Exponential;
		
		Manager.PropagateInfluence(ExpParams);
		
		float ExpSourceValue = Manager.GetCellValueAtWorldPosition(SourcePos, TEXT("Exponential"));
		float ExpHalfwayValue = Manager.GetCellValueAtWorldPosition(SourcePos + FVector2D(100.0f, 0.0f), TEXT("Exponential"));
		
		TestTrue("Exponential: Source should have max value", ExpSourceValue > 90.0f);
		TestTrue("Exponential: Halfway should have lower value than linear", ExpHalfwayValue < HalfwayValue);
	}

	// Test multiple sources
	{
		Manager.ClearAllValues();
		
		FVector2D Source1(300.0f, 300.0f);
		FVector2D Source2(700.0f, 700.0f);
		
		FInfluencePropagationParams Params1;
		Params1.SourcePosition = Source1;
		Params1.ValueKey = TEXT("Multi");
		Params1.Strength = 50.0f;
		Params1.Radius = 150.0f;
		Params1.Falloff = EInfluenceFalloff::Linear;
		
		FInfluencePropagationParams Params2;
		Params2.SourcePosition = Source2;
		Params2.ValueKey = TEXT("Multi");
		Params2.Strength = 50.0f;
		Params2.Radius = 150.0f;
		Params2.Falloff = EInfluenceFalloff::Linear;
		
		Manager.PropagateInfluence(Params1);
		Manager.PropagateInfluence(Params2);
		
		// Check that influences combine
		FVector2D MidPoint(500.0f, 500.0f);
		float MidValue = Manager.GetCellValueAtWorldPosition(MidPoint, TEXT("Multi"));
		TestTrue("Multiple sources should combine influence", MidValue > 0.0f);
		
		float Source1Value = Manager.GetCellValueAtWorldPosition(Source1, TEXT("Multi"));
		float Source2Value = Manager.GetCellValueAtWorldPosition(Source2, TEXT("Multi"));
		TestTrue("Source 1 should have high influence", Source1Value > 40.0f);
		TestTrue("Source 2 should have high influence", Source2Value > 40.0f);
	}

	// Test decay over time
	{
		Manager.ClearAllValues();
		
		FVector2D DecaySource(500.0f, 500.0f);
		FInfluencePropagationParams DecayParams;
		DecayParams.SourcePosition = DecaySource;
		DecayParams.ValueKey = TEXT("Decay");
		DecayParams.Strength = 100.0f;
		DecayParams.Radius = 100.0f;
		DecayParams.Falloff = EInfluenceFalloff::Linear;
		
		Manager.PropagateInfluence(DecayParams);
		
		float InitialValue = Manager.GetCellValueAtWorldPosition(DecaySource, TEXT("Decay"));
		
		// Apply decay
		FInfluenceDecayParams DecaySettings;
		DecaySettings.ValueKey = TEXT("Decay");
		DecaySettings.DecayRate = 0.1f; // 10% per second
		DecaySettings.MinValue = 0.0f;
		
		Manager.ApplyDecay(DecaySettings, 1.0f); // 1 second
		
		float DecayedValue = Manager.GetCellValueAtWorldPosition(DecaySource, TEXT("Decay"));
		TestTrue("Value should decay over time", DecayedValue < InitialValue);
		TestTrue("Decayed value should be approximately 90%", FMath::Abs(DecayedValue - InitialValue * 0.9f) < 5.0f);
	}

	return true;
}

/**
 * Performance test for influence map operations
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FInfluenceMapPerformanceTest, "SpatialFusion.Performance.InfluenceMap", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FInfluenceMapPerformanceTest::RunTest(const FString& Parameters)
{
	// Test large grid performance
	{
		TInfluenceMapManager<FVector2D> LargeManager;
		
		double StartTime = FPlatformTime::Seconds();
		LargeManager.Initialize(FVector2D(100, 100), 10.0f, FVector2D::ZeroVector);
		double InitTime = FPlatformTime::Seconds() - StartTime;
		
		TestTrue("Large grid initialization should be fast", InitTime < 0.1); // Less than 100ms
		
		// Test bulk operations
		StartTime = FPlatformTime::Seconds();
		for (int32 i = 0; i < 1000; i++)
		{
			FVector2D RandomPos(FMath::RandRange(0.0f, 1000.0f), FMath::RandRange(0.0f, 1000.0f));
			LargeManager.SetCellValueAtWorldPosition(RandomPos, TEXT("Perf"), FMath::RandRange(0.0f, 100.0f));
		}
		double BulkSetTime = FPlatformTime::Seconds() - StartTime;
		
		TestTrue("Bulk set operations should be fast", BulkSetTime < 0.05); // Less than 50ms
		
		// Test bulk propagation
		StartTime = FPlatformTime::Seconds();
		for (int32 i = 0; i < 10; i++)
		{
			FInfluencePropagationParams PerfParams;
			PerfParams.SourcePosition = FVector2D(FMath::RandRange(100.0f, 900.0f), FMath::RandRange(100.0f, 900.0f));
			PerfParams.ValueKey = TEXT("PerfProp");
			PerfParams.Strength = 50.0f;
			PerfParams.Radius = 100.0f;
			PerfParams.Falloff = EInfluenceFalloff::Linear;
			
			LargeManager.PropagateInfluence(PerfParams);
		}
		double PropagationTime = FPlatformTime::Seconds() - StartTime;
		
		TestTrue("Bulk propagation should be reasonable", PropagationTime < 0.5); // Less than 500ms
	}

	return true;
}
