#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\TPersistentObjectPtrExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UTPersistentObjectPtrExporter
{
    static const FCSExportedFunction UnrealSharpBind_FromObject;
    static const FCSExportedFunction UnrealSharpBind_FromSoftObjectPath;
    static const FCSExportedFunction UnrealSharpBind_Get;
    static const FCSExportedFunction UnrealSharpBind_GetNativePointer;
    static const FCSExportedFunction UnrealSharpBind_GetUniqueID;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTPersistentObjectPtrExporter::UnrealSharpBind_FromObject = FCSExportedFunction("TPersistentObjectPtrExporter", "FromObject", (void*)&UTPersistentObjectPtrExporter::FromObject, GetFunctionSize(UTPersistentObjectPtrExporter::FromObject));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTPersistentObjectPtrExporter::UnrealSharpBind_FromSoftObjectPath = FCSExportedFunction("TPersistentObjectPtrExporter", "FromSoftObjectPath", (void*)&UTPersistentObjectPtrExporter::FromSoftObjectPath, GetFunctionSize(UTPersistentObjectPtrExporter::FromSoftObjectPath));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTPersistentObjectPtrExporter::UnrealSharpBind_Get = FCSExportedFunction("TPersistentObjectPtrExporter", "Get", (void*)&UTPersistentObjectPtrExporter::Get, GetFunctionSize(UTPersistentObjectPtrExporter::Get));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTPersistentObjectPtrExporter::UnrealSharpBind_GetNativePointer = FCSExportedFunction("TPersistentObjectPtrExporter", "GetNativePointer", (void*)&UTPersistentObjectPtrExporter::GetNativePointer, GetFunctionSize(UTPersistentObjectPtrExporter::GetNativePointer));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTPersistentObjectPtrExporter::UnrealSharpBind_GetUniqueID = FCSExportedFunction("TPersistentObjectPtrExporter", "GetUniqueID", (void*)&UTPersistentObjectPtrExporter::GetUniqueID, GetFunctionSize(UTPersistentObjectPtrExporter::GetUniqueID));

