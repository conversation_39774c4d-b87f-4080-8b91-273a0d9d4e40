// Copyright Epic Games, Inc. All Rights Reserved.

#include "PropagationRuleSet.h"
#include "PropagationRuleAsset.h"
#include "Engine/Engine.h"
#include "Async/ParallelFor.h"

//////////////////////////////////////////////////////////////////////////
// UPropagationRuleSet

UPropagationRuleSet::UPropagationRuleSet()
{
	// Set default values
	RuleSetName = TEXT("DefaultRuleSet");
	RuleSetDescription = TEXT("A new rule set");
	ExecutionStrategy = ERuleExecutionStrategy::Priority;
	ConflictResolution = ERuleConflictResolution::Priority;
	bStopOnFailure = false;
	bValidateDependencies = true;

	// Initialize performance settings
	PerformanceSettings.MaxRulesPerFrame = 100;
	PerformanceSettings.MaxExecutionTimePerFrame = 5.0f;
	PerformanceSettings.bUseParallelExecution = true;
	PerformanceSettings.WorkerThreadCount = 4;
	PerformanceSettings.bCacheRuleEvaluations = true;
	PerformanceSettings.CacheDuration = 0.1f;
	PerformanceSettings.bUseAdaptiveExecution = true;
	PerformanceSettings.TargetFrameRate = 60.0f;
}

FPrimaryAssetId UPropagationRuleSet::GetPrimaryAssetId() const
{
	return FPrimaryAssetId(TEXT("PropagationRuleSet"), GetFName());
}

int32 UPropagationRuleSet::ExecuteRules(FPropagationContext& Context) const
{
	if (!PassesRuleSetFilters(Context))
	{
		return 0;
	}

	TArray<UPropagationRuleAsset*> ApplicableRules = GetApplicableRules(Context);
	if (ApplicableRules.Num() == 0)
	{
		return 0;
	}

	// Sort rules by execution strategy
	TArray<UPropagationRuleAsset*> SortedRules = SortRulesByStrategy(ApplicableRules);

	// Resolve conflicts
	TArray<UPropagationRuleAsset*> RulesToExecute = ResolveRuleConflicts(SortedRules, Context);

	// Execute rules
	if (PerformanceSettings.bUseParallelExecution && RulesToExecute.Num() > 1)
	{
		return ExecuteRulesParallel(RulesToExecute, Context);
	}
	else
	{
		return ExecuteRulesSequential(RulesToExecute, Context);
	}
}

TArray<UPropagationRuleAsset*> UPropagationRuleSet::GetApplicableRules(const FPropagationContext& Context) const
{
	TArray<UPropagationRuleAsset*> ApplicableRules;

	// Add pre-process rules first
	for (UPropagationRuleAsset* Rule : PreProcessRules)
	{
		if (Rule && Rule->bIsEnabled && !DisabledRules.Contains(Rule))
		{
			ApplicableRules.Add(Rule);
		}
	}

	// Add main rules
	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (Rule && Rule->bIsEnabled && !DisabledRules.Contains(Rule))
		{
			ApplicableRules.Add(Rule);
		}
	}

	// Add post-process rules last
	for (UPropagationRuleAsset* Rule : PostProcessRules)
	{
		if (Rule && Rule->bIsEnabled && !DisabledRules.Contains(Rule))
		{
			ApplicableRules.Add(Rule);
		}
	}

	return ApplicableRules;
}

bool UPropagationRuleSet::AddRule(UPropagationRuleAsset* Rule)
{
	if (!Rule)
	{
		return false;
	}

	if (Rules.Contains(Rule))
	{
		return false; // Already exists
	}

	Rules.Add(Rule);
	return true;
}

bool UPropagationRuleSet::RemoveRule(UPropagationRuleAsset* Rule)
{
	if (!Rule)
	{
		return false;
	}

	bool bRemoved = false;
	bRemoved |= Rules.Remove(Rule) > 0;
	bRemoved |= PreProcessRules.Remove(Rule) > 0;
	bRemoved |= PostProcessRules.Remove(Rule) > 0;
	bRemoved |= DisabledRules.Remove(Rule) > 0;

	return bRemoved;
}

bool UPropagationRuleSet::SetRuleEnabled(UPropagationRuleAsset* Rule, bool bEnabled)
{
	if (!Rule)
	{
		return false;
	}

	if (bEnabled)
	{
		// Enable rule
		DisabledRules.Remove(Rule);
		Rule->bIsEnabled = true;
	}
	else
	{
		// Disable rule
		if (!DisabledRules.Contains(Rule))
		{
			DisabledRules.Add(Rule);
		}
		Rule->bIsEnabled = false;
	}

	return true;
}

TArray<UPropagationRuleAsset*> UPropagationRuleSet::GetEnabledRules() const
{
	TArray<UPropagationRuleAsset*> EnabledRules;

	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (Rule && Rule->bIsEnabled && !DisabledRules.Contains(Rule))
		{
			EnabledRules.Add(Rule);
		}
	}

	return EnabledRules;
}

TArray<UPropagationRuleAsset*> UPropagationRuleSet::GetRulesInExecutionOrder() const
{
	TArray<UPropagationRuleAsset*> AllRules = GetEnabledRules();
	return SortRulesByStrategy(AllRules);
}

bool UPropagationRuleSet::ValidateRuleSet(TArray<FString>& OutErrors, TArray<FString>& OutWarnings) const
{
	OutErrors.Empty();
	OutWarnings.Empty();
	bool bIsValid = true;

	// Validate rule set name
	if (RuleSetName.IsNone())
	{
		OutErrors.Add(TEXT("Rule set name cannot be empty"));
		bIsValid = false;
	}

	// Validate individual rules
	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (!Rule)
		{
			OutErrors.Add(TEXT("Rule set contains null rule reference"));
			bIsValid = false;
			continue;
		}

		TArray<FString> RuleErrors;
		if (!Rule->ValidateRule(RuleErrors))
		{
			for (const FString& Error : RuleErrors)
			{
				OutErrors.Add(FString::Printf(TEXT("Rule '%s': %s"), *Rule->RuleName.ToString(), *Error));
			}
			bIsValid = false;
		}
	}

	// Check for circular dependencies
	if (bValidateDependencies)
	{
		TArray<TArray<UPropagationRuleAsset*>> CircularDependencies;
		if (HasCircularDependencies(CircularDependencies))
		{
			OutErrors.Add(TEXT("Rule set contains circular dependencies"));
			bIsValid = false;
		}
	}

	// Performance warnings
	if (Rules.Num() > 50)
	{
		OutWarnings.Add(FString::Printf(TEXT("Large number of rules (%d) may impact performance"), Rules.Num()));
	}

	return bIsValid;
}

TMap<FName, float> UPropagationRuleSet::GetPerformanceStatistics() const
{
	TMap<FName, float> Stats;

	Stats.Add(TEXT("TotalRules"), static_cast<float>(Rules.Num()));
	Stats.Add(TEXT("EnabledRules"), static_cast<float>(GetEnabledRules().Num()));
	Stats.Add(TEXT("DisabledRules"), static_cast<float>(DisabledRules.Num()));
	Stats.Add(TEXT("PreProcessRules"), static_cast<float>(PreProcessRules.Num()));
	Stats.Add(TEXT("PostProcessRules"), static_cast<float>(PostProcessRules.Num()));

	// Calculate estimated complexity
	float TotalComplexity = 0.0f;
	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (Rule && Rule->bIsEnabled)
		{
			TotalComplexity += Rule->ComputationalCost;
		}
	}
	Stats.Add(TEXT("TotalComplexity"), TotalComplexity);

	return Stats;
}

UPropagationRuleSet* UPropagationRuleSet::CreateFilteredRuleSet(const FGameplayTagContainer& FilterTags, bool bRequireAllTags) const
{
	UPropagationRuleSet* FilteredSet = NewObject<UPropagationRuleSet>();
	FilteredSet->RuleSetName = FName(*FString::Printf(TEXT("%s_Filtered"), *RuleSetName.ToString()));
	FilteredSet->ExecutionStrategy = ExecutionStrategy;
	FilteredSet->ConflictResolution = ConflictResolution;
	FilteredSet->PerformanceSettings = PerformanceSettings;

	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (!Rule)
		{
			continue;
		}

		bool bMatches = false;
		if (bRequireAllTags)
		{
			bMatches = Rule->RuleTags.HasAll(FilterTags);
		}
		else
		{
			bMatches = Rule->RuleTags.HasAny(FilterTags);
		}

		if (bMatches)
		{
			FilteredSet->Rules.Add(Rule);
		}
	}

	return FilteredSet;
}

bool UPropagationRuleSet::MergeRuleSet(const UPropagationRuleSet* OtherRuleSet, bool bOverwriteConflicts)
{
	if (!OtherRuleSet)
	{
		return false;
	}

	for (UPropagationRuleAsset* OtherRule : OtherRuleSet->Rules)
	{
		if (!OtherRule)
		{
			continue;
		}

		// Check for conflicts
		bool bHasConflict = false;
		for (UPropagationRuleAsset* ExistingRule : Rules)
		{
			if (ExistingRule && ExistingRule->RuleName == OtherRule->RuleName)
			{
				bHasConflict = true;
				if (bOverwriteConflicts)
				{
					Rules.Remove(ExistingRule);
				}
				break;
			}
		}

		if (!bHasConflict || bOverwriteConflicts)
		{
			Rules.Add(OtherRule);
		}
	}

	return true;
}

TArray<UPropagationRuleAsset*> UPropagationRuleSet::SortRulesByStrategy(const TArray<UPropagationRuleAsset*>& RulesToSort) const
{
	TArray<UPropagationRuleAsset*> SortedRules = RulesToSort;

	switch (ExecutionStrategy)
	{
	case ERuleExecutionStrategy::Priority:
		SortedRules.Sort([](const UPropagationRuleAsset& A, const UPropagationRuleAsset& B)
		{
			return A.GetPriorityValue() > B.GetPriorityValue();
		});
		break;

	case ERuleExecutionStrategy::Random:
		for (int32 i = SortedRules.Num() - 1; i > 0; i--)
		{
			int32 j = FMath::RandRange(0, i);
			SortedRules.Swap(i, j);
		}
		break;

	case ERuleExecutionStrategy::Dependency:
		// Topological sort based on dependencies
		// This is a simplified implementation
		SortedRules.Sort([](const UPropagationRuleAsset& A, const UPropagationRuleAsset& B)
		{
			// Rules with fewer dependencies come first
			return A.PrerequisiteRules.Num() < B.PrerequisiteRules.Num();
		});
		break;

	case ERuleExecutionStrategy::Parallel:
	case ERuleExecutionStrategy::Custom:
	default:
		// Keep original order
		break;
	}

	return SortedRules;
}

TArray<UPropagationRuleAsset*> UPropagationRuleSet::ResolveRuleConflicts(const TArray<UPropagationRuleAsset*>& ConflictingRules, const FPropagationContext& Context) const
{
	TArray<UPropagationRuleAsset*> ResolvedRules;

	for (UPropagationRuleAsset* Rule : ConflictingRules)
	{
		if (!Rule)
		{
			continue;
		}

		bool bHasConflict = false;
		for (UPropagationRuleAsset* ExistingRule : ResolvedRules)
		{
			if (ExistingRule && Rule->ConflictsWith(ExistingRule))
			{
				bHasConflict = true;

				switch (ConflictResolution)
				{
				case ERuleConflictResolution::Priority:
					if (Rule->GetPriorityValue() > ExistingRule->GetPriorityValue())
					{
						ResolvedRules.Remove(ExistingRule);
						ResolvedRules.Add(Rule);
					}
					break;

				case ERuleConflictResolution::FirstWins:
					// Keep existing rule, skip new one
					break;

				case ERuleConflictResolution::LastWins:
					ResolvedRules.Remove(ExistingRule);
					ResolvedRules.Add(Rule);
					break;

				case ERuleConflictResolution::Skip:
					// Skip both rules
					ResolvedRules.Remove(ExistingRule);
					break;

				case ERuleConflictResolution::Merge:
					// Both rules can execute (merge their effects)
					ResolvedRules.Add(Rule);
					break;
				}
				break;
			}
		}

		if (!bHasConflict)
		{
			ResolvedRules.Add(Rule);
		}
	}

	return ResolvedRules;
}

bool UPropagationRuleSet::PassesRuleSetFilters(const FPropagationContext& Context) const
{
	// Check required cell tags
	if (RequiredCellTags.Num() > 0)
	{
		for (const FGameplayTag& RequiredTag : RequiredCellTags)
		{
			if (!Context.HasCellTag(RequiredTag))
			{
				return false;
			}
		}
	}

	// Check excluded cell tags
	if (ExcludedCellTags.Num() > 0)
	{
		for (const FGameplayTag& ExcludedTag : ExcludedCellTags)
		{
			if (Context.HasCellTag(ExcludedTag))
			{
				return false;
			}
		}
	}

	// Check required value keys
	for (const FName& RequiredKey : RequiredValueKeys)
	{
		if (Context.GetCellValue(RequiredKey) == 0.0f)
		{
			return false; // Assuming 0.0f means the key doesn't exist
		}
	}

	// Check minimum values
	for (const auto& MinValuePair : MinimumValues)
	{
		float CellValue = Context.GetCellValue(MinValuePair.Key);
		if (CellValue < MinValuePair.Value)
		{
			return false;
		}
	}

	// Check maximum values
	for (const auto& MaxValuePair : MaximumValues)
	{
		float CellValue = Context.GetCellValue(MaxValuePair.Key);
		if (CellValue > MaxValuePair.Value)
		{
			return false;
		}
	}

	return true;
}

int32 UPropagationRuleSet::ExecuteRulesParallel(const TArray<UPropagationRuleAsset*>& RulesToExecute, FPropagationContext& Context) const
{
	// For now, fall back to sequential execution
	// Parallel execution would require thread-safe context handling
	return ExecuteRulesSequential(RulesToExecute, Context);
}

int32 UPropagationRuleSet::ExecuteRulesSequential(const TArray<UPropagationRuleAsset*>& RulesToExecute, FPropagationContext& Context) const
{
	int32 ExecutedCount = 0;

	for (UPropagationRuleAsset* Rule : RulesToExecute)
	{
		if (!Rule)
		{
			continue;
		}

		bool bExecuted = Rule->ExecuteRule(Context);
		if (bExecuted)
		{
			ExecutedCount++;

			// Trigger any dependent rules
			for (UPropagationRuleAsset* TriggeredRule : Rule->GetTriggeredRules())
			{
				if (TriggeredRule && TriggeredRule->bIsEnabled)
				{
					if (TriggeredRule->ExecuteRule(Context))
					{
						ExecutedCount++;
					}
				}
			}
		}
		else if (bStopOnFailure)
		{
			break;
		}
	}

	return ExecutedCount;
}

#if WITH_EDITOR
TMap<UPropagationRuleAsset*, TArray<UPropagationRuleAsset*>> UPropagationRuleSet::GetDependencyGraph() const
{
	TMap<UPropagationRuleAsset*, TArray<UPropagationRuleAsset*>> DependencyGraph;

	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (Rule)
		{
			DependencyGraph.Add(Rule, Rule->PrerequisiteRules);
		}
	}

	return DependencyGraph;
}

bool UPropagationRuleSet::HasCircularDependencies(TArray<TArray<UPropagationRuleAsset*>>& OutCircularDependencies) const
{
	OutCircularDependencies.Empty();

	// Simple cycle detection using DFS
	TSet<UPropagationRuleAsset*> Visited;
	TSet<UPropagationRuleAsset*> RecursionStack;

	for (UPropagationRuleAsset* Rule : Rules)
	{
		if (Rule && !Visited.Contains(Rule))
		{
			TArray<UPropagationRuleAsset*> CurrentPath;
			if (HasCircularDependencyDFS(Rule, Visited, RecursionStack, CurrentPath))
			{
				OutCircularDependencies.Add(CurrentPath);
			}
		}
	}

	return OutCircularDependencies.Num() > 0;
}

bool UPropagationRuleSet::HasCircularDependencyDFS(UPropagationRuleAsset* Rule, TSet<UPropagationRuleAsset*>& Visited, TSet<UPropagationRuleAsset*>& RecursionStack, TArray<UPropagationRuleAsset*>& CurrentPath) const
{
	if (!Rule)
	{
		return false;
	}

	Visited.Add(Rule);
	RecursionStack.Add(Rule);
	CurrentPath.Add(Rule);

	for (UPropagationRuleAsset* Dependency : Rule->PrerequisiteRules)
	{
		if (!Dependency)
		{
			continue;
		}

		if (RecursionStack.Contains(Dependency))
		{
			// Found a cycle
			return true;
		}

		if (!Visited.Contains(Dependency))
		{
			if (HasCircularDependencyDFS(Dependency, Visited, RecursionStack, CurrentPath))
			{
				return true;
			}
		}
	}

	RecursionStack.Remove(Rule);
	CurrentPath.RemoveAt(CurrentPath.Num() - 1);
	return false;
}

FString UPropagationRuleSet::GeneratePerformanceReport() const
{
	FString Report;
	Report += FString::Printf(TEXT("Rule Set Performance Report: %s\n"), *RuleSetName.ToString());
	Report += FString::Printf(TEXT("Total Rules: %d\n"), Rules.Num());
	Report += FString::Printf(TEXT("Enabled Rules: %d\n"), GetEnabledRules().Num());
	Report += FString::Printf(TEXT("Execution Strategy: %s\n"), *UEnum::GetValueAsString(ExecutionStrategy));

	TMap<FName, float> Stats = GetPerformanceStatistics();
	for (const auto& StatPair : Stats)
	{
		Report += FString::Printf(TEXT("%s: %.2f\n"), *StatPair.Key.ToString(), StatPair.Value);
	}

	return Report;
}
#endif
