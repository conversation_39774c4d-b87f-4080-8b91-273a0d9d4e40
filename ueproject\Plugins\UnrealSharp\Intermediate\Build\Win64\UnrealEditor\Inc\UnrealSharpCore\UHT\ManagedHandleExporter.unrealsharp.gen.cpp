#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\ManagedHandleExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UManagedHandleExporter
{
    static const FCSExportedFunction UnrealSharpBind_StoreManagedHandle;
    static const FCSExportedFunction UnrealSharpBind_LoadManagedHandle;
    static const FCSExportedFunction UnrealSharpBind_StoreUnmanagedMemory;
    static const FCSExportedFunction UnrealSharpBind_LoadUnmanagedMemory;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UManagedHandleExporter::UnrealSharpBind_StoreManagedHandle = FCSExportedFunction("ManagedHandleExporter", "StoreManagedHandle", (void*)&UManagedHandleExporter::StoreManagedHandle, GetFunctionSize(UManagedHandleExporter::StoreManagedHandle));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UManagedHandleExporter::UnrealSharpBind_LoadManagedHandle = FCSExportedFunction("ManagedHandleExporter", "LoadManagedHandle", (void*)&UManagedHandleExporter::LoadManagedHandle, GetFunctionSize(UManagedHandleExporter::LoadManagedHandle));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UManagedHandleExporter::UnrealSharpBind_StoreUnmanagedMemory = FCSExportedFunction("ManagedHandleExporter", "StoreUnmanagedMemory", (void*)&UManagedHandleExporter::StoreUnmanagedMemory, GetFunctionSize(UManagedHandleExporter::StoreUnmanagedMemory));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UManagedHandleExporter::UnrealSharpBind_LoadUnmanagedMemory = FCSExportedFunction("ManagedHandleExporter", "LoadUnmanagedMemory", (void*)&UManagedHandleExporter::LoadUnmanagedMemory, GetFunctionSize(UManagedHandleExporter::LoadUnmanagedMemory));

