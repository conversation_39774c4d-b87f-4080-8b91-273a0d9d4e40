#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\UAssetManagerExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UUAssetManagerExporter
{
    static const FCSExportedFunction UnrealSharpBind_GetAssetManager;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UUAssetManagerExporter::UnrealSharpBind_GetAssetManager = FCSExportedFunction("UAssetManagerExporter", "GetAssetManager", (void*)&UUAssetManagerExporter::GetAssetManager, GetFunctionSize(UUAssetManagerExporter::GetAssetManager));

