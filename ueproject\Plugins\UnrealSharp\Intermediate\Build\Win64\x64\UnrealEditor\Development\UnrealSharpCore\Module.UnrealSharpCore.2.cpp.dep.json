{"Version": "1.2", "Data": {"Source": "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpcore\\module.unrealsharpcore.2.cpp", "ProvidedModule": "", "PCH": "e:\\unreal projects\\horizonbrigade\\ueproject\\intermediate\\build\\win64\\x64\\horizonbrigadeeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpcore\\definitions.unrealsharpcore.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csinterface.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csinterface.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\utils\\csmacros.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csinterface.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cslocalplayersubsystem.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\subsystems\\cslocalplayersubsystem.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cslocalplayersubsystem.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanagedgchandle.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedgchandle.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedcallbackscache.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanagedgchandle.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanager.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanager.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\managed\\dotnetruntime\\inc\\coreclr_delegates.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\managed\\dotnetruntime\\inc\\hostfxr.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csassembly.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\unrealsharpcore.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cstypereferencemetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csfieldname.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csnamespace.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\utils\\csclassutilities.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csclass.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csclass.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csskeletonclass.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csskeletonclass.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanager.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmappropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csmappropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cspropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cspropertymetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csmembermetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csunrealtype.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cspropertytype.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cspropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmappropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmovementcomponentextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csmovementcomponentextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmovementcomponentextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmulticastdelegatepropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csmulticastdelegatepropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csdelegatebasepropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdelegatebasepropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmulticastdelegatepropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csobjectextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csobjectextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csobjectextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csobjectpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csobjectpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cscommonpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cscommonpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csobjectpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csoptionalpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csoptionalpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csoptionalpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cspackagenameextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\cspackagenameextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cspackagenameextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csprimarydataasset.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\dataassets\\csprimarydataasset.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csprimarydataasset.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cspropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cspropertytype.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csquatextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csquatextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csquatextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csreplicatedobject.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\replication\\csreplicatedobject.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csreplicatedobject.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csscriptinterfacepropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csscriptinterfacepropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csscriptinterfacepropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csscriptstruct.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csscriptstruct.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\userdefinedstruct.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\userdefinedstruct.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\managedreferencescollection.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\managedreferencescollection.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csscriptstruct.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssetpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cssetpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssetpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssimplepropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cssimplepropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssimplepropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csskeletonclass.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssoftclasspropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cssoftclasspropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssoftclasspropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssoftobjectpathextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\cssoftobjectpathextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssoftobjectpathextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csstructpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csstructpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csstructpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssystemextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\cssystemextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cssystemextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cstimerextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\export\\cstimerextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csbindsmanager.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csexportedfunction.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cstimerextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cstimerextensions.unrealsharp.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\unrealsharpbinds.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csunmanageddatastore.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csunmanageddatastore.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csunmanageddatastore.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csunrealsharpsettings.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csunrealsharpsettings.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csunrealsharpsettings.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csuserwidgetextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csuserwidgetextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csuserwidgetextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csworldextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csworldextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csworldextensions.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}