{"Version": "1.2", "Data": {"Source": "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpasync\\module.unrealsharpasync.cpp", "ProvidedModule": "", "PCH": "e:\\unreal projects\\horizonbrigade\\ueproject\\intermediate\\build\\win64\\x64\\horizonbrigadeeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpasync\\definitions.unrealsharpasync.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncactionbase.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\public\\csasyncactionbase.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanageddelegate.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedgchandle.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedcallbackscache.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanagedgchandle.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csbindsmanager.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csexportedfunction.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncactionbase.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncactionbase.unrealsharp.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\unrealsharpbinds.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncloadprimarydataassets.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\public\\csasyncloadprimarydataassets.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncloadprimarydataassets.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncloadsoftobjectptr.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\public\\csasyncloadsoftobjectptr.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\csasyncloadsoftobjectptr.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpasync\\uht\\unrealsharpasync.init.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpasync\\permoduleinline.gen.cpp", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\private\\csasyncactionbase.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\public\\unrealsharpasync.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\private\\csasyncloadprimarydataassets.cpp", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanagertypes.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanagertypes.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\iostoreondemand.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\sharedstring.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandhostgroup.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandtoc.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\experimental\\streamablemanagererror.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformchunkinstall.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\contentencryptionconfig.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanager.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\private\\csasyncloadsoftobjectptr.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpasync\\private\\unrealsharpasync.cpp"], "ImportedModules": [], "ImportedHeaderUnits": []}}