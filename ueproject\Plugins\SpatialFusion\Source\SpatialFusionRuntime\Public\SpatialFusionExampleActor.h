// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "InfluenceMapManager2D.h"
#include "InfluenceMapManager3D.h"
#include "FlowFieldManager2D.h"
#include "PropagationRuleEngine.h"
#include "PropagationRuleSet.h"
#include "NiagaraComponent.h"
#include "SpatialFusionVisualization.h"
#include "SpatialFusionExampleActor.generated.h"

/**
 * Example simulation type
 */
UENUM(BlueprintType)
enum class ESpatialFusionExampleType : uint8
{
	/** Fire propagation simulation */
	FirePropagation UMETA(DisplayName = "Fire Propagation"),
	
	/** Disease spread simulation */
	DiseaseSpread UMETA(DisplayName = "Disease Spread"),
	
	/** Economic influence simulation */
	EconomicInfluence UMETA(DisplayName = "Economic Influence"),
	
	/** Crowd movement simulation */
	CrowdMovement UMETA(DisplayName = "Crowd Movement"),
	
	/** Weather simulation */
	WeatherSimulation UMETA(DisplayName = "Weather Simulation"),
	
	/** Custom simulation */
	Custom UMETA(DisplayName = "Custom")
};

/**
 * Comprehensive example actor demonstrating all SpatialFusion features
 * This actor serves as both a tutorial and a reference implementation
 */
UCLASS(BlueprintType, Blueprintable, meta = (DisplayName = "SpatialFusion Example Actor"))
class SPATIALFUSIONRUNTIME_API ASpatialFusionExampleActor : public AActor
{
	GENERATED_BODY()

public:
	ASpatialFusionExampleActor();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif

	// Components

	/** Root scene component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<USceneComponent> RootSceneComponent;

	/** Visualization component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<USpatialFusionVisualization> VisualizationComponent;

	/** Niagara component for particle effects */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<UNiagaraComponent> ParticleComponent;

	/** Static mesh for visual reference */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<UStaticMeshComponent> ReferenceMesh;

public:
	// Configuration

	/** Type of simulation to run */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
	ESpatialFusionExampleType SimulationType = ESpatialFusionExampleType::FirePropagation;

	/** Grid size for the simulation */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
	FVector GridSize = FVector(50, 50, 1);

	/** Cell size for the simulation */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
	float CellSize = 100.0f;

	/** Whether to use 3D simulation */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
	bool bUse3D = false;

	/** Whether to automatically start simulation */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
	bool bAutoStart = true;

	/** Simulation update frequency */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
	float UpdateFrequency = 10.0f;

	/** Whether to show real-time visualization */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	bool bShowVisualization = true;

	/** Whether to show particle effects */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	bool bShowParticleEffects = true;

	/** Whether to show debug information */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	bool bShowDebugInfo = false;

	// Simulation Parameters

	/** Fire simulation parameters */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::FirePropagation"))
	float FireSpreadRate = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::FirePropagation"))
	float FireDecayRate = 0.1f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::FirePropagation"))
	float FireIntensity = 100.0f;

	/** Disease simulation parameters */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disease Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::DiseaseSpread"))
	float InfectionRate = 0.3f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disease Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::DiseaseSpread"))
	float RecoveryRate = 0.1f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disease Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::DiseaseSpread"))
	float ImmunityDuration = 10.0f;

	/** Economic simulation parameters */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Economic Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::EconomicInfluence"))
	float EconomicGrowthRate = 0.05f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Economic Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::EconomicInfluence"))
	float EconomicDecayRate = 0.02f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Economic Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::EconomicInfluence"))
	float TradeRadius = 500.0f;

	/** Crowd movement parameters */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crowd Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::CrowdMovement"))
	int32 CrowdSize = 100;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crowd Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::CrowdMovement"))
	float CrowdSpeed = 200.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crowd Simulation", meta = (EditCondition = "SimulationType == ESpatialFusionExampleType::CrowdMovement"))
	TArray<FVector> CrowdGoals;

protected:
	// Internal components
	UPROPERTY()
	TObjectPtr<UInfluenceMapManager2D> InfluenceMapManager2D;

	UPROPERTY()
	TObjectPtr<UInfluenceMapManager3D> InfluenceMapManager3D;

	UPROPERTY()
	TObjectPtr<UFlowFieldManager2D> FlowFieldManager;

	UPROPERTY()
	TObjectPtr<UPropagationRuleEngine> RuleEngine;

	UPROPERTY()
	TObjectPtr<UPropagationRuleSet> CurrentRuleSet;

	// Internal state
	bool bSimulationRunning = false;
	float LastUpdateTime = 0.0f;
	float SimulationTime = 0.0f;
	TArray<FVector> SimulationSources;

public:
	// Blueprint callable functions

	/**
	 * Initialize the simulation with current parameters
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void InitializeSimulation();

	/**
	 * Start the simulation
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void StartSimulation();

	/**
	 * Stop the simulation
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void StopSimulation();

	/**
	 * Reset the simulation to initial state
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void ResetSimulation();

	/**
	 * Add a simulation source at the specified location
	 * @param Location - World location for the source
	 * @param Intensity - Source intensity
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void AddSimulationSource(const FVector& Location, float Intensity = 100.0f);

	/**
	 * Remove simulation source at location
	 * @param Location - Location to remove source from
	 * @param Radius - Radius to clear
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void RemoveSimulationSource(const FVector& Location, float Radius = 100.0f);

	/**
	 * Get simulation value at location
	 * @param Location - World location to query
	 * @param ValueKey - Value key to get
	 * @return Value at location
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	float GetSimulationValueAtLocation(const FVector& Location, const FName& ValueKey = TEXT("Default")) const;

	/**
	 * Set simulation value at location
	 * @param Location - World location to set
	 * @param ValueKey - Value key to set
	 * @param Value - Value to set
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void SetSimulationValueAtLocation(const FVector& Location, const FName& ValueKey, float Value);

	/**
	 * Get flow direction at location (for crowd movement)
	 * @param Location - World location to query
	 * @return Flow direction at location
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	FVector GetFlowDirectionAtLocation(const FVector& Location) const;

	/**
	 * Switch to a different simulation type
	 * @param NewType - New simulation type
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	void SwitchSimulationType(ESpatialFusionExampleType NewType);

	/**
	 * Get current simulation statistics
	 * @return Map of statistic names to values
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	TMap<FString, float> GetSimulationStatistics() const;

	/**
	 * Export simulation data to file
	 * @param Filename - Output filename
	 * @return True if export was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	bool ExportSimulationData(const FString& Filename) const;

	/**
	 * Import simulation data from file
	 * @param Filename - Input filename
	 * @return True if import was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	bool ImportSimulationData(const FString& Filename);

	/**
	 * Take a screenshot of the current visualization
	 * @param Filename - Output filename
	 * @return True if screenshot was taken successfully
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion Example")
	bool TakeVisualizationScreenshot(const FString& Filename);

protected:
	/**
	 * Setup simulation based on current type
	 */
	void SetupSimulation();

	/**
	 * Create rule set for current simulation type
	 */
	void CreateRuleSet();

	/**
	 * Update simulation
	 * @param DeltaTime - Time since last update
	 */
	void UpdateSimulation(float DeltaTime);

	/**
	 * Update visualization
	 */
	void UpdateVisualization();

	/**
	 * Update particle effects
	 */
	void UpdateParticleEffects();

	/**
	 * Check if simulation should update
	 * @param CurrentTime - Current time
	 * @return True if should update
	 */
	bool ShouldUpdateSimulation(float CurrentTime) const;

	/**
	 * Get the appropriate influence map manager
	 * @return Influence map manager for current configuration
	 */
	UInfluenceMapManagerBase* GetInfluenceMapManager() const;

	/**
	 * Create fire propagation rules
	 * @return Fire rule set
	 */
	UPropagationRuleSet* CreateFireRules();

	/**
	 * Create disease spread rules
	 * @return Disease rule set
	 */
	UPropagationRuleSet* CreateDiseaseRules();

	/**
	 * Create economic influence rules
	 * @return Economic rule set
	 */
	UPropagationRuleSet* CreateEconomicRules();

	/**
	 * Setup crowd movement flow field
	 */
	void SetupCrowdMovement();

	/**
	 * Draw debug information
	 */
	void DrawDebugInformation();

#if WITH_EDITOR
	/**
	 * Update editor preview
	 */
	void UpdateEditorPreview();
#endif
};
