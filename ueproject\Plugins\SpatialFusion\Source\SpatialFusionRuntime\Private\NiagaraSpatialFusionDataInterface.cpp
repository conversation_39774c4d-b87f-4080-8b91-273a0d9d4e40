// Copyright Epic Games, Inc. All Rights Reserved.

#include "NiagaraSpatialFusionDataInterface.h"
#include "InfluenceMapManagerBase.h"
#include "FlowFieldManager.h"
#include "NiagaraTypes.h"
#include "NiagaraCustomVersion.h"
#include "NiagaraSystemInstance.h"

// Define function names
const FName UNiagaraSpatialFusionDataInterface::GetInfluenceValueName(TEXT("GetInfluenceValue"));
const FName UNiagaraSpatialFusionDataInterface::GetInfluenceGradientName(TEXT("GetInfluenceGradient"));
const FName UNiagaraSpatialFusionDataInterface::GetFlowDirectionName(TEXT("GetFlowDirection"));
const FName UNiagaraSpatialFusionDataInterface::GetFlowDistanceName(TEXT("GetFlowDistance"));
const FName UNiagaraSpatialFusionDataInterface::IsPositionBlockedName(TEXT("IsPositionBlocked"));
const FName UNiagaraSpatialFusionDataInterface::GetGridDimensionsName(TEXT("GetGridDimensions"));
const FName UNiagaraSpatialFusionDataInterface::GetCellSizeName(TEXT("GetCellSize"));
const FName UNiagaraSpatialFusionDataInterface::GetWorldOriginName(TEXT("GetWorldOrigin"));
const FName UNiagaraSpatialFusionDataInterface::SampleInfluenceAtPositionsName(TEXT("SampleInfluenceAtPositions"));
const FName UNiagaraSpatialFusionDataInterface::SampleFlowAtPositionsName(TEXT("SampleFlowAtPositions"));
const FName UNiagaraSpatialFusionDataInterface::GetInfluenceValueWithKeyName(TEXT("GetInfluenceValueWithKey"));
const FName UNiagaraSpatialFusionDataInterface::GetNearestGoalPositionName(TEXT("GetNearestGoalPosition"));
const FName UNiagaraSpatialFusionDataInterface::GetDistanceToNearestGoalName(TEXT("GetDistanceToNearestGoal"));

UNiagaraSpatialFusionDataInterface::UNiagaraSpatialFusionDataInterface()
	: InfluenceMapManager(nullptr)
	, FlowFieldManager(nullptr)
	, bUseWorldSpace(true)
	, DefaultValue(0.0f)
	, bUseInterpolation(true)
	, MaxFlowDistance(10000.0f)
	, bNormalizeFlowDirections(true)
{
}

void UNiagaraSpatialFusionDataInterface::PostInitProperties()
{
	Super::PostInitProperties();

	// Mark render data as dirty
	MarkRenderDataDirty();
}

void UNiagaraSpatialFusionDataInterface::GetFunctions(TArray<FNiagaraFunctionSignature>& OutFunctions)
{
	// Basic influence map functions
	OutFunctions.Add(GetFunctionSignature(GetInfluenceValueName));
	OutFunctions.Add(GetFunctionSignature(GetInfluenceGradientName));
	OutFunctions.Add(GetFunctionSignature(GetInfluenceValueWithKeyName));

	// Flow field functions
	OutFunctions.Add(GetFunctionSignature(GetFlowDirectionName));
	OutFunctions.Add(GetFunctionSignature(GetFlowDistanceName));
	OutFunctions.Add(GetFunctionSignature(GetNearestGoalPositionName));
	OutFunctions.Add(GetFunctionSignature(GetDistanceToNearestGoalName));

	// Utility functions
	OutFunctions.Add(GetFunctionSignature(IsPositionBlockedName));
	OutFunctions.Add(GetFunctionSignature(GetGridDimensionsName));
	OutFunctions.Add(GetFunctionSignature(GetCellSizeName));
	OutFunctions.Add(GetFunctionSignature(GetWorldOriginName));

	// Batch sampling functions
	OutFunctions.Add(GetFunctionSignature(SampleInfluenceAtPositionsName));
	OutFunctions.Add(GetFunctionSignature(SampleFlowAtPositionsName));
}

void UNiagaraSpatialFusionDataInterface::GetVMExternalFunction(const FVMExternalFunctionBindingInfo& BindingInfo, void* InstanceData, FVMExternalFunction &OutFunc)
{
	if (BindingInfo.Name == GetInfluenceValueName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetInfluenceValue);
	}
	else if (BindingInfo.Name == GetInfluenceGradientName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetInfluenceGradient);
	}
	else if (BindingInfo.Name == GetFlowDirectionName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetFlowDirection);
	}
	else if (BindingInfo.Name == GetFlowDistanceName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetFlowDistance);
	}
	else if (BindingInfo.Name == IsPositionBlockedName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::IsPositionBlocked);
	}
	else if (BindingInfo.Name == GetGridDimensionsName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetGridDimensions);
	}
	else if (BindingInfo.Name == GetCellSizeName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetCellSize);
	}
	else if (BindingInfo.Name == GetWorldOriginName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetWorldOrigin);
	}
	else if (BindingInfo.Name == GetInfluenceValueWithKeyName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetInfluenceValueWithKey);
	}
	else if (BindingInfo.Name == GetNearestGoalPositionName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetNearestGoalPosition);
	}
	else if (BindingInfo.Name == GetDistanceToNearestGoalName)
	{
		OutFunc = FVMExternalFunction::CreateUObject(this, &UNiagaraSpatialFusionDataInterface::GetDistanceToNearestGoal);
	}
}

bool UNiagaraSpatialFusionDataInterface::InitPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance)
{
	FNiagaraSpatialFusionInstanceData* InstanceData = new(PerInstanceData) FNiagaraSpatialFusionInstanceData();
	InstanceData->UpdateCache(this);
	return true;
}

void UNiagaraSpatialFusionDataInterface::DestroyPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance)
{
	FNiagaraSpatialFusionInstanceData* InstanceData = static_cast<FNiagaraSpatialFusionInstanceData*>(PerInstanceData);
	InstanceData->~FNiagaraSpatialFusionInstanceData();
}

int32 UNiagaraSpatialFusionDataInterface::PerInstanceDataSize() const
{
	return sizeof(FNiagaraSpatialFusionInstanceData);
}

void UNiagaraSpatialFusionDataInterface::PreSimulateTick(void* PerInstanceData, float DeltaSeconds)
{
	FNiagaraSpatialFusionInstanceData* InstanceData = static_cast<FNiagaraSpatialFusionInstanceData*>(PerInstanceData);
	
	if (InstanceData->NeedsCacheUpdate(DeltaSeconds))
	{
		InstanceData->UpdateCache(this);
	}
}

bool UNiagaraSpatialFusionDataInterface::Equals(const UNiagaraDataInterface* Other) const
{
	if (!Super::Equals(Other))
	{
		return false;
	}

	const UNiagaraSpatialFusionDataInterface* OtherInterface = CastChecked<UNiagaraSpatialFusionDataInterface>(Other);
	return OtherInterface->InfluenceMapManager == InfluenceMapManager &&
		   OtherInterface->FlowFieldManager == FlowFieldManager &&
		   OtherInterface->bUseWorldSpace == bUseWorldSpace &&
		   OtherInterface->DefaultValue == DefaultValue &&
		   OtherInterface->bUseInterpolation == bUseInterpolation &&
		   OtherInterface->MaxFlowDistance == MaxFlowDistance &&
		   OtherInterface->bNormalizeFlowDirections == bNormalizeFlowDirections;
}

#if WITH_EDITORONLY_DATA
void UNiagaraSpatialFusionDataInterface::GetParameterDefinitionHLSL(const FNiagaraDataInterfaceGPUParamInfo& ParamInfo, FString& OutHLSL)
{
	// GPU implementation would go here
	OutHLSL += TEXT("// SpatialFusion Data Interface GPU parameters\n");
}

bool UNiagaraSpatialFusionDataInterface::GetFunctionHLSL(const FNiagaraDataInterfaceGPUParamInfo& ParamInfo, const FNiagaraDataInterfaceGeneratedFunction& FunctionInfo, int FunctionInstanceIndex, FString& OutHLSL)
{
	// GPU implementation would go here
	return false; // CPU only for now
}
#endif

//////////////////////////////////////////////////////////////////////////
// Instance Data Implementation

void UNiagaraSpatialFusionDataInterface::FNiagaraSpatialFusionInstanceData::UpdateCache(UNiagaraSpatialFusionDataInterface* Interface)
{
	CachedInfluenceMapManager = Interface->InfluenceMapManager;
	CachedFlowFieldManager = Interface->FlowFieldManager;

	if (CachedInfluenceMapManager.IsValid())
	{
		GridDimensions = CachedInfluenceMapManager->GetGridDimensions();
		CellSize = CachedInfluenceMapManager->GetCellSize();
		WorldOrigin = CachedInfluenceMapManager->GetWorldOrigin();
		bCacheValid = true;
	}
	else
	{
		GridDimensions = FVector::ZeroVector;
		CellSize = 100.0f;
		WorldOrigin = FVector::ZeroVector;
		bCacheValid = false;
	}

	LastUpdateTime = 0.0f;
}

bool UNiagaraSpatialFusionDataInterface::FNiagaraSpatialFusionInstanceData::NeedsCacheUpdate(float CurrentTime) const
{
	// Update cache every 0.1 seconds or if invalid
	return !bCacheValid || (CurrentTime - LastUpdateTime) > 0.1f;
}

//////////////////////////////////////////////////////////////////////////
// VM Function Implementations

void UNiagaraSpatialFusionDataInterface::GetInfluenceValue(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<float> ValueParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());
		
		float Value = DefaultValue;
		if (InstanceData->bCacheValid && InstanceData->CachedInfluenceMapManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);
			
			if (bUseInterpolation)
			{
				Value = SampleInfluenceValueInterpolated(GridPos, TEXT("Default"), InstanceData);
			}
			else
			{
				Value = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos, TEXT("Default"));
			}
		}

		ValueParam.SetAndAdvance(Value);
	}
}

void UNiagaraSpatialFusionDataInterface::GetInfluenceGradient(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<FVector3f> GradientParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());
		
		FVector Gradient = FVector::ZeroVector;
		if (InstanceData->bCacheValid && InstanceData->CachedInfluenceMapManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);
			
			// Calculate gradient using finite differences
			float CenterValue = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos, TEXT("Default"));
			float RightValue = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos + FVector(InstanceData->CellSize, 0, 0), TEXT("Default"));
			float UpValue = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos + FVector(0, InstanceData->CellSize, 0), TEXT("Default"));
			float ForwardValue = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos + FVector(0, 0, InstanceData->CellSize), TEXT("Default"));

			Gradient.X = (RightValue - CenterValue) / InstanceData->CellSize;
			Gradient.Y = (UpValue - CenterValue) / InstanceData->CellSize;
			Gradient.Z = (ForwardValue - CenterValue) / InstanceData->CellSize;
		}

		GradientParam.SetAndAdvance(FVector3f(Gradient));
	}
}

void UNiagaraSpatialFusionDataInterface::GetFlowDirection(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<FVector3f> DirectionParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		FVector Direction = FVector::ZeroVector;
		if (InstanceData->bCacheValid && InstanceData->CachedFlowFieldManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);

			if (bUseInterpolation)
			{
				Direction = SampleFlowDirectionInterpolated(GridPos, InstanceData);
			}
			else
			{
				Direction = InstanceData->CachedFlowFieldManager->GetFlowDirectionAtPosition(GridPos);
			}

			if (bNormalizeFlowDirections && !Direction.IsZero())
			{
				Direction.Normalize();
			}
		}

		DirectionParam.SetAndAdvance(FVector3f(Direction));
	}
}

void UNiagaraSpatialFusionDataInterface::GetFlowDistance(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<float> DistanceParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		float Distance = MaxFlowDistance;
		if (InstanceData->bCacheValid && InstanceData->CachedFlowFieldManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);
			Distance = InstanceData->CachedFlowFieldManager->GetDistanceAtPosition(GridPos);

			// Clamp to max distance
			Distance = FMath::Min(Distance, MaxFlowDistance);
		}

		DistanceParam.SetAndAdvance(Distance);
	}
}

void UNiagaraSpatialFusionDataInterface::IsPositionBlocked(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<bool> BlockedParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		bool bBlocked = false;
		if (InstanceData->bCacheValid && InstanceData->CachedInfluenceMapManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);
			bBlocked = InstanceData->CachedInfluenceMapManager->IsCellBlockedAtWorldPosition(GridPos);
		}

		BlockedParam.SetAndAdvance(bBlocked);
	}
}

void UNiagaraSpatialFusionDataInterface::GetGridDimensions(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIOutputParam<FVector3f> DimensionsParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Dimensions = InstanceData->bCacheValid ? InstanceData->GridDimensions : FVector::ZeroVector;
		DimensionsParam.SetAndAdvance(FVector3f(Dimensions));
	}
}

void UNiagaraSpatialFusionDataInterface::GetCellSize(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIOutputParam<float> CellSizeParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		float CellSize = InstanceData->bCacheValid ? InstanceData->CellSize : 100.0f;
		CellSizeParam.SetAndAdvance(CellSize);
	}
}

void UNiagaraSpatialFusionDataInterface::GetWorldOrigin(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIOutputParam<FVector3f> OriginParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Origin = InstanceData->bCacheValid ? InstanceData->WorldOrigin : FVector::ZeroVector;
		OriginParam.SetAndAdvance(FVector3f(Origin));
	}
}

void UNiagaraSpatialFusionDataInterface::SampleInfluenceAtPositions(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<float> ValueParam(Context);

	// This is essentially the same as GetInfluenceValue but designed for batch operations
	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		float Value = DefaultValue;
		if (InstanceData->bCacheValid && InstanceData->CachedInfluenceMapManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);

			if (bUseInterpolation)
			{
				Value = SampleInfluenceValueInterpolated(GridPos, TEXT("Default"), InstanceData);
			}
			else
			{
				Value = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos, TEXT("Default"));
			}
		}

		ValueParam.SetAndAdvance(Value);
	}
}

void UNiagaraSpatialFusionDataInterface::SampleFlowAtPositions(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<FVector3f> DirectionParam(Context);

	// This is essentially the same as GetFlowDirection but designed for batch operations
	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		FVector Direction = FVector::ZeroVector;
		if (InstanceData->bCacheValid && InstanceData->CachedFlowFieldManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);

			if (bUseInterpolation)
			{
				Direction = SampleFlowDirectionInterpolated(GridPos, InstanceData);
			}
			else
			{
				Direction = InstanceData->CachedFlowFieldManager->GetFlowDirectionAtPosition(GridPos);
			}

			if (bNormalizeFlowDirections && !Direction.IsZero())
			{
				Direction.Normalize();
			}
		}

		DirectionParam.SetAndAdvance(FVector3f(Direction));
	}
}

void UNiagaraSpatialFusionDataInterface::GetInfluenceValueWithKey(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIInputParam<int32> KeyHashParam(Context); // Hash of the key name
	FNDIOutputParam<float> ValueParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());
		int32 KeyHash = KeyHashParam.GetAndAdvance();

		float Value = DefaultValue;
		if (InstanceData->bCacheValid && InstanceData->CachedInfluenceMapManager.IsValid())
		{
			FVector GridPos = bUseWorldSpace ? Position : WorldToGridPosition(Position, InstanceData);

			// Convert hash back to key name (simplified - in practice you'd maintain a hash->name mapping)
			FName KeyName = TEXT("Default");
			if (KeyHash != 0)
			{
				// This is a simplified approach - in practice you'd need a proper hash->name mapping
				KeyName = FName(*FString::Printf(TEXT("Key_%d"), KeyHash));
			}

			if (bUseInterpolation)
			{
				Value = SampleInfluenceValueInterpolated(GridPos, KeyName, InstanceData);
			}
			else
			{
				Value = InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(GridPos, KeyName);
			}
		}

		ValueParam.SetAndAdvance(Value);
	}
}

void UNiagaraSpatialFusionDataInterface::GetNearestGoalPosition(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<FVector3f> GoalPositionParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		FVector GoalPosition = Position; // Default to current position
		if (InstanceData->bCacheValid && InstanceData->CachedFlowFieldManager.IsValid())
		{
			// Get current goals and find nearest
			TArray<FFlowFieldGoal> Goals = InstanceData->CachedFlowFieldManager->GetCurrentGoals();
			float MinDistance = FLT_MAX;

			for (const FFlowFieldGoal& Goal : Goals)
			{
				if (Goal.IsValid())
				{
					float Distance = FVector::Distance(Position, Goal.WorldPosition);
					if (Distance < MinDistance)
					{
						MinDistance = Distance;
						GoalPosition = Goal.WorldPosition;
					}
				}
			}
		}

		GoalPositionParam.SetAndAdvance(FVector3f(GoalPosition));
	}
}

void UNiagaraSpatialFusionDataInterface::GetDistanceToNearestGoal(FVectorVMExternalFunctionContext& Context)
{
	VectorVM::FUserPtrHandler<FNiagaraSpatialFusionInstanceData> InstanceData(Context);
	FNDIInputParam<FVector3f> PositionParam(Context);
	FNDIOutputParam<float> DistanceParam(Context);

	for (int32 i = 0; i < Context.GetNumInstances(); ++i)
	{
		FVector Position = FVector(PositionParam.GetAndAdvance());

		float MinDistance = MaxFlowDistance;
		if (InstanceData->bCacheValid && InstanceData->CachedFlowFieldManager.IsValid())
		{
			// Get current goals and find nearest
			TArray<FFlowFieldGoal> Goals = InstanceData->CachedFlowFieldManager->GetCurrentGoals();

			for (const FFlowFieldGoal& Goal : Goals)
			{
				if (Goal.IsValid())
				{
					float Distance = FVector::Distance(Position, Goal.WorldPosition);
					MinDistance = FMath::Min(MinDistance, Distance);
				}
			}
		}

		DistanceParam.SetAndAdvance(MinDistance);
	}
}

//////////////////////////////////////////////////////////////////////////
// Helper Functions

FVector UNiagaraSpatialFusionDataInterface::WorldToGridPosition(const FVector& WorldPos, const FNiagaraSpatialFusionInstanceData* InstanceData) const
{
	if (!InstanceData->bCacheValid)
	{
		return WorldPos;
	}

	// Convert world position to grid position
	FVector GridPos = (WorldPos - InstanceData->WorldOrigin) / InstanceData->CellSize;
	return GridPos;
}

float UNiagaraSpatialFusionDataInterface::SampleInfluenceValueInterpolated(const FVector& Position, const FName& ValueKey, const FNiagaraSpatialFusionInstanceData* InstanceData) const
{
	if (!InstanceData->bCacheValid || !InstanceData->CachedInfluenceMapManager.IsValid())
	{
		return DefaultValue;
	}

	// For now, just use direct sampling - full interpolation would require access to grid data
	return InstanceData->CachedInfluenceMapManager->GetCellValueAtWorldPosition(Position, ValueKey);
}

FVector UNiagaraSpatialFusionDataInterface::SampleFlowDirectionInterpolated(const FVector& Position, const FNiagaraSpatialFusionInstanceData* InstanceData) const
{
	if (!InstanceData->bCacheValid || !InstanceData->CachedFlowFieldManager.IsValid())
	{
		return FVector::ZeroVector;
	}

	// For now, just use direct sampling - full interpolation would require access to flow field data
	return InstanceData->CachedFlowFieldManager->GetFlowDirectionAtPosition(Position);
}

bool UNiagaraSpatialFusionDataInterface::IsWithinGridBounds(const FVector& Position, const FNiagaraSpatialFusionInstanceData* InstanceData) const
{
	if (!InstanceData->bCacheValid)
	{
		return false;
	}

	FVector GridPos = bUseWorldSpace ? WorldToGridPosition(Position, InstanceData) : Position;

	return GridPos.X >= 0 && GridPos.X < InstanceData->GridDimensions.X &&
		   GridPos.Y >= 0 && GridPos.Y < InstanceData->GridDimensions.Y &&
		   GridPos.Z >= 0 && GridPos.Z < InstanceData->GridDimensions.Z;
}

FNiagaraFunctionSignature UNiagaraSpatialFusionDataInterface::GetFunctionSignature(const FName& FunctionName) const
{
	FNiagaraFunctionSignature Sig;
	Sig.Name = FunctionName;
	Sig.bMemberFunction = true;
	Sig.bRequiresContext = false;
	Sig.Inputs.Add(FNiagaraVariable(FNiagaraTypeDefinition(GetClass()), TEXT("SpatialFusion")));

	if (FunctionName == GetInfluenceValueName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Value"), FNiagaraTypeDefinition::GetFloatDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetInfluenceValueDesc", "Get influence value at position"));
	}
	else if (FunctionName == GetInfluenceGradientName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Gradient"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetInfluenceGradientDesc", "Get influence gradient at position"));
	}
	else if (FunctionName == GetFlowDirectionName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Direction"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetFlowDirectionDesc", "Get flow direction at position"));
	}
	else if (FunctionName == GetFlowDistanceName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Distance"), FNiagaraTypeDefinition::GetFloatDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetFlowDistanceDesc", "Get distance to nearest goal"));
	}
	else if (FunctionName == IsPositionBlockedName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("IsBlocked"), FNiagaraTypeDefinition::GetBoolDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "IsPositionBlockedDesc", "Check if position is blocked"));
	}
	else if (FunctionName == GetGridDimensionsName)
	{
		Sig.Outputs.Add(CreateOutputParam(TEXT("Dimensions"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetGridDimensionsDesc", "Get grid dimensions"));
	}
	else if (FunctionName == GetCellSizeName)
	{
		Sig.Outputs.Add(CreateOutputParam(TEXT("CellSize"), FNiagaraTypeDefinition::GetFloatDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetCellSizeDesc", "Get grid cell size"));
	}
	else if (FunctionName == GetWorldOriginName)
	{
		Sig.Outputs.Add(CreateOutputParam(TEXT("Origin"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetWorldOriginDesc", "Get grid world origin"));
	}
	else if (FunctionName == GetInfluenceValueWithKeyName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Inputs.Add(CreateInputParam(TEXT("KeyHash"), FNiagaraTypeDefinition::GetIntDef()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Value"), FNiagaraTypeDefinition::GetFloatDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetInfluenceValueWithKeyDesc", "Get influence value with specific key"));
	}
	else if (FunctionName == GetNearestGoalPositionName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("GoalPosition"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetNearestGoalPositionDesc", "Get nearest goal position"));
	}
	else if (FunctionName == GetDistanceToNearestGoalName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Distance"), FNiagaraTypeDefinition::GetFloatDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "GetDistanceToNearestGoalDesc", "Get distance to nearest goal"));
	}
	else if (FunctionName == SampleInfluenceAtPositionsName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Value"), FNiagaraTypeDefinition::GetFloatDef()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "SampleInfluenceAtPositionsDesc", "Sample influence at multiple positions"));
	}
	else if (FunctionName == SampleFlowAtPositionsName)
	{
		Sig.Inputs.Add(CreateInputParam(TEXT("Position"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.Outputs.Add(CreateOutputParam(TEXT("Direction"), FNiagaraTypeDefinition::GetVec3Def()));
		Sig.SetDescription(NSLOCTEXT("SpatialFusion", "SampleFlowAtPositionsDesc", "Sample flow at multiple positions"));
	}

	return Sig;
}

FNiagaraVariable UNiagaraSpatialFusionDataInterface::CreateInputParam(const FName& Name, const FNiagaraTypeDefinition& Type) const
{
	FNiagaraVariable Param(Type, Name);
	return Param;
}

FNiagaraVariable UNiagaraSpatialFusionDataInterface::CreateOutputParam(const FName& Name, const FNiagaraTypeDefinition& Type) const
{
	FNiagaraVariable Param(Type, Name);
	return Param;
}
