// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "FlowFieldManager.h"
#include "FlowFieldManager2D.generated.h"

/**
 * 2D Flow Field Manager
 * Specialized for 2D pathfinding scenarios like RTS games
 */
UCLASS(BlueprintType, Blueprintable)
class SPATIALFUSIONRUNTIME_API UFlowFieldManager2D : public UFlowFieldManagerBase
{
	GENERATED_BODY()

public:
	UFlowFieldManager2D();

	// UFlowFieldManagerBase interface
	virtual FFlowFieldResult CalculateFlowFieldInternal(const FFlowFieldQuery& Query) override;

	/**
	 * Get flow direction at 2D position
	 * @param Position2D - 2D position to query
	 * @return Flow direction as 2D vector
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	FVector2D GetFlowDirection2D(const FVector2D& Position2D) const;

	/**
	 * Get distance to nearest goal at 2D position
	 * @param Position2D - 2D position to query
	 * @return Distance to nearest goal
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	float GetDistance2D(const FVector2D& Position2D) const;

	/**
	 * Add a 2D goal
	 * @param Position2D - Goal position in 2D
	 * @param Weight - Goal weight
	 * @param Radius - Goal radius
	 * @param GoalTag - Goal identification tag
	 * @return True if goal was added
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	bool AddGoal2D(const FVector2D& Position2D, float Weight = 1.0f, float Radius = 1.0f, const FGameplayTag& GoalTag = FGameplayTag());

	/**
	 * Get flow field as 2D array for visualization
	 * @return 2D array of flow directions [Y][X]
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	TArray<TArray<FVector2D>> GetFlowField2D() const;

	/**
	 * Get distance field as 2D array for visualization
	 * @return 2D array of distances [Y][X]
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	TArray<TArray<float>> GetDistanceField2D() const;

	/**
	 * Calculate path from start to nearest goal
	 * @param StartPosition2D - Start position in 2D
	 * @param MaxPathLength - Maximum path length to calculate
	 * @return Array of positions forming the path
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	TArray<FVector2D> CalculatePath2D(const FVector2D& StartPosition2D, float MaxPathLength = 10000.0f) const;

	/**
	 * Get all positions within a certain distance of goals
	 * @param MaxDistance - Maximum distance from goals
	 * @return Array of positions within distance
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	TArray<FVector2D> GetPositionsWithinDistance2D(float MaxDistance) const;

	/**
	 * Sample flow field at multiple positions
	 * @param Positions - Positions to sample
	 * @return Array of flow directions at each position
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	TArray<FVector2D> SampleFlowField2D(const TArray<FVector2D>& Positions) const;

	/**
	 * Get flow field gradient at position (rate of change)
	 * @param Position2D - Position to calculate gradient at
	 * @return Gradient vector
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	FVector2D GetFlowGradient2D(const FVector2D& Position2D) const;

	/**
	 * Check if there's a clear path from start to any goal
	 * @param StartPosition2D - Start position
	 * @param MaxDistance - Maximum distance to check
	 * @return True if a clear path exists
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	bool HasClearPath2D(const FVector2D& StartPosition2D, float MaxDistance = 10000.0f) const;

	/**
	 * Get the optimal direction for group movement
	 * @param GroupPositions - Positions of all group members
	 * @param GroupRadius - Radius of the group
	 * @return Optimal movement direction for the group
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	FVector2D GetGroupMovementDirection2D(const TArray<FVector2D>& GroupPositions, float GroupRadius = 1.0f) const;

	/**
	 * Set movement cost in a rectangular area
	 * @param MinPosition - Minimum corner of the rectangle
	 * @param MaxPosition - Maximum corner of the rectangle
	 * @param Cost - Movement cost to set
	 * @return Number of cells affected
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	int32 SetMovementCostInArea2D(const FVector2D& MinPosition, const FVector2D& MaxPosition, float Cost);

	/**
	 * Set blocked state in a circular area
	 * @param Center - Center of the circle
	 * @param Radius - Radius of the circle
	 * @param bBlocked - Whether area should be blocked
	 * @return Number of cells affected
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	int32 SetBlockedInCircle2D(const FVector2D& Center, float Radius, bool bBlocked);

	/**
	 * Get flow field statistics
	 * @return Map of statistics
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field 2D")
	TMap<FName, float> GetFlowFieldStats2D() const;

protected:
	/** 2D flow field data */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field 2D")
	TArray<FFlowFieldCell> FlowField2D;

	/** Grid dimensions for 2D */
	UPROPERTY(BlueprintReadOnly, Category = "Flow Field 2D")
	FVector2D GridDimensions2D;

	/**
	 * Convert 2D position to grid index
	 * @param Position2D - 2D position
	 * @return Grid index
	 */
	int32 Position2DToIndex(const FVector2D& Position2D) const;

	/**
	 * Convert grid index to 2D position
	 * @param Index - Grid index
	 * @return 2D position
	 */
	FVector2D IndexToPosition2D(int32 Index) const;

	/**
	 * Check if 2D position is valid
	 * @param Position2D - Position to check
	 * @return True if valid
	 */
	bool IsValidPosition2D(const FVector2D& Position2D) const;

	/**
	 * Get 2D neighbors of a position
	 * @param Position2D - Center position
	 * @param bIncludeDiagonals - Whether to include diagonal neighbors
	 * @return Array of neighbor positions
	 */
	TArray<FVector2D> GetNeighbors2D(const FVector2D& Position2D, bool bIncludeDiagonals = true) const;

	/**
	 * Interpolate flow direction between grid cells
	 * @param Position2D - Position to interpolate at (can be fractional)
	 * @return Interpolated flow direction
	 */
	FVector2D InterpolateFlowDirection2D(const FVector2D& Position2D) const;

	/**
	 * Update 2D-specific performance metrics
	 */
	void UpdatePerformanceMetrics2D();
};
