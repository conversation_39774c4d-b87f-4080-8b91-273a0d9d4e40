{"Version": "1.2", "Data": {"Source": "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\spatialfusionruntime\\module.spatialfusionruntime.3.cpp", "ProvidedModule": "", "PCH": "e:\\unreal projects\\horizonbrigade\\ueproject\\intermediate\\build\\win64\\x64\\horizonbrigadeeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\spatialfusionruntime\\definitions.spatialfusionruntime.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\spatialfusionruntime.init.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\spatialfusiontypes.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\spatialfusiontypes.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\spatialfusiontypes.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\spatialfusionruntime\\permoduleinline.gen.cpp", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\private\\influencegrid2d.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\influencegrid2d.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\influencegridinterface.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\private\\influencegrid3d.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\influencegrid3d.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\private\\influencegridinterface.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\private\\spatialfusionruntime.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\spatialfusionruntime.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\source\\spatialfusionruntime\\public\\propagationstrategy.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\spatialfusion\\intermediate\\build\\win64\\unrealeditor\\inc\\spatialfusionruntime\\uht\\propagationstrategy.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}