// Copyright Epic Games, Inc. All Rights Reserved.

#include "FlowFieldAlgorithm.h"
#include "InfluenceMapManagerBase.h"
#include "HAL/PlatformFilemanager.h"

// Initialize static member
UFlowFieldAlgorithmRegistry* UFlowFieldAlgorithmRegistry::GlobalRegistry = nullptr;

//////////////////////////////////////////////////////////////////////////
// UFlowFieldAlgorithm

UFlowFieldAlgorithm::UFlowFieldAlgorithm()
{
}

FFlowFieldResult UFlowFieldAlgorithm::CalculateFlowField(
	UInfluenceMapManagerBase* GridManager,
	const FFlowFieldQuery& Query,
	TArray<FFlowFieldCell>& OutFlowField
)
{
	if (!GridManager)
	{
		FFlowFieldResult Result;
		Result.bSuccess = false;
		Result.ErrorMessage = TEXT("Invalid grid manager");
		return Result;
	}

	if (Query.Goals.Num() == 0)
	{
		FFlowFieldResult Result;
		Result.bSuccess = false;
		Result.ErrorMessage = TEXT("No goals specified");
		return Result;
	}

	double StartTime = FPlatformTime::Seconds();

	// Initialize flow field
	InitializeFlowField(GridManager, Query, OutFlowField);

	// Run algorithm implementation
	FFlowFieldResult Result = CalculateFlowFieldInternal(GridManager, Query, OutFlowField);

	// Calculate timing
	double EndTime = FPlatformTime::Seconds();
	Result.CalculationTime = (EndTime - StartTime) * 1000.0f; // Convert to milliseconds

	return Result;
}

float UFlowFieldAlgorithm::GetEstimatedComplexity(const FVector& GridSize, int32 NumGoals) const
{
	// Base complexity: O(N) where N is number of cells
	float TotalCells = GridSize.X * GridSize.Y * (GridSize.Z > 1 ? GridSize.Z : 1);
	return TotalCells * NumGoals;
}

void UFlowFieldAlgorithm::InitializeFlowField(
	UInfluenceMapManagerBase* GridManager,
	const FFlowFieldQuery& Query,
	TArray<FFlowFieldCell>& OutFlowField
)
{
	FVector GridDimensions = GridManager->GetGridDimensions();
	int32 TotalCells = GridDimensions.X * GridDimensions.Y * (GridDimensions.Z > 1 ? GridDimensions.Z : 1);

	// Initialize flow field array
	OutFlowField.SetNum(TotalCells);

	// Reset all cells
	for (int32 i = 0; i < TotalCells; i++)
	{
		OutFlowField[i].Reset();
		
		// Set movement cost and blocked state from grid
		FVector WorldPos = GridIndexToWorld(GridManager, i);
		OutFlowField[i].MovementCost = CalculateMovementCost(GridManager, WorldPos, Query.Params);
		OutFlowField[i].bIsBlocked = GridManager->IsCellBlockedAtWorldPosition(WorldPos);
	}

	// Set goal cells
	SetGoalCells(GridManager, Query.GetActiveGoals(), OutFlowField);
}

void UFlowFieldAlgorithm::SetGoalCells(
	UInfluenceMapManagerBase* GridManager,
	const TArray<FFlowFieldGoal>& Goals,
	TArray<FFlowFieldCell>& OutFlowField
)
{
	for (const FFlowFieldGoal& Goal : Goals)
	{
		int32 GoalIndex = WorldToGridIndex(GridManager, Goal.WorldPosition);
		if (GoalIndex >= 0 && GoalIndex < OutFlowField.Num())
		{
			OutFlowField[GoalIndex].bIsGoal = true;
			OutFlowField[GoalIndex].Distance = 0.0f;
			OutFlowField[GoalIndex].IntegrationValue = 0.0f;
		}
	}
}

float UFlowFieldAlgorithm::CalculateMovementCost(
	UInfluenceMapManagerBase* GridManager,
	const FVector& Position,
	const FFlowFieldParams& Params
) const
{
	switch (Params.CostMode)
	{
	case EFlowFieldCostMode::Uniform:
		return 1.0f;
		
	case EFlowFieldCostMode::Terrain:
		// Get terrain cost from grid (could be stored as influence value)
		return FMath::Max(1.0f, GridManager->GetCellValueAtWorldPosition(Position, TEXT("TerrainCost")));
		
	case EFlowFieldCostMode::Influence:
		// Get influence-based cost
		return FMath::Max(1.0f, GridManager->GetCellValueAtWorldPosition(Position, TEXT("MovementCost")));
		
	case EFlowFieldCostMode::Custom:
		// Custom cost calculation would be implemented by derived classes
		return 1.0f;
		
	default:
		return 1.0f;
	}
}

TArray<FVector> UFlowFieldAlgorithm::GetNeighbors(
	UInfluenceMapManagerBase* GridManager,
	const FVector& Position,
	bool bAllowDiagonal
) const
{
	TArray<FVector> Neighbors;

	if (GridManager->Is3DGrid())
	{
		// 3D neighbors
		TArray<FVector> Offsets = {
			FVector(1, 0, 0), FVector(-1, 0, 0),
			FVector(0, 1, 0), FVector(0, -1, 0),
			FVector(0, 0, 1), FVector(0, 0, -1)
		};

		if (bAllowDiagonal)
		{
			// Add diagonal neighbors (simplified - only edge diagonals)
			TArray<FVector> DiagonalOffsets = {
				FVector(1, 1, 0), FVector(1, -1, 0), FVector(-1, 1, 0), FVector(-1, -1, 0),
				FVector(1, 0, 1), FVector(1, 0, -1), FVector(-1, 0, 1), FVector(-1, 0, -1),
				FVector(0, 1, 1), FVector(0, 1, -1), FVector(0, -1, 1), FVector(0, -1, -1)
			};
			Offsets.Append(DiagonalOffsets);
		}

		for (const FVector& Offset : Offsets)
		{
			FVector NeighborPos = Position + Offset;
			if (IsValidPosition(GridManager, NeighborPos))
			{
				Neighbors.Add(NeighborPos);
			}
		}
	}
	else
	{
		// 2D neighbors
		TArray<FVector> Offsets = {
			FVector(1, 0, 0), FVector(-1, 0, 0),
			FVector(0, 1, 0), FVector(0, -1, 0)
		};

		if (bAllowDiagonal)
		{
			TArray<FVector> DiagonalOffsets = {
				FVector(1, 1, 0), FVector(1, -1, 0),
				FVector(-1, 1, 0), FVector(-1, -1, 0)
			};
			Offsets.Append(DiagonalOffsets);
		}

		for (const FVector& Offset : Offsets)
		{
			FVector NeighborPos = Position + Offset;
			if (IsValidPosition(GridManager, NeighborPos))
			{
				Neighbors.Add(NeighborPos);
			}
		}
	}

	return Neighbors;
}

float UFlowFieldAlgorithm::CalculateDistance(
	const FVector& From,
	const FVector& To,
	bool bAllowDiagonal
) const
{
	FVector Delta = To - From;
	
	if (bAllowDiagonal)
	{
		// Euclidean distance
		return Delta.Size();
	}
	else
	{
		// Manhattan distance
		return FMath::Abs(Delta.X) + FMath::Abs(Delta.Y) + FMath::Abs(Delta.Z);
	}
}

void UFlowFieldAlgorithm::GenerateFlowDirections(
	UInfluenceMapManagerBase* GridManager,
	TArray<FFlowFieldCell>& OutFlowField,
	const FFlowFieldParams& Params
)
{
	FVector GridDimensions = GridManager->GetGridDimensions();
	
	for (int32 i = 0; i < OutFlowField.Num(); i++)
	{
		if (OutFlowField[i].bIsBlocked || OutFlowField[i].bIsGoal)
		{
			continue;
		}

		FVector CurrentPos = GridIndexToWorld(GridManager, i);
		TArray<FVector> Neighbors = GetNeighbors(GridManager, CurrentPos, Params.bAllowDiagonal);

		FVector BestDirection = FVector::ZeroVector;
		float BestDistance = FLT_MAX;

		// Find neighbor with lowest integration value
		for (const FVector& NeighborPos : Neighbors)
		{
			int32 NeighborIndex = WorldToGridIndex(GridManager, NeighborPos);
			if (NeighborIndex >= 0 && NeighborIndex < OutFlowField.Num())
			{
				const FFlowFieldCell& NeighborCell = OutFlowField[NeighborIndex];
				if (!NeighborCell.bIsBlocked && NeighborCell.IntegrationValue < BestDistance)
				{
					BestDistance = NeighborCell.IntegrationValue;
					BestDirection = (NeighborPos - CurrentPos).GetSafeNormal();
				}
			}
		}

		OutFlowField[i].FlowDirection = BestDirection;
	}

	// Apply smoothing if requested
	if (Params.FlowSmoothingFactor > 0.0f)
	{
		SmoothFlowDirections(GridManager, OutFlowField, Params.FlowSmoothingFactor);
	}
}

void UFlowFieldAlgorithm::SmoothFlowDirections(
	UInfluenceMapManagerBase* GridManager,
	TArray<FFlowFieldCell>& OutFlowField,
	float SmoothingFactor
)
{
	TArray<FVector> SmoothedDirections;
	SmoothedDirections.SetNum(OutFlowField.Num());

	// Copy original directions
	for (int32 i = 0; i < OutFlowField.Num(); i++)
	{
		SmoothedDirections[i] = OutFlowField[i].FlowDirection;
	}

	// Apply smoothing
	for (int32 i = 0; i < OutFlowField.Num(); i++)
	{
		if (OutFlowField[i].bIsBlocked || OutFlowField[i].bIsGoal)
		{
			continue;
		}

		FVector CurrentPos = GridIndexToWorld(GridManager, i);
		TArray<FVector> Neighbors = GetNeighbors(GridManager, CurrentPos, true);

		FVector AverageDirection = OutFlowField[i].FlowDirection;
		int32 ValidNeighbors = 1;

		// Average with neighbor directions
		for (const FVector& NeighborPos : Neighbors)
		{
			int32 NeighborIndex = WorldToGridIndex(GridManager, NeighborPos);
			if (NeighborIndex >= 0 && NeighborIndex < OutFlowField.Num())
			{
				const FFlowFieldCell& NeighborCell = OutFlowField[NeighborIndex];
				if (!NeighborCell.bIsBlocked && !NeighborCell.FlowDirection.IsZero())
				{
					AverageDirection += NeighborCell.FlowDirection;
					ValidNeighbors++;
				}
			}
		}

		if (ValidNeighbors > 1)
		{
			AverageDirection /= ValidNeighbors;
			AverageDirection.Normalize();

			// Blend with original direction
			SmoothedDirections[i] = FMath::Lerp(
				OutFlowField[i].FlowDirection,
				AverageDirection,
				SmoothingFactor
			).GetSafeNormal();
		}
	}

	// Apply smoothed directions
	for (int32 i = 0; i < OutFlowField.Num(); i++)
	{
		OutFlowField[i].FlowDirection = SmoothedDirections[i];
	}
}

int32 UFlowFieldAlgorithm::WorldToGridIndex(
	UInfluenceMapManagerBase* GridManager,
	const FVector& WorldPosition
) const
{
	// This is a simplified implementation - actual conversion depends on grid type
	FVector GridDimensions = GridManager->GetGridDimensions();
	FVector GridPos = WorldPosition; // Assume world pos is already in grid coordinates for now

	int32 X = FMath::FloorToInt(GridPos.X);
	int32 Y = FMath::FloorToInt(GridPos.Y);
	int32 Z = FMath::FloorToInt(GridPos.Z);

	if (X < 0 || X >= GridDimensions.X || Y < 0 || Y >= GridDimensions.Y)
	{
		return -1;
	}

	if (GridManager->Is3DGrid())
	{
		if (Z < 0 || Z >= GridDimensions.Z)
		{
			return -1;
		}
		return Z * (GridDimensions.X * GridDimensions.Y) + Y * GridDimensions.X + X;
	}
	else
	{
		return Y * GridDimensions.X + X;
	}
}

FVector UFlowFieldAlgorithm::GridIndexToWorld(
	UInfluenceMapManagerBase* GridManager,
	int32 GridIndex
) const
{
	FVector GridDimensions = GridManager->GetGridDimensions();

	if (GridManager->Is3DGrid())
	{
		int32 LayerSize = GridDimensions.X * GridDimensions.Y;
		int32 Z = GridIndex / LayerSize;
		int32 Remainder = GridIndex % LayerSize;
		int32 Y = Remainder / (int32)GridDimensions.X;
		int32 X = Remainder % (int32)GridDimensions.X;
		return FVector(X, Y, Z);
	}
	else
	{
		int32 Y = GridIndex / (int32)GridDimensions.X;
		int32 X = GridIndex % (int32)GridDimensions.X;
		return FVector(X, Y, 0);
	}
}

bool UFlowFieldAlgorithm::IsValidPosition(
	UInfluenceMapManagerBase* GridManager,
	const FVector& Position
) const
{
	FVector GridDimensions = GridManager->GetGridDimensions();

	if (Position.X < 0 || Position.X >= GridDimensions.X ||
		Position.Y < 0 || Position.Y >= GridDimensions.Y)
	{
		return false;
	}

	if (GridManager->Is3DGrid())
	{
		return Position.Z >= 0 && Position.Z < GridDimensions.Z;
	}

	return true;
}

//////////////////////////////////////////////////////////////////////////
// UDijkstraFlowFieldAlgorithm

UDijkstraFlowFieldAlgorithm::UDijkstraFlowFieldAlgorithm()
{
}

bool UDijkstraFlowFieldAlgorithm::SupportsParameters(const FFlowFieldParams& Params) const
{
	// Dijkstra supports all parameter combinations
	return true;
}

float UDijkstraFlowFieldAlgorithm::GetEstimatedComplexity(const FVector& GridSize, int32 NumGoals) const
{
	// Dijkstra: O((V + E) log V) where V = cells, E = edges
	float TotalCells = GridSize.X * GridSize.Y * (GridSize.Z > 1 ? GridSize.Z : 1);
	float Edges = TotalCells * 8; // Approximate number of edges (8-connected)
	return (TotalCells + Edges) * FMath::Loge(TotalCells);
}

FFlowFieldResult UDijkstraFlowFieldAlgorithm::CalculateFlowFieldInternal(
	UInfluenceMapManagerBase* GridManager,
	const FFlowFieldQuery& Query,
	TArray<FFlowFieldCell>& OutFlowField
)
{
	FFlowFieldResult Result;
	Result.bSuccess = true;

	// Run Dijkstra algorithm
	int32 CellsProcessed = RunDijkstra(GridManager, Query.GetActiveGoals(), OutFlowField, Query.Params);

	// Generate flow directions from integration field
	GenerateFlowDirections(GridManager, OutFlowField, Query.Params);

	// Update result
	Result.CellsProcessed = CellsProcessed;
	Result.GoalsReached = Query.GetActiveGoals().Num();

	// Calculate max distance
	for (const FFlowFieldCell& Cell : OutFlowField)
	{
		if (Cell.Distance < FLT_MAX)
		{
			Result.MaxDistance = FMath::Max(Result.MaxDistance, Cell.Distance);
		}
	}

	return Result;
}

int32 UDijkstraFlowFieldAlgorithm::RunDijkstra(
	UInfluenceMapManagerBase* GridManager,
	const TArray<FFlowFieldGoal>& Goals,
	TArray<FFlowFieldCell>& OutFlowField,
	const FFlowFieldParams& Params
)
{
	// Priority queue for Dijkstra algorithm
	TArray<FDijkstraNode> OpenSet;
	TSet<int32> ClosedSet;

	// Initialize with goal cells
	for (const FFlowFieldGoal& Goal : Goals)
	{
		int32 GoalIndex = WorldToGridIndex(GridManager, Goal.WorldPosition);
		if (GoalIndex >= 0 && GoalIndex < OutFlowField.Num())
		{
			OpenSet.Add(FDijkstraNode(GoalIndex, 0.0f));
			OutFlowField[GoalIndex].IntegrationValue = 0.0f;
			OutFlowField[GoalIndex].Distance = 0.0f;
		}
	}

	// Make heap
	OpenSet.Heapify();

	int32 CellsProcessed = 0;

	// Main Dijkstra loop
	while (OpenSet.Num() > 0)
	{
		// Get node with minimum distance
		FDijkstraNode CurrentNode(0, 0.0f);
		OpenSet.HeapPop(CurrentNode);

		// Skip if already processed
		if (ClosedSet.Contains(CurrentNode.Index))
		{
			continue;
		}

		ClosedSet.Add(CurrentNode.Index);
		CellsProcessed++;

		// Check distance limit
		if (CurrentNode.Distance > Params.MaxDistance)
		{
			continue;
		}

		// Get current position
		FVector CurrentPos = GridIndexToWorld(GridManager, CurrentNode.Index);

		// Process neighbors
		TArray<FVector> Neighbors = GetNeighbors(GridManager, CurrentPos, Params.bAllowDiagonal);

		for (const FVector& NeighborPos : Neighbors)
		{
			int32 NeighborIndex = WorldToGridIndex(GridManager, NeighborPos);
			if (NeighborIndex < 0 || NeighborIndex >= OutFlowField.Num())
			{
				continue;
			}

			// Skip if already processed or blocked
			if (ClosedSet.Contains(NeighborIndex) || OutFlowField[NeighborIndex].bIsBlocked)
			{
				continue;
			}

			// Calculate movement cost
			float MovementCost = OutFlowField[NeighborIndex].MovementCost;
			float EdgeCost = CalculateDistance(CurrentPos, NeighborPos, Params.bAllowDiagonal);

			// Apply diagonal cost multiplier
			if (Params.bAllowDiagonal && EdgeCost > 1.1f) // Diagonal movement
			{
				EdgeCost *= Params.DiagonalCostMultiplier;
			}

			float NewDistance = CurrentNode.Distance + EdgeCost * MovementCost;

			// Update if we found a better path
			if (NewDistance < OutFlowField[NeighborIndex].IntegrationValue)
			{
				OutFlowField[NeighborIndex].IntegrationValue = NewDistance;
				OutFlowField[NeighborIndex].Distance = NewDistance;

				// Add to open set
				OpenSet.HeapPush(FDijkstraNode(NeighborIndex, NewDistance));
			}
		}
	}

	return CellsProcessed;
}

//////////////////////////////////////////////////////////////////////////
// UAStarFlowFieldAlgorithm

UAStarFlowFieldAlgorithm::UAStarFlowFieldAlgorithm()
{
}

bool UAStarFlowFieldAlgorithm::SupportsParameters(const FFlowFieldParams& Params) const
{
	// A* supports most parameters but works best with single goals
	return true;
}

float UAStarFlowFieldAlgorithm::GetEstimatedComplexity(const FVector& GridSize, int32 NumGoals) const
{
	// A* is typically faster than Dijkstra due to heuristic guidance
	float TotalCells = GridSize.X * GridSize.Y * (GridSize.Z > 1 ? GridSize.Z : 1);
	return TotalCells * FMath::Loge(TotalCells) * 0.7f; // Roughly 30% faster than Dijkstra
}

FFlowFieldResult UAStarFlowFieldAlgorithm::CalculateFlowFieldInternal(
	UInfluenceMapManagerBase* GridManager,
	const FFlowFieldQuery& Query,
	TArray<FFlowFieldCell>& OutFlowField
)
{
	// For now, A* implementation falls back to Dijkstra
	// A full A* implementation would require more complex heuristic handling for multiple goals
	return Super::CalculateFlowFieldInternal(GridManager, Query, OutFlowField);
}

float UAStarFlowFieldAlgorithm::CalculateHeuristic(
	const FVector& Position,
	const TArray<FFlowFieldGoal>& Goals
) const
{
	float MinHeuristic = FLT_MAX;

	for (const FFlowFieldGoal& Goal : Goals)
	{
		if (Goal.IsValid())
		{
			float Distance = FVector::Distance(Position, Goal.GridPosition);
			MinHeuristic = FMath::Min(MinHeuristic, Distance);
		}
	}

	return MinHeuristic == FLT_MAX ? 0.0f : MinHeuristic;
}

//////////////////////////////////////////////////////////////////////////
// UFlowFieldAlgorithmRegistry

UFlowFieldAlgorithmRegistry::UFlowFieldAlgorithmRegistry()
{
	InitializeDefaultAlgorithms();
}

void UFlowFieldAlgorithmRegistry::RegisterAlgorithm(EFlowFieldMethod Method, UFlowFieldAlgorithm* Algorithm)
{
	if (Algorithm)
	{
		Algorithms.Add(Method, Algorithm);
	}
}

UFlowFieldAlgorithm* UFlowFieldAlgorithmRegistry::GetAlgorithm(EFlowFieldMethod Method) const
{
	if (const TObjectPtr<UFlowFieldAlgorithm>* Algorithm = Algorithms.Find(Method))
	{
		return Algorithm->Get();
	}
	return nullptr;
}

TMap<EFlowFieldMethod, UFlowFieldAlgorithm*> UFlowFieldAlgorithmRegistry::GetAllAlgorithms() const
{
	TMap<EFlowFieldMethod, UFlowFieldAlgorithm*> Result;
	for (const auto& Pair : Algorithms)
	{
		Result.Add(Pair.Key, Pair.Value.Get());
	}
	return Result;
}

UFlowFieldAlgorithmRegistry* UFlowFieldAlgorithmRegistry::GetGlobalRegistry()
{
	if (!GlobalRegistry)
	{
		GlobalRegistry = NewObject<UFlowFieldAlgorithmRegistry>();
		GlobalRegistry->AddToRoot(); // Prevent garbage collection
	}
	return GlobalRegistry;
}

void UFlowFieldAlgorithmRegistry::InitializeDefaultAlgorithms()
{
	// Register default algorithms
	RegisterAlgorithm(EFlowFieldMethod::Dijkstra, NewObject<UDijkstraFlowFieldAlgorithm>());
	RegisterAlgorithm(EFlowFieldMethod::AStar, NewObject<UAStarFlowFieldAlgorithm>());
}
