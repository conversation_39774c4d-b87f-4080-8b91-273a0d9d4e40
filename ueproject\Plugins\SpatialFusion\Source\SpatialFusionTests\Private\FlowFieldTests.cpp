// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "FlowFieldTypes.h"
#include "FlowFieldManager.h"
#include "FlowFieldManager2D.h"
#include "FlowFieldAlgorithm.h"
#include "InfluenceMapManager2D.h"

/**
 * Test suite for flow field core functionality
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FFlowFieldCoreTest, "SpatialFusion.FlowField.Core", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FFlowFieldCoreTest::RunTest(const FString& Parameters)
{
	// Test FFlowFieldCell basic functionality
	{
		FFlowFieldCell Cell;
		TestEqual("Default distance should be FLT_MAX", Cell.Distance, FLT_MAX);
		TestEqual("Default integration value should be FLT_MAX", Cell.IntegrationValue, FLT_MAX);
		TestEqual("Default movement cost should be 1.0", Cell.MovementCost, 1.0f);
		TestFalse("Default blocked state should be false", Cell.bIsBlocked);
		TestFalse("Default goal state should be false", Cell.bIsGoal);
		
		Cell.Reset();
		TestEqual("Reset should set distance to FLT_MAX", Cell.Distance, FLT_MAX);
		TestEqual("Reset should set integration value to FLT_MAX", Cell.IntegrationValue, FLT_MAX);
		TestEqual("Reset should set flow direction to zero", Cell.FlowDirection, FVector::ZeroVector);
	}

	// Test FFlowFieldGoal functionality
	{
		FFlowFieldGoal Goal;
		TestFalse("Default goal should be invalid", Goal.IsValid());
		
		Goal.WorldPosition = FVector(100, 200, 0);
		Goal.GridPosition = FVector(1, 2, 0);
		Goal.Weight = 1.5f;
		Goal.Radius = 50.0f;
		Goal.bIsActive = true;
		
		TestTrue("Configured goal should be valid", Goal.IsValid());
		TestEqual("Goal weight should be 1.5", Goal.Weight, 1.5f);
		TestEqual("Goal radius should be 50", Goal.Radius, 50.0f);
		
		Goal.bIsActive = false;
		TestFalse("Inactive goal should be invalid", Goal.IsValid());
	}

	// Test FFlowFieldQuery functionality
	{
		FFlowFieldQuery Query;
		TestEqual("Default query should have no goals", Query.Goals.Num(), 0);
		TestEqual("Default method should be Dijkstra", Query.Params.Method, EFlowFieldMethod::Dijkstra);
		
		FFlowFieldGoal Goal1;
		Goal1.WorldPosition = FVector(100, 100, 0);
		Goal1.bIsActive = true;
		
		FFlowFieldGoal Goal2;
		Goal2.WorldPosition = FVector(200, 200, 0);
		Goal2.bIsActive = false;
		
		Query.Goals.Add(Goal1);
		Query.Goals.Add(Goal2);
		
		TArray<FFlowFieldGoal> ActiveGoals = Query.GetActiveGoals();
		TestEqual("Should have 1 active goal", ActiveGoals.Num(), 1);
		TestEqual("Active goal should be Goal1", ActiveGoals[0].WorldPosition, Goal1.WorldPosition);
	}

	return true;
}

/**
 * Test suite for flow field algorithms
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FFlowFieldAlgorithmTest, "SpatialFusion.FlowField.Algorithm", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FFlowFieldAlgorithmTest::RunTest(const FString& Parameters)
{
	// Create test grid manager
	UInfluenceMapManager2D* GridManager = NewObject<UInfluenceMapManager2D>();
	GridManager->Initialize(FVector(10, 10, 1), 100.0f, FVector::ZeroVector);

	// Test Dijkstra algorithm
	{
		UDijkstraFlowFieldAlgorithm* DijkstraAlgorithm = NewObject<UDijkstraFlowFieldAlgorithm>();
		
		// Create test query
		FFlowFieldQuery Query;
		Query.Params.Method = EFlowFieldMethod::Dijkstra;
		Query.Params.bAllowDiagonal = true;
		Query.Params.MaxDistance = 1000.0f;
		
		FFlowFieldGoal Goal;
		Goal.WorldPosition = FVector(500, 500, 0); // Center of 10x10 grid
		Goal.GridPosition = Goal.WorldPosition;
		Goal.bIsActive = true;
		Query.Goals.Add(Goal);
		
		TArray<FFlowFieldCell> FlowField;
		FFlowFieldResult Result = DijkstraAlgorithm->CalculateFlowField(GridManager, Query, FlowField);
		
		TestTrue("Dijkstra should succeed", Result.bSuccess);
		TestEqual("Should process cells", Result.CellsProcessed > 0, true);
		TestEqual("Should reach 1 goal", Result.GoalsReached, 1);
		TestTrue("Should have reasonable calculation time", Result.CalculationTime < 100.0f); // Less than 100ms
		
		// Check that goal cell has distance 0
		int32 GoalIndex = 5 * 10 + 5; // Center of 10x10 grid
		TestTrue("Goal index should be valid", GoalIndex < FlowField.Num());
		TestEqual("Goal cell should have distance 0", FlowField[GoalIndex].Distance, 0.0f);
		TestTrue("Goal cell should be marked as goal", FlowField[GoalIndex].bIsGoal);
		
		// Check that adjacent cells have reasonable distances
		int32 AdjacentIndex = 5 * 10 + 6; // One cell to the right
		TestTrue("Adjacent cell should have distance > 0", FlowField[AdjacentIndex].Distance > 0.0f);
		TestTrue("Adjacent cell should have distance < 200", FlowField[AdjacentIndex].Distance < 200.0f);
		
		// Check flow directions point toward goal
		FVector FlowDirection = FlowField[AdjacentIndex].FlowDirection;
		TestTrue("Flow direction should not be zero", !FlowDirection.IsZero());
		TestTrue("Flow direction should point toward goal", FlowDirection.X < 0.0f); // Should point left toward goal
	}

	// Test algorithm complexity estimation
	{
		UDijkstraFlowFieldAlgorithm* DijkstraAlgorithm = NewObject<UDijkstraFlowFieldAlgorithm>();
		
		float Complexity1 = DijkstraAlgorithm->GetEstimatedComplexity(FVector(10, 10, 1), 1);
		float Complexity2 = DijkstraAlgorithm->GetEstimatedComplexity(FVector(20, 20, 1), 1);
		
		TestTrue("Larger grid should have higher complexity", Complexity2 > Complexity1);
		
		float ComplexityMultiGoal = DijkstraAlgorithm->GetEstimatedComplexity(FVector(10, 10, 1), 3);
		TestTrue("Multiple goals should increase complexity", ComplexityMultiGoal > Complexity1);
	}

	// Test algorithm registry
	{
		UFlowFieldAlgorithmRegistry* Registry = UFlowFieldAlgorithmRegistry::GetGlobalRegistry();
		TestNotNull("Registry should exist", Registry);
		
		UFlowFieldAlgorithm* DijkstraFromRegistry = Registry->GetAlgorithm(EFlowFieldMethod::Dijkstra);
		TestNotNull("Dijkstra algorithm should be registered", DijkstraFromRegistry);
		TestTrue("Should be Dijkstra algorithm", DijkstraFromRegistry->IsA<UDijkstraFlowFieldAlgorithm>());
		
		UFlowFieldAlgorithm* AStarFromRegistry = Registry->GetAlgorithm(EFlowFieldMethod::AStar);
		TestNotNull("A* algorithm should be registered", AStarFromRegistry);
		
		TMap<EFlowFieldMethod, UFlowFieldAlgorithm*> AllAlgorithms = Registry->GetAllAlgorithms();
		TestTrue("Should have multiple algorithms", AllAlgorithms.Num() >= 2);
	}

	return true;
}

/**
 * Test suite for 2D flow field manager
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FFlowFieldManager2DTest, "SpatialFusion.FlowField.Manager2D", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FFlowFieldManager2DTest::RunTest(const FString& Parameters)
{
	// Create test components
	UInfluenceMapManager2D* GridManager = NewObject<UInfluenceMapManager2D>();
	GridManager->Initialize(FVector(20, 20, 1), 50.0f, FVector::ZeroVector);
	
	UFlowFieldManager2D* FlowFieldManager = NewObject<UFlowFieldManager2D>();
	FlowFieldManager->InitializeWithGrid(GridManager);
	
	// Test basic functionality
	{
		TestTrue("Manager should be initialized", FlowFieldManager->IsInitialized());
		
		// Add a goal
		bool bGoalAdded = FlowFieldManager->AddGoal2D(FVector2D(500, 500), 1.0f, 100.0f);
		TestTrue("Goal should be added successfully", bGoalAdded);
		
		TArray<FFlowFieldGoal> Goals = FlowFieldManager->GetCurrentGoals();
		TestEqual("Should have 1 goal", Goals.Num(), 1);
		TestEqual("Goal position should match", Goals[0].WorldPosition, FVector(500, 500, 0));
	}

	// Test flow field calculation
	{
		FFlowFieldQuery Query;
		Query.Goals = FlowFieldManager->GetCurrentGoals();
		Query.Params.Method = EFlowFieldMethod::Dijkstra;
		Query.Params.bAllowDiagonal = true;
		
		FFlowFieldResult Result = FlowFieldManager->CalculateFlowField(Query);
		TestTrue("Flow field calculation should succeed", Result.bSuccess);
		TestTrue("Should process cells", Result.CellsProcessed > 0);
		
		// Test flow direction queries
		FVector2D TestPos(300, 300);
		FVector2D FlowDirection = FlowFieldManager->GetFlowDirection2D(TestPos);
		TestTrue("Flow direction should not be zero", !FlowDirection.IsZero());
		
		float Distance = FlowFieldManager->GetDistance2D(TestPos);
		TestTrue("Distance should be reasonable", Distance > 0.0f && Distance < 1000.0f);
		
		// Test path calculation
		TArray<FVector2D> Path = FlowFieldManager->CalculatePath2D(TestPos, 1000.0f);
		TestTrue("Path should have multiple points", Path.Num() > 1);
		TestEqual("Path should start at test position", Path[0], TestPos);
		
		// Path should generally move toward goal
		FVector2D GoalPos(500, 500);
		float InitialDistance = FVector2D::Distance(Path[0], GoalPos);
		float FinalDistance = FVector2D::Distance(Path.Last(), GoalPos);
		TestTrue("Path should move toward goal", FinalDistance < InitialDistance);
	}

	// Test visualization data
	{
		TArray<TArray<FVector2D>> FlowField2D = FlowFieldManager->GetFlowField2D();
		TestEqual("Flow field should have correct height", FlowField2D.Num(), 20);
		if (FlowField2D.Num() > 0)
		{
			TestEqual("Flow field should have correct width", FlowField2D[0].Num(), 20);
		}
		
		TArray<TArray<float>> DistanceField2D = FlowFieldManager->GetDistanceField2D();
		TestEqual("Distance field should have correct height", DistanceField2D.Num(), 20);
		if (DistanceField2D.Num() > 0)
		{
			TestEqual("Distance field should have correct width", DistanceField2D[0].Num(), 20);
		}
	}

	// Test group movement
	{
		TArray<FVector2D> GroupPositions = {
			FVector2D(200, 200),
			FVector2D(220, 200),
			FVector2D(200, 220),
			FVector2D(220, 220)
		};
		
		FVector2D GroupDirection = FlowFieldManager->GetGroupMovementDirection2D(GroupPositions, 50.0f);
		TestTrue("Group direction should not be zero", !GroupDirection.IsZero());
		
		// Group direction should generally point toward goal
		FVector2D GroupCenter(210, 210);
		FVector2D ToGoal = (FVector2D(500, 500) - GroupCenter).GetSafeNormal();
		float DotProduct = FVector2D::DotProduct(GroupDirection, ToGoal);
		TestTrue("Group direction should align with goal direction", DotProduct > 0.5f);
	}

	// Test area operations
	{
		int32 CellsAffected = FlowFieldManager->SetMovementCostInArea2D(
			FVector2D(100, 100), FVector2D(200, 200), 5.0f);
		TestTrue("Should affect some cells", CellsAffected > 0);
		
		int32 BlockedCells = FlowFieldManager->SetBlockedInCircle2D(
			FVector2D(150, 150), 75.0f, true);
		TestTrue("Should block some cells", BlockedCells > 0);
		
		// Recalculate flow field and verify changes
		FFlowFieldQuery Query;
		Query.Goals = FlowFieldManager->GetCurrentGoals();
		Query.Params.Method = EFlowFieldMethod::Dijkstra;
		
		FFlowFieldResult Result = FlowFieldManager->CalculateFlowField(Query);
		TestTrue("Recalculation should succeed", Result.bSuccess);
	}

	// Test statistics
	{
		TMap<FName, float> Stats = FlowFieldManager->GetFlowFieldStats2D();
		TestTrue("Should have statistics", Stats.Num() > 0);
		TestTrue("Should have total cells stat", Stats.Contains(TEXT("TotalCells")));
		TestTrue("Should have valid cells stat", Stats.Contains(TEXT("ValidCells")));
		TestTrue("Should have blocked cells stat", Stats.Contains(TEXT("BlockedCells")));
		
		float TotalCells = Stats[TEXT("TotalCells")];
		TestEqual("Total cells should be 400", TotalCells, 400.0f); // 20x20 grid
	}

	return true;
}

/**
 * Performance test for flow field operations
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FFlowFieldPerformanceTest, "SpatialFusion.Performance.FlowField", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FFlowFieldPerformanceTest::RunTest(const FString& Parameters)
{
	// Test large grid performance
	{
		UInfluenceMapManager2D* LargeGridManager = NewObject<UInfluenceMapManager2D>();
		LargeGridManager->Initialize(FVector(100, 100, 1), 10.0f, FVector::ZeroVector);
		
		UFlowFieldManager2D* LargeFlowFieldManager = NewObject<UFlowFieldManager2D>();
		LargeFlowFieldManager->InitializeWithGrid(LargeGridManager);
		
		// Add multiple goals
		for (int32 i = 0; i < 5; i++)
		{
			FVector2D GoalPos(
				FMath::RandRange(100.0f, 900.0f),
				FMath::RandRange(100.0f, 900.0f)
			);
			LargeFlowFieldManager->AddGoal2D(GoalPos, 1.0f, 50.0f);
		}
		
		// Test calculation performance
		double StartTime = FPlatformTime::Seconds();
		
		FFlowFieldQuery Query;
		Query.Goals = LargeFlowFieldManager->GetCurrentGoals();
		Query.Params.Method = EFlowFieldMethod::Dijkstra;
		Query.Params.bAllowDiagonal = true;
		
		FFlowFieldResult Result = LargeFlowFieldManager->CalculateFlowField(Query);
		
		double CalculationTime = FPlatformTime::Seconds() - StartTime;
		
		TestTrue("Large grid calculation should succeed", Result.bSuccess);
		TestTrue("Large grid calculation should be reasonable", CalculationTime < 1.0); // Less than 1 second
		TestTrue("Should process many cells", Result.CellsProcessed > 5000);
		
		// Test query performance
		StartTime = FPlatformTime::Seconds();
		
		for (int32 i = 0; i < 1000; i++)
		{
			FVector2D QueryPos(
				FMath::RandRange(0.0f, 1000.0f),
				FMath::RandRange(0.0f, 1000.0f)
			);
			FVector2D FlowDir = LargeFlowFieldManager->GetFlowDirection2D(QueryPos);
			float Distance = LargeFlowFieldManager->GetDistance2D(QueryPos);
		}
		
		double QueryTime = FPlatformTime::Seconds() - StartTime;
		TestTrue("Bulk queries should be fast", QueryTime < 0.1); // Less than 100ms for 1000 queries
	}

	return true;
}
