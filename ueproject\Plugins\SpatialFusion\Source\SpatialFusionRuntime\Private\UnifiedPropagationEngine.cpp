// Copyright Epic Games, Inc. All Rights Reserved.

#include "UnifiedPropagationEngine.h"
#include "InfluenceMapManagerBase.h"
#include "PropagationStrategyRegistry.h"
#include "PropagationRuleEngine.h"
#include "InfluenceMapManager2D.h"
#include "InfluenceMapManager3D.h"
#include "Engine/World.h"
#include "HAL/PlatformFilemanager.h"

UUnifiedPropagationEngine::UUnifiedPropagationEngine()
{
	bIsEnabled = true;
	bUseOptimizations = true;
	MaxExecutionTimePerFrame = 5.0f;
	bEnableParallelExecution = true;
	MinRulesForParallelExecution = 4;
	CurrentSimulationTime = 0.0f;
	
	// 获取全局策略注册表
	StrategyRegistry = UPropagationStrategyRegistry::GetGlobalRegistry();
}

FRuleExecutionStats UUnifiedPropagationEngine::ExecuteRulesForAllCells(float DeltaTime)
{
	FRuleExecutionStats Stats;
	
	if (!bIsEnabled || !GridManager)
	{
		return Stats;
	}

	// 更新模拟时间
	CurrentSimulationTime += DeltaTime;
	
	// 记录开始时间
	double StartTime = FPlatformTime::Seconds();
	
	// 获取所有激活的规则
	TArray<FUnifiedPropagationRule*> ActiveRules = CurrentRuleSet.GetActiveRules();
	
	if (ActiveRules.Num() == 0)
	{
		return Stats;
	}

	// 获取所有需要处理的单元格位置
	TArray<FVector> AllCellPositions;
	// TODO: 从GridManager获取所有单元格位置
	// GridManager->GetAllCellPositions(AllCellPositions);
	
	// 临时实现：生成一些测试位置
	for (int32 X = 0; X < 10; X++)
	{
		for (int32 Y = 0; Y < 10; Y++)
		{
			AllCellPositions.Add(FVector(X * 100.0f, Y * 100.0f, 0.0f));
		}
	}

	// 决定是否使用并行执行
	bool bUseParallel = bEnableParallelExecution && ActiveRules.Num() >= MinRulesForParallelExecution;
	
	if (bUseParallel)
	{
		Stats = ExecuteRulesParallel(ActiveRules, AllCellPositions, DeltaTime);
	}
	else
	{
		Stats = ExecuteRulesSerial(ActiveRules, AllCellPositions, DeltaTime);
	}

	// 更新性能指标
	UpdatePerformanceMetrics(DeltaTime, Stats);
	
	// 保存执行统计
	LastExecutionStats = Stats;
	
	return Stats;
}

FRuleExecutionStats UUnifiedPropagationEngine::ExecuteRulesForCells(const TArray<FVector>& CellPositions, float DeltaTime)
{
	FRuleExecutionStats Stats;
	
	if (!bIsEnabled || !GridManager || CellPositions.Num() == 0)
	{
		return Stats;
	}

	// 更新模拟时间
	CurrentSimulationTime += DeltaTime;
	
	// 获取所有激活的规则
	TArray<FUnifiedPropagationRule*> ActiveRules = CurrentRuleSet.GetActiveRules();
	
	if (ActiveRules.Num() == 0)
	{
		return Stats;
	}

	// 决定是否使用并行执行
	bool bUseParallel = bEnableParallelExecution && ActiveRules.Num() >= MinRulesForParallelExecution;
	
	if (bUseParallel)
	{
		Stats = ExecuteRulesParallel(ActiveRules, CellPositions, DeltaTime);
	}
	else
	{
		Stats = ExecuteRulesSerial(ActiveRules, CellPositions, DeltaTime);
	}

	// 更新性能指标
	UpdatePerformanceMetrics(DeltaTime, Stats);
	
	// 保存执行统计
	LastExecutionStats = Stats;
	
	return Stats;
}

int32 UUnifiedPropagationEngine::ExecuteRulesForCell(const FVector& CellPosition, float DeltaTime)
{
	if (!bIsEnabled || !GridManager)
	{
		return 0;
	}

	// 更新模拟时间
	CurrentSimulationTime += DeltaTime;
	
	// 创建传播上下文
	FPropagationContext Context = CreatePropagationContext(CellPosition, DeltaTime);
	
	// 获取所有激活的规则
	TArray<FUnifiedPropagationRule*> ActiveRules = CurrentRuleSet.GetActiveRules();
	
	int32 ExecutedRules = 0;
	
	// 执行每个规则
	for (FUnifiedPropagationRule* Rule : ActiveRules)
	{
		if (Rule && ExecuteRule(*Rule, Context))
		{
			ExecutedRules++;
		}
		
		// 检查是否需要限制执行时间
		if (bUseOptimizations && ShouldThrottleExecution(FPlatformTime::Seconds()))
		{
			break;
		}
	}
	
	// 应用上下文变更到网格
	ApplyContextChangesToGrid(Context);
	
	return ExecutedRules;
}

bool UUnifiedPropagationEngine::ExecuteRule(const FUnifiedPropagationRule& Rule, FPropagationContext& Context)
{
	if (!Rule.bIsActive)
	{
		return false;
	}

	// 检查规则是否应该更新
	if (!Rule.ShouldUpdate(CurrentSimulationTime, Context.DeltaTime))
	{
		return false;
	}

	// 检查规则是否可以在当前位置执行
	if (!Rule.CanExecuteAtPosition(Context.CellPosition, CurrentSimulationTime))
	{
		return false;
	}

	// 检查执行缓存
	bool CachedResult;
	if (bUseOptimizations && CheckExecutionCache(Rule, Context, CachedResult))
	{
		return CachedResult;
	}

	// 评估规则条件
	if (!EvaluateRuleConditions(Rule, Context))
	{
		// 更新缓存
		if (bUseOptimizations)
		{
			UpdateExecutionCache(Rule, Context, false);
		}
		return false;
	}

	// 执行规则动作
	int32 ActionsExecuted = ExecuteRuleActions(Rule, Context);
	
	bool bExecutionSuccess = ActionsExecuted > 0;
	
	// 更新执行统计
	if (bExecutionSuccess)
	{
		const_cast<FUnifiedPropagationRule&>(Rule).UpdateExecutionStats(Context.CellPosition, CurrentSimulationTime);
		
		// 更新规则执行计数
		int32& Count = RuleExecutionCounts.FindOrAdd(Rule.RuleName);
		Count++;
	}

	// 更新缓存
	if (bUseOptimizations)
	{
		UpdateExecutionCache(Rule, Context, bExecutionSuccess);
	}

	return bExecutionSuccess;
}

void UUnifiedPropagationEngine::SetRuleSet(const FUnifiedPropagationRuleSet& NewRuleSet)
{
	CurrentRuleSet = NewRuleSet;
	
	// 清除缓存
	if (bUseOptimizations)
	{
		ClearExecutionCache();
	}
	
	// 重置执行统计
	RuleExecutionCounts.Empty();
}

bool UUnifiedPropagationEngine::AddRule(const FUnifiedPropagationRule& Rule)
{
	bool bSuccess = CurrentRuleSet.AddRule(Rule);
	
	if (bSuccess && bUseOptimizations)
	{
		// 清除相关缓存
		ClearExecutionCache();
	}
	
	return bSuccess;
}

bool UUnifiedPropagationEngine::RemoveRule(const FName& RuleName)
{
	bool bSuccess = CurrentRuleSet.RemoveRule(RuleName);
	
	if (bSuccess)
	{
		// 清除相关缓存和统计
		if (bUseOptimizations)
		{
			ClearExecutionCache();
		}
		RuleExecutionCounts.Remove(RuleName);
	}
	
	return bSuccess;
}

bool UUnifiedPropagationEngine::SetRuleEnabled(const FName& RuleName, bool bEnabled)
{
	FUnifiedPropagationRule* Rule = CurrentRuleSet.FindRule(RuleName);
	if (Rule)
	{
		Rule->bIsActive = bEnabled;
		
		// 清除相关缓存
		if (bUseOptimizations)
		{
			ClearExecutionCache();
		}
		
		return true;
	}
	
	return false;
}

bool UUnifiedPropagationEngine::GetRule(const FName& RuleName, FUnifiedPropagationRule& OutRule) const
{
	const FUnifiedPropagationRule* Rule = const_cast<FUnifiedPropagationRuleSet&>(CurrentRuleSet).FindRule(RuleName);
	if (Rule)
	{
		OutRule = *Rule;
		return true;
	}
	
	return false;
}

TArray<FUnifiedPropagationRule> UUnifiedPropagationEngine::GetActiveRules() const
{
	TArray<FUnifiedPropagationRule*> ActiveRulePointers = const_cast<FUnifiedPropagationRuleSet&>(CurrentRuleSet).GetActiveRules();
	TArray<FUnifiedPropagationRule> ActiveRules;
	
	for (FUnifiedPropagationRule* RulePtr : ActiveRulePointers)
	{
		if (RulePtr)
		{
			ActiveRules.Add(*RulePtr);
		}
	}
	
	return ActiveRules;
}

bool UUnifiedPropagationEngine::CanRuleExecuteForCell(const FUnifiedPropagationRule& Rule, const FVector& CellPosition) const
{
	if (!Rule.bIsActive)
	{
		return false;
	}

	// 检查规则是否应该更新
	if (!Rule.ShouldUpdate(CurrentSimulationTime, 0.0f))
	{
		return false;
	}

	// 检查规则是否可以在当前位置执行
	if (!Rule.CanExecuteAtPosition(CellPosition, CurrentSimulationTime))
	{
		return false;
	}

	// 创建预览上下文
	FPropagationContext Context = CreatePropagationContext(CellPosition, 0.0f);
	Context.bIsPreview = true;

	// 评估规则条件
	return EvaluateRuleConditions(Rule, Context);
}

FPropagationContext UUnifiedPropagationEngine::PreviewRuleExecution(const FVector& CellPosition, float DeltaTime) const
{
	FPropagationContext Context = CreatePropagationContext(CellPosition, DeltaTime);
	Context.bIsPreview = true;
	
	return Context;
}

TMap<FName, float> UUnifiedPropagationEngine::GetPerformanceMetrics() const
{
	FScopeLock Lock(&ExecutionMutex);
	return PerformanceMetrics;
}

void UUnifiedPropagationEngine::ClearExecutionCache()
{
	FScopeLock Lock(&ExecutionMutex);
	ExecutionCache.Empty();
}

void UUnifiedPropagationEngine::ClearPerformanceStats()
{
	FScopeLock Lock(&ExecutionMutex);
	PerformanceMetrics.Empty();
	RuleExecutionCounts.Empty();
	LastExecutionTimes.Empty();
}

// === 内部实现方法 ===

FPropagationContext UUnifiedPropagationEngine::CreatePropagationContext(const FVector& CellPosition, float DeltaTime) const
{
	FPropagationContext Context;

	// 基础信息
	Context.CellPosition = CellPosition;
	Context.DeltaTime = DeltaTime;
	Context.CurrentTime = CurrentSimulationTime;
	Context.GridManager = GridManager;

	// 获取当前单元格的值和标签
	if (GridManager)
	{
		// TODO: 从GridManager获取单元格数据
		// GridManager->GetCellValues(CellPosition, Context.CellValues);
		// GridManager->GetCellTags(CellPosition, Context.CellTags);

		// 临时实现：设置一些默认值
		Context.CellValues.Add(TEXT("Temperature"), 25.0f);
		Context.CellValues.Add(TEXT("Pressure"), 1.0f);
	}

	// 获取邻居信息
	// TODO: 从GridManager获取邻居数据
	// GridManager->GetNeighborPositions(CellPosition, Context.NeighborPositions);
	// GridManager->GetNeighborValues(CellPosition, Context.NeighborValues);
	// GridManager->GetNeighborTags(CellPosition, Context.NeighborTags);

	// 临时实现：生成一些邻居位置
	TArray<FVector> Directions = {
		FVector(1, 0, 0), FVector(-1, 0, 0), FVector(0, 1, 0), FVector(0, -1, 0),
		FVector(1, 1, 0), FVector(-1, 1, 0), FVector(1, -1, 0), FVector(-1, -1, 0)
	};

	for (const FVector& Dir : Directions)
	{
		Context.NeighborPositions.Add(CellPosition + Dir * 100.0f);

		FInfluenceCellData NeighborData;
		NeighborData.Values.Add(TEXT("Temperature"), 20.0f);
		NeighborData.Values.Add(TEXT("Pressure"), 0.9f);
		Context.NeighborValues.Add(NeighborData);

		Context.NeighborTags.Add(FGameplayTagContainer());
	}

	return Context;
}

void UUnifiedPropagationEngine::ApplyContextChangesToGrid(const FPropagationContext& Context)
{
	if (!GridManager || Context.bIsPreview)
	{
		return;
	}

	// TODO: 将上下文中的变更应用到网格
	// GridManager->SetCellValues(Context.CellPosition, Context.CellValues);
	// GridManager->SetCellTags(Context.CellPosition, Context.CellTags);
}

bool UUnifiedPropagationEngine::EvaluateRuleConditions(const FUnifiedPropagationRule& Rule, const FPropagationContext& Context) const
{
	// 如果没有条件，默认为true
	if (Rule.Conditions.Num() == 0)
	{
		return true;
	}

	bool bResult = Rule.bUseAndLogic; // AND逻辑从true开始，OR逻辑从false开始

	for (const FPropagationCondition& Condition : Rule.Conditions)
	{
		bool bConditionResult = Condition.EvaluateCondition(Context);

		if (Rule.bUseAndLogic)
		{
			bResult = bResult && bConditionResult;
			// AND逻辑：如果任何条件为false，可以提前退出
			if (!bResult)
			{
				break;
			}
		}
		else
		{
			bResult = bResult || bConditionResult;
			// OR逻辑：如果任何条件为true，可以提前退出
			if (bResult)
			{
				break;
			}
		}
	}

	return bResult;
}

int32 UUnifiedPropagationEngine::ExecuteRuleActions(const FUnifiedPropagationRule& Rule, FPropagationContext& Context) const
{
	int32 ActionsExecuted = 0;

	// 如果有自定义动作，执行它们
	if (Rule.Actions.Num() > 0)
	{
		for (const FPropagationAction& Action : Rule.Actions)
		{
			if (Action.ExecuteAction(Context))
			{
				ActionsExecuted++;

				// 如果不是执行所有动作，执行第一个成功的就退出
				if (!Rule.bExecuteAllActions)
				{
					break;
				}
			}
		}
	}
	else
	{
		// 如果没有自定义动作，使用传统的传播策略
		IPropagationStrategy* Strategy = GetStrategyForRule(Rule);
		if (Strategy)
		{
			// 转换为旧格式进行兼容
			FInfluencePropagationRuleData LegacyRule = Rule.ToRuleData();
			int32 CellsAffected = Strategy->ExecutePropagation(LegacyRule, GridManager, Context.DeltaTime);
			ActionsExecuted = CellsAffected > 0 ? 1 : 0;
		}
	}

	return ActionsExecuted;
}

IPropagationStrategy* UUnifiedPropagationEngine::GetStrategyForRule(const FUnifiedPropagationRule& Rule) const
{
	if (!StrategyRegistry)
	{
		return nullptr;
	}

	// 根据传播模式获取对应的策略
	switch (Rule.PropagationMode)
	{
	case ESpatialPropagationMode::Diffusion:
		return StrategyRegistry->GetStrategy(TEXT("Diffusion"));

	case ESpatialPropagationMode::Directional:
		return StrategyRegistry->GetStrategy(TEXT("Directional"));

	case ESpatialPropagationMode::Radial:
		return StrategyRegistry->GetStrategy(TEXT("Radial"));

	case ESpatialPropagationMode::Custom:
		// 尝试根据规则标签获取自定义策略
		if (Rule.RuleTag.IsValid())
		{
			return StrategyRegistry->GetStrategyByTag(Rule.RuleTag);
		}
		break;

	default:
		break;
	}

	// 返回默认的扩散策略
	return StrategyRegistry->GetStrategy(TEXT("Diffusion"));
}

FRuleExecutionStats UUnifiedPropagationEngine::ExecuteRulesParallel(const TArray<FUnifiedPropagationRule*>& Rules, const TArray<FVector>& CellPositions, float DeltaTime)
{
	FRuleExecutionStats Stats;
	Stats.TotalExecutionTime = 0.0f;
	Stats.TotalRulesExecuted = 0;
	Stats.CellsProcessed = CellPositions.Num();
	Stats.RuleFailures = 0;

	// TODO: 实现真正的并行执行
	// 目前使用串行执行作为临时实现
	return ExecuteRulesSerial(Rules, CellPositions, DeltaTime);
}

FRuleExecutionStats UUnifiedPropagationEngine::ExecuteRulesSerial(const TArray<FUnifiedPropagationRule*>& Rules, const TArray<FVector>& CellPositions, float DeltaTime)
{
	FRuleExecutionStats Stats;
	double StartTime = FPlatformTime::Seconds();

	Stats.CellsProcessed = CellPositions.Num();
	Stats.TotalRulesExecuted = 0;
	Stats.RuleFailures = 0;
	Stats.ConditionEvaluations = 0;
	Stats.ActionExecutions = 0;

	// 为每个单元格执行所有规则
	for (const FVector& CellPosition : CellPositions)
	{
		// 创建传播上下文
		FPropagationContext Context = CreatePropagationContext(CellPosition, DeltaTime);

		// 执行每个规则
		for (FUnifiedPropagationRule* Rule : Rules)
		{
			if (!Rule)
			{
				continue;
			}

			Stats.ConditionEvaluations++;

			if (ExecuteRule(*Rule, Context))
			{
				Stats.TotalRulesExecuted++;
				Stats.ActionExecutions++;
			}
			else
			{
				Stats.RuleFailures++;
			}

			// 检查是否需要限制执行时间
			if (bUseOptimizations && ShouldThrottleExecution(StartTime))
			{
				break;
			}
		}

		// 应用上下文变更到网格
		ApplyContextChangesToGrid(Context);

		// 检查是否需要限制执行时间
		if (bUseOptimizations && ShouldThrottleExecution(StartTime))
		{
			break;
		}
	}

	// 计算总执行时间
	double EndTime = FPlatformTime::Seconds();
	Stats.TotalExecutionTime = (EndTime - StartTime) * 1000.0f; // 转换为毫秒

	// 计算平均执行时间
	if (Stats.TotalRulesExecuted > 0)
	{
		Stats.AverageExecutionTimePerRule = Stats.TotalExecutionTime / Stats.TotalRulesExecuted;
	}

	return Stats;
}

bool UUnifiedPropagationEngine::ShouldThrottleExecution(double StartTime) const
{
	double CurrentTime = FPlatformTime::Seconds();
	double ElapsedTime = (CurrentTime - StartTime) * 1000.0; // 转换为毫秒

	return ElapsedTime >= MaxExecutionTimePerFrame;
}

void UUnifiedPropagationEngine::UpdatePerformanceMetrics(float DeltaTime, const FRuleExecutionStats& Stats)
{
	FScopeLock Lock(&ExecutionMutex);

	PerformanceMetrics.Add(TEXT("FrameTime"), DeltaTime * 1000.0f); // 转换为毫秒
	PerformanceMetrics.Add(TEXT("ExecutionTime"), Stats.TotalExecutionTime);
	PerformanceMetrics.Add(TEXT("RulesExecuted"), static_cast<float>(Stats.TotalRulesExecuted));
	PerformanceMetrics.Add(TEXT("CellsProcessed"), static_cast<float>(Stats.CellsProcessed));
	PerformanceMetrics.Add(TEXT("RuleFailures"), static_cast<float>(Stats.RuleFailures));
	PerformanceMetrics.Add(TEXT("ConditionEvaluations"), static_cast<float>(Stats.ConditionEvaluations));
	PerformanceMetrics.Add(TEXT("ActionExecutions"), static_cast<float>(Stats.ActionExecutions));
	PerformanceMetrics.Add(TEXT("AverageExecutionTimePerRule"), Stats.AverageExecutionTimePerRule);

	// 计算执行效率
	if (Stats.CellsProcessed > 0)
	{
		float CellsPerSecond = DeltaTime > 0.0f ? Stats.CellsProcessed / DeltaTime : 0.0f;
		PerformanceMetrics.Add(TEXT("CellsPerSecond"), CellsPerSecond);
	}

	// 计算规则成功率
	int32 TotalAttempts = Stats.TotalRulesExecuted + Stats.RuleFailures;
	if (TotalAttempts > 0)
	{
		float SuccessRate = static_cast<float>(Stats.TotalRulesExecuted) / TotalAttempts;
		PerformanceMetrics.Add(TEXT("RuleSuccessRate"), SuccessRate);
	}
}

uint32 UUnifiedPropagationEngine::CalculateContextHash(const FPropagationContext& Context) const
{
	uint32 Hash = 0;

	// 位置哈希
	Hash = HashCombine(Hash, GetTypeHash(Context.CellPosition));

	// 值哈希
	for (const auto& ValuePair : Context.CellValues)
	{
		Hash = HashCombine(Hash, GetTypeHash(ValuePair.Key));
		Hash = HashCombine(Hash, GetTypeHash(ValuePair.Value));
	}

	// 标签哈希
	Hash = HashCombine(Hash, GetTypeHash(Context.CellTags));

	// 时间哈希（量化到0.1秒精度以提高缓存命中率）
	float QuantizedTime = FMath::FloorToFloat(Context.CurrentTime * 10.0f) / 10.0f;
	Hash = HashCombine(Hash, GetTypeHash(QuantizedTime));

	return Hash;
}

bool UUnifiedPropagationEngine::CheckExecutionCache(const FUnifiedPropagationRule& Rule, const FPropagationContext& Context, bool& OutCachedResult) const
{
	uint32 ContextHash = CalculateContextHash(Context);
	uint32 CacheKey = HashCombine(GetTypeHash(Rule.RuleName), ContextHash);

	const FRuleExecutionCacheEntry* CacheEntry = ExecutionCache.Find(CacheKey);
	if (CacheEntry)
	{
		// 检查缓存是否仍然有效
		float CacheAge = CurrentSimulationTime - CacheEntry->CacheTime;
		if (CacheAge <= CacheEntry->CacheDuration && CacheEntry->ContextHash == ContextHash)
		{
			OutCachedResult = CacheEntry->bResult;
			return true;
		}
	}

	return false;
}

void UUnifiedPropagationEngine::UpdateExecutionCache(const FUnifiedPropagationRule& Rule, const FPropagationContext& Context, bool Result)
{
	uint32 ContextHash = CalculateContextHash(Context);
	uint32 CacheKey = HashCombine(GetTypeHash(Rule.RuleName), ContextHash);

	FRuleExecutionCacheEntry& CacheEntry = ExecutionCache.FindOrAdd(CacheKey);
	CacheEntry.bResult = Result;
	CacheEntry.CacheTime = CurrentSimulationTime;
	CacheEntry.CacheDuration = 0.1f; // 缓存0.1秒
	CacheEntry.ContextHash = ContextHash;
}
