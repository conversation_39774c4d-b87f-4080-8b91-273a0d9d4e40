// Copyright Epic Games, Inc. All Rights Reserved.

#include "InfluenceMapManagerBase.h"
#include "PropagationRuleEngine.h"
#include "PropagationRuleSet.h"
#include "PropagationStrategyRegistry.h"
#include "Engine/World.h"

UInfluenceMapManagerBase::UInfluenceMapManagerBase()
{
	bIsPropagationPaused = false;
	bIsInitialized = false;
	LastUpdateTime = 0.0f;
	LastFrameCellUpdates = 0;
	RuleEngine = nullptr;
	
	// Get the global strategy registry
	StrategyRegistry = UPropagationStrategyRegistry::GetGlobalRegistry();
}

void UInfluenceMapManagerBase::UpdatePropagation(float DeltaTime)
{
	if (bIsPropagationPaused || !bIsInitialized || !RuleEngine)
	{
		return;
	}

	// Update performance tracking
	double StartTime = FPlatformTime::Seconds();

	// Execute rules using the unified rule engine
	FRuleExecutionStats Stats = RuleEngine->ExecuteRulesForAllCells(DeltaTime);

	// Update performance metrics
	LastFrameCellUpdates = Stats.CellsProcessed;
	UpdatePerformanceMetrics(DeltaTime);

	// Broadcast update event
	OnPropagationUpdated.Broadcast(DeltaTime, Stats.CellsProcessed);

	// Update last update time
	LastUpdateTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
}

void UInfluenceMapManagerBase::SetRuleEngine(UPropagationRuleEngine* InRuleEngine)
{
	RuleEngine = InRuleEngine;
	
	// Set this influence map as the grid manager for the rule engine
	if (RuleEngine)
	{
		RuleEngine->GridManager = this;
	}
}

bool UInfluenceMapManagerBase::SetRuleSet(UPropagationRuleSet* RuleSet)
{
	if (!RuleEngine)
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot set rule set: No rule engine assigned"));
		return false;
	}

	RuleEngine->SetRuleSet(RuleSet);
	return true;
}

void UInfluenceMapManagerBase::UpdatePerformanceMetrics(float DeltaTime)
{
	// Update basic performance metrics
	PerformanceMetrics.Add(TEXT("LastUpdateTime"), LastUpdateTime);
	PerformanceMetrics.Add(TEXT("LastFrameCellUpdates"), static_cast<float>(LastFrameCellUpdates));
	PerformanceMetrics.Add(TEXT("DeltaTime"), DeltaTime);
	
	// Add rule engine metrics if available
	if (RuleEngine)
	{
		FRuleExecutionStats Stats = RuleEngine->GetExecutionStatistics();
		PerformanceMetrics.Add(TEXT("TotalRulesExecuted"), static_cast<float>(Stats.TotalRulesExecuted));
		PerformanceMetrics.Add(TEXT("TotalExecutionTime"), Stats.TotalExecutionTime);
		PerformanceMetrics.Add(TEXT("AverageExecutionTimePerRule"), Stats.AverageExecutionTimePerRule);
	}
}

TMap<FName, float> UInfluenceMapManagerBase::GetPerformanceStats() const
{
	return PerformanceMetrics;
}
