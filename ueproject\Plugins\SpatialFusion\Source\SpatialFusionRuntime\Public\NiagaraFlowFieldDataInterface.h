// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "NiagaraDataInterface.h"
#include "NiagaraCommon.h"
#include "VectorVM.h"
#include "FlowFieldTypes.h"
#include "NiagaraFlowFieldDataInterface.generated.h"

// Forward declarations
class UFlowFieldManagerBase;
class UFlowFieldManager2D;
class FNiagaraSystemInstance;

/**
 * Specialized Niagara Data Interface for Flow Fields
 * Optimized for flow field operations and particle movement
 */
UCLASS(EditInlineNew, Category = "SpatialFusion", meta = (DisplayName = "Flow Field Data Interface"))
class SPATIALFUSIONRUNTIME_API UNiagaraFlowFieldDataInterface : public UNiagaraDataInterface
{
	GENERATED_BODY()

public:
	UNiagaraFlowFieldDataInterface();

	//~ UNiagaraDataInterface interface
	virtual void PostInitProperties() override;
	virtual void GetFunctions(TArray<FNiagaraFunctionSignature>& OutFunctions) override;
	virtual void GetVMExternalFunction(const FVMExternalFunctionBindingInfo& BindingInfo, void* InstanceData, FVMExternalFunction &OutFunc) override;
	virtual bool InitPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance) override;
	virtual void DestroyPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance) override;
	virtual int32 PerInstanceDataSize() const override;
	virtual bool PerInstanceDataPassedToRenderThread() const override { return false; }
	virtual bool HasPreSimulateTick() const override { return true; }
	virtual void PreSimulateTick(void* PerInstanceData, float DeltaSeconds) override;
	virtual bool Equals(const UNiagaraDataInterface* Other) const override;
	virtual bool CanExecuteOnTarget(ENiagaraSimTarget Target) const override { return Target == ENiagaraSimTarget::CPUSim; }

	// Configuration properties

	/** Reference to the flow field manager */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	TObjectPtr<UFlowFieldManagerBase> FlowFieldManager;

	/** Whether to use world space coordinates */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	bool bUseWorldSpace = true;

	/** Whether to normalize flow directions */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	bool bNormalizeFlowDirections = true;

	/** Whether to use bilinear interpolation for flow sampling */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	bool bUseFlowInterpolation = true;

	/** Maximum flow distance for queries */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	float MaxFlowDistance = 10000.0f;

	/** Flow strength multiplier */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	float FlowStrength = 1.0f;

	/** Noise scale for flow variation */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	float NoiseScale = 0.1f;

	/** Noise strength for flow variation */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	float NoiseStrength = 0.0f;

	/** Temporal smoothing factor */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	float TemporalSmoothing = 0.1f;

	/** Whether to apply curl to flow field */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	bool bApplyCurl = false;

	/** Curl strength */
	UPROPERTY(EditAnywhere, Category = "Flow Field")
	float CurlStrength = 1.0f;

	// Function names
	static const FName GetFlowVelocityName;
	static const FName GetFlowDirectionName;
	static const FName GetFlowDistanceName;
	static const FName GetFlowGradientName;
	static const FName GetFlowCurlName;
	static const FName GetFlowDivergenceName;
	static const FName SampleFlowAlongPathName;
	static const FName GetFlowAccelerationName;
	static const FName GetNearestGoalName;
	static const FName GetGoalInfluenceName;
	static const FName CalculateFlowPathName;
	static const FName GetFlowTurbulenceName;
	static const FName GetFlowVorticityName;
	static const FName ApplyFlowForceName;

protected:
	/**
	 * Per-instance data for flow field operations
	 */
	struct FNiagaraFlowFieldInstanceData
	{
		/** Cached flow field manager */
		TWeakObjectPtr<UFlowFieldManagerBase> CachedFlowFieldManager;

		/** Cached 2D manager for optimized operations */
		TWeakObjectPtr<UFlowFieldManager2D> CachedFlowFieldManager2D;

		/** Grid properties */
		FVector GridDimensions = FVector::ZeroVector;
		float CellSize = 100.0f;
		FVector WorldOrigin = FVector::ZeroVector;
		bool bIs3D = false;

		/** Flow field cache */
		TArray<FVector> CachedFlowDirections;
		TArray<float> CachedFlowDistances;
		bool bFlowDataCached = false;
		float LastFlowCacheTime = 0.0f;

		/** Goal cache */
		TArray<FFlowFieldGoal> CachedGoals;
		bool bGoalsCached = false;
		float LastGoalCacheTime = 0.0f;

		/** Performance metrics */
		float AverageFlowMagnitude = 0.0f;
		float MaxFlowMagnitude = 0.0f;
		int32 ValidFlowCells = 0;

		/** Update cache */
		void UpdateCache(UNiagaraFlowFieldDataInterface* Interface);
		void UpdateFlowDataCache();
		void UpdateGoalCache();
		bool NeedsFlowCacheUpdate(float CurrentTime) const;
		bool NeedsGoalCacheUpdate(float CurrentTime) const;
	};

	// VM function implementations

	/** Get flow velocity (direction * magnitude) */
	void GetFlowVelocity(FVectorVMExternalFunctionContext& Context);

	/** Get flow direction */
	void GetFlowDirection(FVectorVMExternalFunctionContext& Context);

	/** Get distance to nearest goal */
	void GetFlowDistance(FVectorVMExternalFunctionContext& Context);

	/** Get flow gradient */
	void GetFlowGradient(FVectorVMExternalFunctionContext& Context);

	/** Get flow curl (rotation) */
	void GetFlowCurl(FVectorVMExternalFunctionContext& Context);

	/** Get flow divergence */
	void GetFlowDivergence(FVectorVMExternalFunctionContext& Context);

	/** Sample flow along a path */
	void SampleFlowAlongPath(FVectorVMExternalFunctionContext& Context);

	/** Get flow acceleration */
	void GetFlowAcceleration(FVectorVMExternalFunctionContext& Context);

	/** Get nearest goal position */
	void GetNearestGoal(FVectorVMExternalFunctionContext& Context);

	/** Get goal influence at position */
	void GetGoalInfluence(FVectorVMExternalFunctionContext& Context);

	/** Calculate flow path from position */
	void CalculateFlowPath(FVectorVMExternalFunctionContext& Context);

	/** Get flow turbulence */
	void GetFlowTurbulence(FVectorVMExternalFunctionContext& Context);

	/** Get flow vorticity */
	void GetFlowVorticity(FVectorVMExternalFunctionContext& Context);

	/** Apply flow force to particle */
	void ApplyFlowForce(FVectorVMExternalFunctionContext& Context);

	// Helper functions

	/**
	 * Sample flow direction with interpolation and noise
	 * @param Position - Position to sample
	 * @param InstanceData - Instance data
	 * @param Time - Current time for noise
	 * @return Flow direction
	 */
	FVector SampleFlowDirectionOptimized(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData, float Time) const;

	/**
	 * Calculate flow curl at position
	 * @param Position - Position to calculate curl
	 * @param InstanceData - Instance data
	 * @return Curl vector
	 */
	FVector CalculateFlowCurl(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData) const;

	/**
	 * Calculate flow divergence at position
	 * @param Position - Position to calculate divergence
	 * @param InstanceData - Instance data
	 * @return Divergence value
	 */
	float CalculateFlowDivergence(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData) const;

	/**
	 * Calculate flow acceleration (temporal derivative)
	 * @param Position - Position to calculate acceleration
	 * @param InstanceData - Instance data
	 * @param DeltaTime - Time step
	 * @return Acceleration vector
	 */
	FVector CalculateFlowAcceleration(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData, float DeltaTime) const;

	/**
	 * Get goal influence at position
	 * @param Position - Position to check
	 * @param Goal - Goal to check influence from
	 * @return Influence strength (0-1)
	 */
	float GetGoalInfluenceStrength(const FVector& Position, const FFlowFieldGoal& Goal) const;

	/**
	 * Add noise to flow direction
	 * @param FlowDirection - Original flow direction
	 * @param Position - Position for noise sampling
	 * @param Time - Time for temporal noise
	 * @return Modified flow direction
	 */
	FVector AddFlowNoise(const FVector& FlowDirection, const FVector& Position, float Time) const;

	/**
	 * Calculate turbulence at position
	 * @param Position - Position to calculate turbulence
	 * @param InstanceData - Instance data
	 * @return Turbulence intensity
	 */
	float CalculateTurbulence(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData) const;

	/**
	 * Calculate vorticity (curl magnitude)
	 * @param Position - Position to calculate vorticity
	 * @param InstanceData - Instance data
	 * @return Vorticity magnitude
	 */
	float CalculateVorticity(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData) const;

	/**
	 * Convert world position to grid position
	 * @param WorldPos - World position
	 * @param InstanceData - Instance data
	 * @return Grid position
	 */
	FVector WorldToGridPosition(const FVector& WorldPos, const FNiagaraFlowFieldInstanceData* InstanceData) const;

	/**
	 * Check if position is within flow field bounds
	 * @param Position - Position to check
	 * @param InstanceData - Instance data
	 * @return True if within bounds
	 */
	bool IsWithinFlowBounds(const FVector& Position, const FNiagaraFlowFieldInstanceData* InstanceData) const;

	/**
	 * Get function signature for flow field functions
	 * @param FunctionName - Function name
	 * @return Function signature
	 */
	FNiagaraFunctionSignature GetFlowFieldFunctionSignature(const FName& FunctionName) const;

	/**
	 * Generate Perlin noise for flow variation
	 * @param Position - Position for noise
	 * @param Time - Time for temporal variation
	 * @param Scale - Noise scale
	 * @return Noise value (-1 to 1)
	 */
	float GeneratePerlinNoise(const FVector& Position, float Time, float Scale) const;
};
