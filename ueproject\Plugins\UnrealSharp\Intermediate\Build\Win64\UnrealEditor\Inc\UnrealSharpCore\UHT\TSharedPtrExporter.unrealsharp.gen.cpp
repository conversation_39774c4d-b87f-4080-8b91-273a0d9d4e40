#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\TSharedPtrExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UTSharedPtrExporter
{
    static const FCSExportedFunction UnrealSharpBind_AddSharedReference;
    static const FCSExportedFunction UnrealSharpBind_ReleaseSharedReference;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTSharedPtrExporter::UnrealSharpBind_AddSharedReference = FCSExportedFunction("TSharedPtrExporter", "AddSharedReference", (void*)&UTSharedPtrExporter::AddSharedReference, GetFunctionSize(UTSharedPtrExporter::AddSharedReference));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UTSharedPtrExporter::UnrealSharpBind_ReleaseSharedReference = FCSExportedFunction("TSharedPtrExporter", "ReleaseSharedReference", (void*)&UTSharedPtrExporter::ReleaseSharedReference, GetFunctionSize(UTSharedPtrExporter::ReleaseSharedReference));

