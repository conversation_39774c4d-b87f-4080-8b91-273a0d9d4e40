{"Version": "1.2", "Data": {"Source": "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpeditor\\module.unrealsharpeditor.cpp", "ProvidedModule": "", "PCH": "e:\\unreal projects\\horizonbrigade\\ueproject\\intermediate\\build\\win64\\x64\\horizonbrigadeeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpeditor\\definitions.unrealsharpeditor.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\cseditorsubsystem.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\extensions\\cseditorsubsystem.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\cseditorsubsystem.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\csunrealsharpeditorsettings.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\csunrealsharpeditorsettings.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\csunrealsharpeditorsettings.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\unrealsharpeditor.init.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\unrealsharpeditormoduleexporter.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\unrealsharpeditormoduleexporter.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\unrealsharpeditor.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csbindsmanager.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csexportedfunction.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\unrealsharpeditormoduleexporter.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpeditor\\uht\\unrealsharpeditormoduleexporter.unrealsharp.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\unrealsharpbinds.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpeditor\\permoduleinline.gen.cpp", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\assetactions\\csassettypeaction_csblueprint.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\assetactions\\csassettypeaction_csblueprint.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettypeactions\\assettypeactions_blueprint.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettypeactions\\assettypeactions_classtypebase.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettypeactions_base.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\iassettools.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettypecategories.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\iassettypeactions.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\merge\\public\\merge.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\thumbnailrendering\\thumbnailmanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\thumbnailrendering\\thumbnailrenderer.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\thumbnailrenderer.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\thumbnailmanager.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\automatedassetimportdata.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\automatedassetimportdata.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assettools\\uht\\iassettools.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettoolsmodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\toolkits\\simpleasseteditor.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csblueprint.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csblueprint.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\csstyle.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\csstyle.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slatecore\\public\\styling\\slatestyleregistry.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\interfaces\\ipluginmanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\plugindescriptor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\custombuildsteps.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\localizationdescriptor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\moduledescriptor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\plugindisalloweddescriptor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\pluginreferencedescriptor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\projects\\public\\versescope.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slatecore\\public\\styling\\slatestylemacros.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\csunrealsharpeditorcommands.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\csunrealsharpeditorcommands.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\csunrealsharpeditorsettings.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\slate\\csnewprojectwizard.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\slate\\csnewprojectwizard.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\desktopplatform\\public\\desktopplatformmodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\desktopplatform\\public\\idesktopplatform.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\appframework\\public\\widgets\\workflow\\swizard.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\navigation\\sbreadcrumbtrail.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpprochelper\\csprochelper.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\unrealsharpeditor.cpp", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismet\\public\\blueprintcompilationmanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\kismeteditorutilities.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprinteditorlibrary\\public\\blueprinteditorlibrary.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprinteditorlibrary\\uht\\blueprinteditorlibrary.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\directorywatcher\\public\\directorywatchermodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\directorywatcher\\public\\idirectorywatcher.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\settings\\public\\isettingsmodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\leveleditor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\ileveleditor.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\framework\\engineelementslibrary.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\engineelementslibrary.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\viewporttypedefinition.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\leveleditoroutlinersettings.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\toolwidgets\\public\\filters\\genericfilter.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\toolwidgets\\public\\filters\\filterbase.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\sourcecodenavigation.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\subobjectdatainterface\\public\\subobjectdatasubsystem.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\subobjectdatainterface\\public\\subobjectdata.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\subobjectdatainterface\\public\\subobjectdatahandle.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\subobjectdatainterface\\uht\\subobjectdatahandle.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\scs_node.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\simpleconstructionscript.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\simpleconstructionscript.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\scs_node.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\subobjectdatainterface\\uht\\subobjectdata.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\subobjectdatainterface\\uht\\subobjectdatasubsystem.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpruntimeglue\\public\\unrealsharpruntimeglue.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpruntimeglue\\public\\csgluegenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpruntimeglue\\public\\csscriptbuilder.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpruntimeglue\\uht\\csgluegenerator.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanagertypes.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanagertypes.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\iostoreondemand.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\sharedstring.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandhostgroup.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandtoc.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\experimental\\streamablemanagererror.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformchunkinstall.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\contentencryptionconfig.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanager.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\inheritablecomponenthandler.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\inheritablecomponenthandler.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanager.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\managed\\dotnetruntime\\inc\\coreclr_delegates.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\managed\\dotnetruntime\\inc\\hostfxr.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csassembly.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedgchandle.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedcallbackscache.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanagedgchandle.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\unrealsharpcore.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cstypereferencemetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csfieldname.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csnamespace.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\utils\\csclassutilities.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csclass.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\utils\\csmacros.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csclass.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csskeletonclass.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csskeletonclass.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanager.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\framework\\notifications\\notificationmanager.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\coreasynctasknotificationimpl.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\asynctasknotification.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\mainframe\\public\\interfaces\\imainframemodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\blueprinteditorutils.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\classviewer\\public\\classviewermodule.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\debuggercommands.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\csgeneratedclassbuilder.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\csgeneratedtypebuilder.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\csmetadatautils.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csfunctionmetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csmembermetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cspropertymetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csunrealtype.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cspropertytype.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csclassmetadata.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\notifications\\snotificationlist.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csenum.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\managedreferencescollection.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\managedreferencescollection.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\userdefinedenum.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\userdefinedenum.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenum.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csscriptstruct.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\userdefinedstruct.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\userdefinedstruct.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csscriptstruct.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\unrealtypeprivate.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpeditor\\unrealsharpeditormoduleexporter.cpp"], "ImportedModules": [], "ImportedHeaderUnits": []}}