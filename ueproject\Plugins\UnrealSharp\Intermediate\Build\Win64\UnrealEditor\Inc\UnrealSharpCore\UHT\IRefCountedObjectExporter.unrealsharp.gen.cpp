#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\IRefCountedObjectExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UIRefCountedObjectExporter
{
    static const FCSExportedFunction UnrealSharpBind_GetRefCount;
    static const FCSExportedFunction UnrealSharpBind_AddRef;
    static const FCSExportedFunction UnrealSharpBind_Release;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UIRefCountedObjectExporter::UnrealSharpBind_GetRefCount = FCSExportedFunction("IRefCountedObjectExporter", "GetRefCount", (void*)&UIRefCountedObjectExporter::GetRefCount, GetFunctionSize(UIRefCountedObjectExporter::GetRefCount));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UIRefCountedObjectExporter::UnrealSharpBind_AddRef = FCSExportedFunction("IRefCountedObjectExporter", "AddRef", (void*)&UIRefCountedObjectExporter::AddRef, GetFunctionSize(UIRefCountedObjectExporter::AddRef));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UIRefCountedObjectExporter::UnrealSharpBind_Release = FCSExportedFunction("IRefCountedObjectExporter", "Release", (void*)&UIRefCountedObjectExporter::Release, GetFunctionSize(UIRefCountedObjectExporter::Release));

