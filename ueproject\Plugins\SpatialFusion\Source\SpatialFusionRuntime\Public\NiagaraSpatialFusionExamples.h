// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameFramework/Actor.h"
#include "NiagaraComponent.h"
#include "NiagaraSpatialFusionExamples.generated.h"

// Forward declarations
class UNiagaraSpatialFusionDataInterface;
class UNiagaraInfluenceMapDataInterface;
class UNiagaraFlowFieldDataInterface;
class UInfluenceMapManagerBase;
class UFlowFieldManagerBase;

/**
 * Example actor demonstrating Niagara integration with SpatialFusion
 * Shows various use cases for influence maps and flow fields in particle systems
 */
UCLASS(BlueprintType, Blueprintable)
class SPATIALFUSIONRUNTIME_API ANiagaraSpatialFusionExample : public AActor
{
	GENERATED_BODY()

public:
	ANiagaraSpatialFusionExample();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;

	// Components

	/** Root scene component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<USceneComponent> RootSceneComponent;

	/** Fire simulation Niagara component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<UNiagaraComponent> FireSimulationComponent;

	/** Smoke simulation Niagara component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<UNiagaraComponent> SmokeSimulationComponent;

	/** Flow visualization Niagara component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<UNiagaraComponent> FlowVisualizationComponent;

	/** Particle movement Niagara component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	TObjectPtr<UNiagaraComponent> ParticleMovementComponent;

	// Configuration

	/** Influence map manager for fire simulation */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SpatialFusion")
	TObjectPtr<UInfluenceMapManagerBase> FireInfluenceMap;

	/** Influence map manager for smoke simulation */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SpatialFusion")
	TObjectPtr<UInfluenceMapManagerBase> SmokeInfluenceMap;

	/** Flow field manager for particle movement */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SpatialFusion")
	TObjectPtr<UFlowFieldManagerBase> ParticleFlowField;

	/** Whether to automatically update simulations */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SpatialFusion")
	bool bAutoUpdate = true;

	/** Update frequency for simulations */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SpatialFusion")
	float UpdateFrequency = 10.0f;

	/** Fire spread rate */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Simulation")
	float FireSpreadRate = 1.0f;

	/** Fire intensity */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Simulation")
	float FireIntensity = 100.0f;

	/** Smoke generation rate */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Smoke Simulation")
	float SmokeGenerationRate = 0.5f;

	/** Wind direction for smoke */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Smoke Simulation")
	FVector WindDirection = FVector(1, 0, 0);

	/** Wind strength */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Smoke Simulation")
	float WindStrength = 1.0f;

	// Internal state

	/** Last update time */
	float LastUpdateTime = 0.0f;

	/** Fire source positions */
	UPROPERTY(BlueprintReadOnly, Category = "Fire Simulation")
	TArray<FVector> FireSources;

	/** Current fire temperature map */
	UPROPERTY(BlueprintReadOnly, Category = "Fire Simulation")
	TArray<float> FireTemperatureMap;

	/** Current smoke density map */
	UPROPERTY(BlueprintReadOnly, Category = "Smoke Simulation")
	TArray<float> SmokeDensityMap;

public:
	// Blueprint callable functions

	/**
	 * Initialize the spatial fusion systems
	 * @param GridSize - Size of the simulation grid
	 * @param CellSize - Size of each grid cell
	 * @param WorldOrigin - Origin of the grid in world space
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion")
	void InitializeSpatialFusion(const FVector& GridSize, float CellSize, const FVector& WorldOrigin);

	/**
	 * Add a fire source at the specified location
	 * @param Location - World location of the fire source
	 * @param Intensity - Initial fire intensity
	 * @param Radius - Fire source radius
	 */
	UFUNCTION(BlueprintCallable, Category = "Fire Simulation")
	void AddFireSource(const FVector& Location, float Intensity = 100.0f, float Radius = 2.0f);

	/**
	 * Remove fire source at location
	 * @param Location - Location to remove fire from
	 * @param Radius - Radius to clear
	 */
	UFUNCTION(BlueprintCallable, Category = "Fire Simulation")
	void RemoveFireSource(const FVector& Location, float Radius = 2.0f);

	/**
	 * Update fire simulation
	 * @param DeltaTime - Time since last update
	 */
	UFUNCTION(BlueprintCallable, Category = "Fire Simulation")
	void UpdateFireSimulation(float DeltaTime);

	/**
	 * Update smoke simulation
	 * @param DeltaTime - Time since last update
	 */
	UFUNCTION(BlueprintCallable, Category = "Smoke Simulation")
	void UpdateSmokeSimulation(float DeltaTime);

	/**
	 * Update flow field for particle movement
	 * @param Goals - Target positions for particles
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	void UpdateParticleFlowField(const TArray<FVector>& Goals);

	/**
	 * Set wind parameters for smoke simulation
	 * @param Direction - Wind direction
	 * @param Strength - Wind strength
	 */
	UFUNCTION(BlueprintCallable, Category = "Smoke Simulation")
	void SetWindParameters(const FVector& Direction, float Strength);

	/**
	 * Get fire temperature at location
	 * @param Location - World location to query
	 * @return Fire temperature at location
	 */
	UFUNCTION(BlueprintCallable, Category = "Fire Simulation")
	float GetFireTemperatureAtLocation(const FVector& Location) const;

	/**
	 * Get smoke density at location
	 * @param Location - World location to query
	 * @return Smoke density at location
	 */
	UFUNCTION(BlueprintCallable, Category = "Smoke Simulation")
	float GetSmokeDensityAtLocation(const FVector& Location) const;

	/**
	 * Get flow direction at location
	 * @param Location - World location to query
	 * @return Flow direction at location
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field")
	FVector GetFlowDirectionAtLocation(const FVector& Location) const;

	/**
	 * Enable or disable automatic updates
	 * @param bEnabled - Whether to enable automatic updates
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion")
	void SetAutoUpdate(bool bEnabled);

	/**
	 * Set update frequency
	 * @param Frequency - Updates per second
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion")
	void SetUpdateFrequency(float Frequency);

	/**
	 * Reset all simulations
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion")
	void ResetSimulations();

	/**
	 * Get simulation statistics
	 * @return Map of statistic names to values
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion")
	TMap<FString, float> GetSimulationStatistics() const;

protected:
	/**
	 * Setup Niagara data interfaces
	 */
	void SetupNiagaraDataInterfaces();

	/**
	 * Update fire propagation using influence map
	 * @param DeltaTime - Time step
	 */
	void PropagateFireInfluence(float DeltaTime);

	/**
	 * Update smoke generation and movement
	 * @param DeltaTime - Time step
	 */
	void UpdateSmokeInfluence(float DeltaTime);

	/**
	 * Apply wind effects to smoke
	 * @param DeltaTime - Time step
	 */
	void ApplyWindEffects(float DeltaTime);

	/**
	 * Update Niagara parameters based on simulation state
	 */
	void UpdateNiagaraParameters();

	/**
	 * Check if update is needed based on frequency
	 * @param CurrentTime - Current time
	 * @return True if update is needed
	 */
	bool ShouldUpdate(float CurrentTime) const;
};

/**
 * Blueprint function library for Niagara SpatialFusion integration
 */
UCLASS()
class SPATIALFUSIONRUNTIME_API UNiagaraSpatialFusionBlueprintLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 * Create and configure a SpatialFusion data interface for Niagara
	 * @param InfluenceMapManager - Influence map manager to use
	 * @param FlowFieldManager - Flow field manager to use
	 * @param bUseWorldSpace - Whether to use world space coordinates
	 * @return Configured data interface
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion|Niagara")
	static UNiagaraSpatialFusionDataInterface* CreateSpatialFusionDataInterface(
		UInfluenceMapManagerBase* InfluenceMapManager,
		UFlowFieldManagerBase* FlowFieldManager,
		bool bUseWorldSpace = true
	);

	/**
	 * Create and configure an influence map data interface for Niagara
	 * @param InfluenceMapManager - Influence map manager to use
	 * @param PrimaryValueKey - Primary value key to sample
	 * @param bUseInterpolation - Whether to use bilinear interpolation
	 * @return Configured data interface
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion|Niagara")
	static UNiagaraInfluenceMapDataInterface* CreateInfluenceMapDataInterface(
		UInfluenceMapManagerBase* InfluenceMapManager,
		const FName& PrimaryValueKey = TEXT("Default"),
		bool bUseInterpolation = true
	);

	/**
	 * Create and configure a flow field data interface for Niagara
	 * @param FlowFieldManager - Flow field manager to use
	 * @param bNormalizeDirections - Whether to normalize flow directions
	 * @param FlowStrength - Flow strength multiplier
	 * @return Configured data interface
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion|Niagara")
	static UNiagaraFlowFieldDataInterface* CreateFlowFieldDataInterface(
		UFlowFieldManagerBase* FlowFieldManager,
		bool bNormalizeDirections = true,
		float FlowStrength = 1.0f
	);

	/**
	 * Set data interface on Niagara component
	 * @param NiagaraComponent - Niagara component to modify
	 * @param DataInterfaceName - Name of the data interface in the system
	 * @param DataInterface - Data interface to set
	 * @return True if successful
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion|Niagara")
	static bool SetNiagaraDataInterface(
		UNiagaraComponent* NiagaraComponent,
		const FName& DataInterfaceName,
		UNiagaraDataInterface* DataInterface
	);

	/**
	 * Get influence value array for Niagara texture sampling
	 * @param InfluenceMapManager - Influence map manager
	 * @param ValueKey - Value key to sample
	 * @param Width - Output texture width
	 * @param Height - Output texture height
	 * @return Array of influence values
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion|Niagara")
	static TArray<float> GetInfluenceTextureData(
		UInfluenceMapManagerBase* InfluenceMapManager,
		const FName& ValueKey,
		int32& Width,
		int32& Height
	);

	/**
	 * Get flow field vector array for Niagara texture sampling
	 * @param FlowFieldManager - Flow field manager
	 * @param Width - Output texture width
	 * @param Height - Output texture height
	 * @return Array of flow vectors
	 */
	UFUNCTION(BlueprintCallable, Category = "SpatialFusion|Niagara")
	static TArray<FVector> GetFlowFieldTextureData(
		UFlowFieldManagerBase* FlowFieldManager,
		int32& Width,
		int32& Height
	);
};
