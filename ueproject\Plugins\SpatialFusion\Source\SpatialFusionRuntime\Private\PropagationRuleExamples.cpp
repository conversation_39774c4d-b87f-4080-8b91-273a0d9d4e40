// Copyright Epic Games, Inc. All Rights Reserved.

#include "PropagationRuleExamples.h"
#include "PropagationRuleAsset.h"
#include "PropagationRuleSet.h"
#include "PropagationRuleEngine.h"
#include "InfluenceMapManager2D.h"
#include "InfluenceMapManager3D.h"
#include "Engine/Engine.h"
#include "Components/SceneComponent.h"

//////////////////////////////////////////////////////////////////////////
// APropagationRuleExample

APropagationRuleExample::APropagationRuleExample()
{
	PrimaryActorTick.bCanEverTick = true;

	// Create components
	RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
	RootComponent = RootSceneComponent;

	// Initialize default values
	SimulationType = ESpatialFusionExampleType::FirePropagation;
	GridSize = FVector(50, 50, 1);
	CellSize = 100.0f;
	bUse3D = false;
	bAutoStart = true;
	UpdateFrequency = 10.0f;
	bShowVisualization = true;
	bShowParticleEffects = false;
	bShowDebugInfo = false;

	// Fire simulation defaults
	FireSpreadRate = 1.0f;
	FireDecayRate = 0.1f;
	FireIntensity = 100.0f;

	// Disease simulation defaults
	InfectionRate = 0.3f;
	RecoveryRate = 0.1f;
	ImmunityDuration = 10.0f;

	// Economic simulation defaults
	EconomicGrowthRate = 0.05f;
	EconomicDecayRate = 0.02f;
	TradeRadius = 500.0f;

	// Crowd simulation defaults
	CrowdSize = 100;
	CrowdSpeed = 200.0f;

	// Internal state
	bSimulationRunning = false;
	LastUpdateTime = 0.0f;
	SimulationTime = 0.0f;
}

void APropagationRuleExample::BeginPlay()
{
	Super::BeginPlay();

	if (bAutoStart)
	{
		InitializeSimulation();
		StartSimulation();
	}
}

void APropagationRuleExample::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (bSimulationRunning && ShouldUpdateSimulation(GetWorld()->GetTimeSeconds()))
	{
		UpdateSimulation(DeltaTime);
		LastUpdateTime = GetWorld()->GetTimeSeconds();
	}

	if (bShowDebugInfo)
	{
		DrawDebugInformation();
	}
}

void APropagationRuleExample::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	StopSimulation();
	Super::EndPlay(EndPlayReason);
}

#if WITH_EDITOR
void APropagationRuleExample::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property)
	{
		FName PropertyName = PropertyChangedEvent.Property->GetFName();
		
		if (PropertyName == GET_MEMBER_NAME_CHECKED(APropagationRuleExample, SimulationType))
		{
			// Reinitialize when simulation type changes
			if (bSimulationRunning)
			{
				StopSimulation();
				InitializeSimulation();
				StartSimulation();
			}
		}
	}
}
#endif

void APropagationRuleExample::InitializeSimulation()
{
	// Create appropriate grid manager
	if (bUse3D)
	{
		InfluenceMapManager3D = NewObject<UInfluenceMapManager3D>(this);
		InfluenceMapManager3D->Initialize3D(GridSize, CellSize, GetActorLocation());
	}
	else
	{
		InfluenceMapManager2D = NewObject<UInfluenceMapManager2D>(this);
		InfluenceMapManager2D->Initialize2D(GridSize, CellSize, GetActorLocation());
	}

	// Create rule engine
	RuleEngine = NewObject<UPropagationRuleEngine>(this);
	RuleEngine->GridManager = GetInfluenceMapManager();
	RuleEngine->SetMaxExecutionTimePerFrame(5.0f);

	// Setup simulation based on type
	SetupSimulation();
}

void APropagationRuleExample::StartSimulation()
{
	if (!RuleEngine || !GetInfluenceMapManager())
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot start simulation: Missing components"));
		return;
	}

	bSimulationRunning = true;
	SimulationTime = 0.0f;
	LastUpdateTime = GetWorld()->GetTimeSeconds();

	UE_LOG(LogTemp, Log, TEXT("Started %s simulation"), *UEnum::GetValueAsString(SimulationType));
}

void APropagationRuleExample::StopSimulation()
{
	bSimulationRunning = false;
	UE_LOG(LogTemp, Log, TEXT("Stopped simulation"));
}

void APropagationRuleExample::ResetSimulation()
{
	StopSimulation();

	// Clear all influence data
	if (UInfluenceMapManagerBase* Manager = GetInfluenceMapManager())
	{
		Manager->ClearAllValues();
	}

	// Reset internal state
	SimulationTime = 0.0f;
	SimulationSources.Empty();

	// Reset rule engine statistics
	if (RuleEngine)
	{
		RuleEngine->ResetStatistics();
	}

	UE_LOG(LogTemp, Log, TEXT("Reset simulation"));
}

void APropagationRuleExample::AddSimulationSource(const FVector& Location, float Intensity)
{
	UInfluenceMapManagerBase* Manager = GetInfluenceMapManager();
	if (!Manager)
	{
		return;
	}

	SimulationSources.Add(Location);

	switch (SimulationType)
	{
	case ESpatialFusionExampleType::FirePropagation:
		Manager->SetCellValueAtWorldPosition(Location, TEXT("Temperature"), Intensity);
		break;

	case ESpatialFusionExampleType::DiseaseSpread:
		Manager->SetCellValueAtWorldPosition(Location, TEXT("Infection"), 1.0f);
		Manager->SetCellValueAtWorldPosition(Location, TEXT("Health"), FMath::Max(0.0f, 100.0f - Intensity));
		break;

	case ESpatialFusionExampleType::EconomicInfluence:
		Manager->SetCellValueAtWorldPosition(Location, TEXT("EconomicValue"), Intensity);
		break;

	case ESpatialFusionExampleType::CrowdMovement:
		// For crowd movement, we'd set up flow field goals instead
		break;

	default:
		Manager->SetCellValueAtWorldPosition(Location, TEXT("Default"), Intensity);
		break;
	}

	UE_LOG(LogTemp, Log, TEXT("Added simulation source at %s with intensity %.2f"), *Location.ToString(), Intensity);
}

void APropagationRuleExample::RemoveSimulationSource(const FVector& Location, float Radius)
{
	UInfluenceMapManagerBase* Manager = GetInfluenceMapManager();
	if (!Manager)
	{
		return;
	}

	// Clear values in radius around location
	FVector GridDimensions = Manager->GetGridDimensions();
	float CellSize = Manager->GetCellSize();
	FVector WorldOrigin = Manager->GetWorldOrigin();

	for (int32 X = 0; X < static_cast<int32>(GridDimensions.X); X++)
	{
		for (int32 Y = 0; Y < static_cast<int32>(GridDimensions.Y); Y++)
		{
			for (int32 Z = 0; Z < static_cast<int32>(GridDimensions.Z); Z++)
			{
				FVector CellPosition = WorldOrigin + FVector(X, Y, Z) * CellSize;
				float Distance = FVector::Distance(CellPosition, Location);
				
				if (Distance <= Radius)
				{
					Manager->SetCellValueAtWorldPosition(CellPosition, TEXT("Default"), 0.0f);
					Manager->SetCellValueAtWorldPosition(CellPosition, TEXT("Temperature"), 0.0f);
					Manager->SetCellValueAtWorldPosition(CellPosition, TEXT("Infection"), 0.0f);
					Manager->SetCellValueAtWorldPosition(CellPosition, TEXT("Health"), 100.0f);
					Manager->SetCellValueAtWorldPosition(CellPosition, TEXT("EconomicValue"), 0.0f);
				}
			}
		}
	}

	// Remove from sources list
	SimulationSources.RemoveAll([Location, Radius](const FVector& Source)
	{
		return FVector::Distance(Source, Location) <= Radius;
	});

	UE_LOG(LogTemp, Log, TEXT("Removed simulation sources near %s within radius %.2f"), *Location.ToString(), Radius);
}

float APropagationRuleExample::GetSimulationValueAtLocation(const FVector& Location, const FName& ValueKey) const
{
	UInfluenceMapManagerBase* Manager = GetInfluenceMapManager();
	if (!Manager)
	{
		return 0.0f;
	}

	return Manager->GetCellValueAtWorldPosition(Location, ValueKey);
}

void APropagationRuleExample::SetSimulationValueAtLocation(const FVector& Location, const FName& ValueKey, float Value)
{
	UInfluenceMapManagerBase* Manager = GetInfluenceMapManager();
	if (!Manager)
	{
		return;
	}

	Manager->SetCellValueAtWorldPosition(Location, ValueKey, Value);
}

FVector APropagationRuleExample::GetFlowDirectionAtLocation(const FVector& Location) const
{
	// This would require flow field implementation
	// For now, return zero vector
	return FVector::ZeroVector;
}

void APropagationRuleExample::SwitchSimulationType(ESpatialFusionExampleType NewType)
{
	if (NewType == SimulationType)
	{
		return;
	}

	bool bWasRunning = bSimulationRunning;
	
	StopSimulation();
	SimulationType = NewType;
	InitializeSimulation();
	
	if (bWasRunning)
	{
		StartSimulation();
	}

	UE_LOG(LogTemp, Log, TEXT("Switched to %s simulation"), *UEnum::GetValueAsString(SimulationType));
}

TMap<FString, float> APropagationRuleExample::GetSimulationStatistics() const
{
	TMap<FString, float> Stats;

	if (RuleEngine)
	{
		FRuleExecutionStats RuleStats = RuleEngine->GetExecutionStatistics();
		Stats.Add(TEXT("TotalRulesExecuted"), static_cast<float>(RuleStats.TotalRulesExecuted));
		Stats.Add(TEXT("TotalExecutionTime"), RuleStats.TotalExecutionTime);
		Stats.Add(TEXT("CellsProcessed"), static_cast<float>(RuleStats.CellsProcessed));
		Stats.Add(TEXT("RuleFailures"), static_cast<float>(RuleStats.RuleFailures));
	}

	Stats.Add(TEXT("SimulationTime"), SimulationTime);
	Stats.Add(TEXT("SimulationSources"), static_cast<float>(SimulationSources.Num()));
	Stats.Add(TEXT("IsRunning"), bSimulationRunning ? 1.0f : 0.0f);

	return Stats;
}

bool APropagationRuleExample::ExportSimulationData(const FString& Filename) const
{
	// This would export the current simulation state to a file
	// Implementation would depend on desired format (JSON, CSV, etc.)
	UE_LOG(LogTemp, Log, TEXT("Export simulation data to %s (not implemented)"), *Filename);
	return false;
}

bool APropagationRuleExample::ImportSimulationData(const FString& Filename)
{
	// This would import simulation state from a file
	UE_LOG(LogTemp, Log, TEXT("Import simulation data from %s (not implemented)"), *Filename);
	return false;
}

bool APropagationRuleExample::TakeVisualizationScreenshot(const FString& Filename)
{
	// This would take a screenshot of the current visualization
	UE_LOG(LogTemp, Log, TEXT("Take visualization screenshot %s (not implemented)"), *Filename);
	return false;
}

void APropagationRuleExample::SetupSimulation()
{
	CreateRuleSet();
	
	if (RuleEngine && CurrentRuleSet)
	{
		RuleEngine->SetRuleSet(CurrentRuleSet);
	}
}

void APropagationRuleExample::CreateRuleSet()
{
	switch (SimulationType)
	{
	case ESpatialFusionExampleType::FirePropagation:
		CurrentRuleSet = CreateFireRules();
		break;

	case ESpatialFusionExampleType::DiseaseSpread:
		CurrentRuleSet = CreateDiseaseRules();
		break;

	case ESpatialFusionExampleType::EconomicInfluence:
		CurrentRuleSet = CreateEconomicRules();
		break;

	case ESpatialFusionExampleType::CrowdMovement:
		SetupCrowdMovement();
		break;

	default:
		// Create a simple default rule set
		CurrentRuleSet = NewObject<UPropagationRuleSet>(this);
		CurrentRuleSet->RuleSetName = TEXT("DefaultRuleSet");
		break;
	}
}

void APropagationRuleExample::UpdateSimulation(float DeltaTime)
{
	if (!RuleEngine)
	{
		return;
	}

	SimulationTime += DeltaTime;

	// Execute rules for all cells
	FRuleExecutionStats Stats = RuleEngine->ExecuteRulesForAllCells(DeltaTime);

	// Log statistics periodically
	static float LastLogTime = 0.0f;
	if (SimulationTime - LastLogTime > 5.0f)
	{
		UE_LOG(LogTemp, Log, TEXT("Simulation Stats - Rules: %d, Time: %.2fms, Cells: %d"), 
			Stats.TotalRulesExecuted, Stats.TotalExecutionTime, Stats.CellsProcessed);
		LastLogTime = SimulationTime;
	}
}

bool APropagationRuleExample::ShouldUpdateSimulation(float CurrentTime) const
{
	return (CurrentTime - LastUpdateTime) >= (1.0f / UpdateFrequency);
}

UInfluenceMapManagerBase* APropagationRuleExample::GetInfluenceMapManager() const
{
	if (bUse3D && InfluenceMapManager3D)
	{
		return InfluenceMapManager3D;
	}
	else if (!bUse3D && InfluenceMapManager2D)
	{
		return InfluenceMapManager2D;
	}
	
	return nullptr;
}

UPropagationRuleSet* APropagationRuleExample::CreateFireRules()
{
	UPropagationRuleSet* FireRuleSet = NewObject<UPropagationRuleSet>(this);
	FireRuleSet->RuleSetName = TEXT("FirePropagation");

	// Create fire spread rule
	UPropagationRuleAsset* SpreadRule = NewObject<UPropagationRuleAsset>(this);
	SpreadRule->RuleName = TEXT("FireSpread");
	SpreadRule->Priority = EPropagationRulePriority::High;

	// Condition: neighbor has high temperature
	FPropagationCondition SpreadCondition;
	SpreadCondition.ConditionType = EPropagationConditionType::NeighborCount;
	SpreadCondition.ValueKey = TEXT("Temperature");
	SpreadCondition.Threshold = 80.0f;
	SpreadCondition.ComparisonOperator = TEXT(">");
	SpreadCondition.MinNeighborCount = 1;
	SpreadRule->Conditions.Add(SpreadCondition);

	// Action: increase temperature
	FPropagationAction SpreadAction;
	SpreadAction.ActionType = EPropagationActionType::AddValue;
	SpreadAction.TargetValueKey = TEXT("Temperature");
	SpreadAction.ActionValue = FireSpreadRate * 10.0f;
	SpreadAction.bClampResult = true;
	SpreadAction.ClampMax = 100.0f;
	SpreadRule->Actions.Add(SpreadAction);

	// Create fire decay rule
	UPropagationRuleAsset* DecayRule = NewObject<UPropagationRuleAsset>(this);
	DecayRule->RuleName = TEXT("FireDecay");
	DecayRule->Priority = EPropagationRulePriority::Normal;

	// Condition: has temperature
	FPropagationCondition DecayCondition;
	DecayCondition.ConditionType = EPropagationConditionType::ValueThreshold;
	DecayCondition.ValueKey = TEXT("Temperature");
	DecayCondition.Threshold = 1.0f;
	DecayCondition.ComparisonOperator = TEXT(">");
	DecayRule->Conditions.Add(DecayCondition);

	// Action: decay temperature
	FPropagationAction DecayAction;
	DecayAction.ActionType = EPropagationActionType::MultiplyValue;
	DecayAction.TargetValueKey = TEXT("Temperature");
	DecayAction.ActionValue = 1.0f - FireDecayRate;
	DecayRule->Actions.Add(DecayAction);

	FireRuleSet->Rules.Add(SpreadRule);
	FireRuleSet->Rules.Add(DecayRule);

	return FireRuleSet;
}

UPropagationRuleSet* APropagationRuleExample::CreateDiseaseRules()
{
	UPropagationRuleSet* DiseaseRuleSet = NewObject<UPropagationRuleSet>(this);
	DiseaseRuleSet->RuleSetName = TEXT("DiseaseSpread");

	// Create infection rule
	UPropagationRuleAsset* InfectionRule = NewObject<UPropagationRuleAsset>(this);
	InfectionRule->RuleName = TEXT("DiseaseInfection");
	InfectionRule->Priority = EPropagationRulePriority::High;
	InfectionRule->ExecutionProbability = InfectionRate;

	// Condition: healthy and has infected neighbors
	FPropagationCondition HealthCondition;
	HealthCondition.ConditionType = EPropagationConditionType::ValueThreshold;
	HealthCondition.ValueKey = TEXT("Health");
	HealthCondition.Threshold = 50.0f;
	HealthCondition.ComparisonOperator = TEXT(">");
	InfectionRule->Conditions.Add(HealthCondition);

	FPropagationCondition InfectedNeighborCondition;
	InfectedNeighborCondition.ConditionType = EPropagationConditionType::NeighborCount;
	InfectedNeighborCondition.ValueKey = TEXT("Infection");
	InfectedNeighborCondition.Threshold = 0.5f;
	InfectedNeighborCondition.ComparisonOperator = TEXT(">");
	InfectedNeighborCondition.MinNeighborCount = 1;
	InfectionRule->Conditions.Add(InfectedNeighborCondition);

	// Action: infect
	FPropagationAction InfectAction;
	InfectAction.ActionType = EPropagationActionType::SetValue;
	InfectAction.TargetValueKey = TEXT("Infection");
	InfectAction.ActionValue = 1.0f;
	InfectionRule->Actions.Add(InfectAction);

	DiseaseRuleSet->Rules.Add(InfectionRule);

	return DiseaseRuleSet;
}

UPropagationRuleSet* APropagationRuleExample::CreateEconomicRules()
{
	UPropagationRuleSet* EconomicRuleSet = NewObject<UPropagationRuleSet>(this);
	EconomicRuleSet->RuleSetName = TEXT("EconomicInfluence");

	// Create economic growth rule
	UPropagationRuleAsset* GrowthRule = NewObject<UPropagationRuleAsset>(this);
	GrowthRule->RuleName = TEXT("EconomicGrowth");
	GrowthRule->Priority = EPropagationRulePriority::Normal;

	// Condition: has economic neighbors
	FPropagationCondition EconomicNeighborCondition;
	EconomicNeighborCondition.ConditionType = EPropagationConditionType::NeighborCount;
	EconomicNeighborCondition.ValueKey = TEXT("EconomicValue");
	EconomicNeighborCondition.Threshold = 10.0f;
	EconomicNeighborCondition.ComparisonOperator = TEXT(">");
	EconomicNeighborCondition.MinNeighborCount = 1;
	GrowthRule->Conditions.Add(EconomicNeighborCondition);

	// Action: grow economic value
	FPropagationAction GrowthAction;
	GrowthAction.ActionType = EPropagationActionType::MultiplyValue;
	GrowthAction.TargetValueKey = TEXT("EconomicValue");
	GrowthAction.ActionValue = 1.0f + EconomicGrowthRate;
	GrowthAction.bClampResult = true;
	GrowthAction.ClampMax = 100.0f;
	GrowthRule->Actions.Add(GrowthAction);

	EconomicRuleSet->Rules.Add(GrowthRule);

	return EconomicRuleSet;
}

void APropagationRuleExample::SetupCrowdMovement()
{
	// This would set up flow field goals for crowd movement
	// For now, create an empty rule set
	CurrentRuleSet = NewObject<UPropagationRuleSet>(this);
	CurrentRuleSet->RuleSetName = TEXT("CrowdMovement");
}

void APropagationRuleExample::DrawDebugInformation()
{
	if (!GetWorld())
	{
		return;
	}

	// Draw simulation sources
	for (const FVector& Source : SimulationSources)
	{
		DrawDebugSphere(GetWorld(), Source, 50.0f, 12, FColor::Red, false, -1.0f, 0, 2.0f);
	}

	// Draw grid bounds
	UInfluenceMapManagerBase* Manager = GetInfluenceMapManager();
	if (Manager)
	{
		FVector GridDimensions = Manager->GetGridDimensions();
		float CellSize = Manager->GetCellSize();
		FVector WorldOrigin = Manager->GetWorldOrigin();
		FVector GridExtent = GridDimensions * CellSize;
		FVector GridCenter = WorldOrigin + GridExtent * 0.5f;

		DrawDebugBox(GetWorld(), GridCenter, GridExtent * 0.5f, FColor::Green, false, -1.0f, 0, 2.0f);
	}

	// Draw statistics
	FVector DebugLocation = GetActorLocation() + FVector(0, 0, 200);
	TMap<FString, float> Stats = GetSimulationStatistics();
	
	int32 LineIndex = 0;
	for (const auto& StatPair : Stats)
	{
		FString StatText = FString::Printf(TEXT("%s: %.2f"), *StatPair.Key, StatPair.Value);
		DrawDebugString(GetWorld(), DebugLocation + FVector(0, 0, LineIndex * 20), StatText, nullptr, FColor::White, 0.0f);
		LineIndex++;
	}
}

//////////////////////////////////////////////////////////////////////////
// UPropagationRuleBlueprintLibrary

UPropagationRuleAsset* UPropagationRuleBlueprintLibrary::CreateSimpleRule(
	const FName& RuleName,
	EPropagationConditionType ConditionType,
	EPropagationActionType ActionType,
	float Threshold,
	float ActionValue)
{
	UPropagationRuleAsset* Rule = NewObject<UPropagationRuleAsset>();
	Rule->RuleName = RuleName;

	// Create condition
	FPropagationCondition Condition;
	Condition.ConditionType = ConditionType;
	Condition.ValueKey = TEXT("Default");
	Condition.Threshold = Threshold;
	Condition.ComparisonOperator = TEXT(">");
	Rule->Conditions.Add(Condition);

	// Create action
	FPropagationAction Action;
	Action.ActionType = ActionType;
	Action.TargetValueKey = TEXT("Default");
	Action.ActionValue = ActionValue;
	Rule->Actions.Add(Action);

	return Rule;
}

UPropagationRuleSet* UPropagationRuleBlueprintLibrary::CreateRuleSetFromRules(
	const FName& RuleSetName,
	const TArray<UPropagationRuleAsset*>& Rules)
{
	UPropagationRuleSet* RuleSet = NewObject<UPropagationRuleSet>();
	RuleSet->RuleSetName = RuleSetName;
	RuleSet->Rules = Rules;

	return RuleSet;
}

UPropagationRuleAsset* UPropagationRuleBlueprintLibrary::CreateFirePropagationRule(float SpreadRate, float DecayRate)
{
	UPropagationRuleAsset* Rule = NewObject<UPropagationRuleAsset>();
	Rule->RuleName = TEXT("FirePropagation");

	// Condition: neighbor temperature > 80
	FPropagationCondition Condition;
	Condition.ConditionType = EPropagationConditionType::NeighborCount;
	Condition.ValueKey = TEXT("Temperature");
	Condition.Threshold = 80.0f;
	Condition.ComparisonOperator = TEXT(">");
	Condition.MinNeighborCount = 1;
	Rule->Conditions.Add(Condition);

	// Action: increase temperature
	FPropagationAction Action;
	Action.ActionType = EPropagationActionType::AddValue;
	Action.TargetValueKey = TEXT("Temperature");
	Action.ActionValue = SpreadRate * 10.0f;
	Action.bClampResult = true;
	Action.ClampMax = 100.0f;
	Rule->Actions.Add(Action);

	return Rule;
}

UPropagationRuleAsset* UPropagationRuleBlueprintLibrary::CreateDiffusionRule(const FName& ValueKey, float DiffusionRate)
{
	UPropagationRuleAsset* Rule = NewObject<UPropagationRuleAsset>();
	Rule->RuleName = FName(*FString::Printf(TEXT("%s_Diffusion"), *ValueKey.ToString()));

	// Condition: has value
	FPropagationCondition Condition;
	Condition.ConditionType = EPropagationConditionType::ValueThreshold;
	Condition.ValueKey = ValueKey;
	Condition.Threshold = 0.1f;
	Condition.ComparisonOperator = TEXT(">");
	Rule->Conditions.Add(Condition);

	// Action: diffuse to neighbors
	FPropagationAction Action;
	Action.ActionType = EPropagationActionType::MultiplyValue;
	Action.TargetValueKey = ValueKey;
	Action.ActionValue = 1.0f - DiffusionRate;
	Rule->Actions.Add(Action);

	return Rule;
}

UPropagationRuleAsset* UPropagationRuleBlueprintLibrary::CreateThresholdRule(const FName& ValueKey, float Threshold, float ActionValue)
{
	UPropagationRuleAsset* Rule = NewObject<UPropagationRuleAsset>();
	Rule->RuleName = FName(*FString::Printf(TEXT("%s_Threshold"), *ValueKey.ToString()));

	// Condition: value above threshold
	FPropagationCondition Condition;
	Condition.ConditionType = EPropagationConditionType::ValueThreshold;
	Condition.ValueKey = ValueKey;
	Condition.Threshold = Threshold;
	Condition.ComparisonOperator = TEXT(">");
	Rule->Conditions.Add(Condition);

	// Action: set value
	FPropagationAction Action;
	Action.ActionType = EPropagationActionType::SetValue;
	Action.TargetValueKey = ValueKey;
	Action.ActionValue = ActionValue;
	Rule->Actions.Add(Action);

	return Rule;
}

bool UPropagationRuleBlueprintLibrary::AreRulesCompatible(UPropagationRuleAsset* Rule1, UPropagationRuleAsset* Rule2)
{
	if (!Rule1 || !Rule2)
	{
		return false;
	}

	return !Rule1->ConflictsWith(Rule2) && !Rule2->ConflictsWith(Rule1);
}

TArray<UPropagationRuleAsset*> UPropagationRuleBlueprintLibrary::GetRuleExecutionOrder(const TArray<UPropagationRuleAsset*>& Rules)
{
	TArray<UPropagationRuleAsset*> SortedRules = Rules;
	
	// Sort by priority
	SortedRules.Sort([](const UPropagationRuleAsset& A, const UPropagationRuleAsset& B)
	{
		return A.GetPriorityValue() > B.GetPriorityValue();
	});

	return SortedRules;
}

UPropagationRuleAsset* UPropagationRuleBlueprintLibrary::CreateRuleFromTemplate(const FName& TemplateName, const TMap<FName, float>& Parameters)
{
	// This would create rules from predefined templates
	// For now, return a simple rule
	return CreateSimpleRule(TemplateName, EPropagationConditionType::Always, EPropagationActionType::SetValue, 0.0f, 1.0f);
}
