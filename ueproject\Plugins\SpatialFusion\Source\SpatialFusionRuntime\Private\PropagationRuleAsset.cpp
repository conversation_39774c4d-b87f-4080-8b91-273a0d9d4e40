// Copyright Epic Games, Inc. All Rights Reserved.

#include "PropagationRuleAsset.h"
#include "Engine/Engine.h"

//////////////////////////////////////////////////////////////////////////
// UPropagationRuleAsset

UPropagationRuleAsset::UPropagationRuleAsset()
{
	// Set default values
	RuleName = TEXT("DefaultRule");
	RuleDescription = TEXT("A new propagation rule");
	Priority = EPropagationRulePriority::Normal;
	ExecutionMode = EPropagationRuleMode::Continuous;
	ExecutionInterval = 1.0f;
	bIsEnabled = true;
	bCanBeDisabled = true;
	bUseAndLogic = true;
	MinConditionsRequired = 1;
	bExecuteAllActions = true;
	MaxExecutionsPerCell = -1;
	MaxTotalExecutions = -1;
	MinTimeBetweenExecutions = 0.0f;
	ExecutionProbability = 1.0f;
	RandomSeed = 0;
	bUseDeterministicRandom = true;
	bAllowParallelExecution = true;
	ComputationalCost = 1.0f;
	bCacheConditionEvaluations = false;
	ConditionCacheDuration = 0.1f;
}

FPrimaryAssetId UPropagationRuleAsset::GetPrimaryAssetId() const
{
	return FPrimaryAssetId(TEXT("PropagationRule"), GetFName());
}

bool UPropagationRuleAsset::ShouldExecute(const FPropagationContext& Context) const
{
	if (!bIsEnabled)
	{
		return false;
	}

	// Check required cell tags
	if (RequiredCellTags.Num() > 0)
	{
		for (const FGameplayTag& RequiredTag : RequiredCellTags)
		{
			if (!Context.HasCellTag(RequiredTag))
			{
				return false;
			}
		}
	}

	// Check excluded cell tags
	if (ExcludedCellTags.Num() > 0)
	{
		for (const FGameplayTag& ExcludedTag : ExcludedCellTags)
		{
			if (Context.HasCellTag(ExcludedTag))
			{
				return false;
			}
		}
	}

	// Check execution constraints
	if (!CheckExecutionConstraints(Context))
	{
		return false;
	}

	// Check execution probability
	if (!RollExecutionProbability(Context))
	{
		return false;
	}

	// Evaluate conditions
	return EvaluateConditions(Context);
}

bool UPropagationRuleAsset::ExecuteRule(FPropagationContext& Context) const
{
	if (!ShouldExecute(Context))
	{
		return false;
	}

	return ExecuteActions(Context);
}

int32 UPropagationRuleAsset::GetPriorityValue() const
{
	switch (Priority)
	{
	case EPropagationRulePriority::VeryLow:
		return 10;
	case EPropagationRulePriority::Low:
		return 30;
	case EPropagationRulePriority::Normal:
		return 50;
	case EPropagationRulePriority::High:
		return 70;
	case EPropagationRulePriority::VeryHigh:
		return 90;
	case EPropagationRulePriority::Critical:
		return 100;
	default:
		return 50;
	}
}

bool UPropagationRuleAsset::ConflictsWith(const UPropagationRuleAsset* OtherRule) const
{
	if (!OtherRule)
	{
		return false;
	}

	// Check if other rule is in our conflicting rules list
	return ConflictingRules.Contains(const_cast<UPropagationRuleAsset*>(OtherRule));
}

bool UPropagationRuleAsset::DependsOn(const UPropagationRuleAsset* OtherRule) const
{
	if (!OtherRule)
	{
		return false;
	}

	// Check if other rule is in our prerequisite rules list
	return PrerequisiteRules.Contains(const_cast<UPropagationRuleAsset*>(OtherRule));
}

TArray<UPropagationRuleAsset*> UPropagationRuleAsset::GetTriggeredRules() const
{
	return TriggeredRules;
}

bool UPropagationRuleAsset::ValidateRule(TArray<FString>& OutErrors) const
{
	OutErrors.Empty();
	bool bIsValid = true;

	// Validate rule name
	if (RuleName.IsNone())
	{
		OutErrors.Add(TEXT("Rule name cannot be empty"));
		bIsValid = false;
	}

	// Validate conditions
	if (Conditions.Num() == 0)
	{
		OutErrors.Add(TEXT("Rule must have at least one condition"));
		bIsValid = false;
	}

	// Validate actions
	if (Actions.Num() == 0)
	{
		OutErrors.Add(TEXT("Rule must have at least one action"));
		bIsValid = false;
	}

	// Validate execution parameters
	if (ExecutionInterval <= 0.0f)
	{
		OutErrors.Add(TEXT("Execution interval must be positive"));
		bIsValid = false;
	}

	if (ExecutionProbability < 0.0f || ExecutionProbability > 1.0f)
	{
		OutErrors.Add(TEXT("Execution probability must be between 0 and 1"));
		bIsValid = false;
	}

	if (MinTimeBetweenExecutions < 0.0f)
	{
		OutErrors.Add(TEXT("Minimum time between executions cannot be negative"));
		bIsValid = false;
	}

	return bIsValid;
}

FString UPropagationRuleAsset::GetFormattedDescription() const
{
	FString FormattedDesc = RuleDescription;
	
	FormattedDesc += FString::Printf(TEXT("\nPriority: %s"), 
		*UEnum::GetValueAsString(Priority));
	
	FormattedDesc += FString::Printf(TEXT("\nConditions: %d"), Conditions.Num());
	FormattedDesc += FString::Printf(TEXT("\nActions: %d"), Actions.Num());
	
	if (ExecutionMode == EPropagationRuleMode::Interval)
	{
		FormattedDesc += FString::Printf(TEXT("\nInterval: %.2fs"), ExecutionInterval);
	}

	return FormattedDesc;
}

UPropagationRuleAsset* UPropagationRuleAsset::CreateVariant(const FName& NewName, const TMap<FName, float>& Modifications) const
{
	UPropagationRuleAsset* NewRule = DuplicateObject<UPropagationRuleAsset>(this, GetOuter());
	NewRule->RuleName = NewName;

	// Apply modifications
	for (const auto& Modification : Modifications)
	{
		const FName& PropertyName = Modification.Key;
		float NewValue = Modification.Value;

		// Apply common property modifications
		if (PropertyName == TEXT("ExecutionInterval"))
		{
			NewRule->ExecutionInterval = NewValue;
		}
		else if (PropertyName == TEXT("ExecutionProbability"))
		{
			NewRule->ExecutionProbability = FMath::Clamp(NewValue, 0.0f, 1.0f);
		}
		else if (PropertyName == TEXT("MinTimeBetweenExecutions"))
		{
			NewRule->MinTimeBetweenExecutions = FMath::Max(0.0f, NewValue);
		}
		// Add more property modifications as needed
	}

	return NewRule;
}

bool UPropagationRuleAsset::EvaluateConditions(const FPropagationContext& Context) const
{
	if (Conditions.Num() == 0)
	{
		return true; // No conditions means always execute
	}

	int32 PassedConditions = 0;

	for (const FPropagationCondition& Condition : Conditions)
	{
		if (Condition.EvaluateCondition(Context))
		{
			PassedConditions++;
			
			if (!bUseAndLogic && PassedConditions >= MinConditionsRequired)
			{
				return true; // OR logic: enough conditions passed
			}
		}
		else if (bUseAndLogic)
		{
			return false; // AND logic: one condition failed
		}
	}

	if (bUseAndLogic)
	{
		return PassedConditions == Conditions.Num(); // All conditions must pass
	}
	else
	{
		return PassedConditions >= MinConditionsRequired; // Minimum conditions must pass
	}
}

bool UPropagationRuleAsset::ExecuteActions(FPropagationContext& Context) const
{
	if (Actions.Num() == 0)
	{
		return false;
	}

	bool bAnyActionSucceeded = false;

	for (const FPropagationAction& Action : Actions)
	{
		bool bActionSucceeded = Action.ExecuteAction(Context);
		
		if (bActionSucceeded)
		{
			bAnyActionSucceeded = true;
			
			if (!bExecuteAllActions)
			{
				break; // Stop after first successful action
			}
		}
	}

	return bAnyActionSucceeded;
}

bool UPropagationRuleAsset::CheckExecutionConstraints(const FPropagationContext& Context) const
{
	// Check maximum executions per cell
	if (MaxExecutionsPerCell > 0)
	{
		// This would need to be tracked by the rule engine
		// For now, we'll assume it's valid
	}

	// Check minimum time between executions
	if (MinTimeBetweenExecutions > 0.0f)
	{
		if (Context.TimeSinceLastExecution < MinTimeBetweenExecutions)
		{
			return false;
		}
	}

	return true;
}

bool UPropagationRuleAsset::RollExecutionProbability(const FPropagationContext& Context) const
{
	if (ExecutionProbability >= 1.0f)
	{
		return true;
	}

	if (ExecutionProbability <= 0.0f)
	{
		return false;
	}

	// Use deterministic random if specified
	if (bUseDeterministicRandom)
	{
		// Create a deterministic seed based on position and time
		int32 Seed = RandomSeed;
		Seed = HashCombine(Seed, GetTypeHash(Context.CellPosition));
		Seed = HashCombine(Seed, FMath::FloorToInt(Context.CurrentTime * 1000.0f));
		
		FRandomStream RandomStream(Seed);
		return RandomStream.FRand() < ExecutionProbability;
	}
	else
	{
		return FMath::FRand() < ExecutionProbability;
	}
}

#if WITH_EDITOR
TSet<FName> UPropagationRuleAsset::GetReferencedValueKeys() const
{
	TSet<FName> ValueKeys;

	// Collect value keys from conditions
	for (const FPropagationCondition& Condition : Conditions)
	{
		if (!Condition.ValueKey.IsNone())
		{
			ValueKeys.Add(Condition.ValueKey);
		}
	}

	// Collect value keys from actions
	for (const FPropagationAction& Action : Actions)
	{
		if (!Action.TargetValueKey.IsNone())
		{
			ValueKeys.Add(Action.TargetValueKey);
		}
		if (!Action.SourceValueKey.IsNone())
		{
			ValueKeys.Add(Action.SourceValueKey);
		}
	}

	return ValueKeys;
}

TSet<FGameplayTag> UPropagationRuleAsset::GetReferencedTags() const
{
	TSet<FGameplayTag> Tags;

	// Collect tags from rule tags
	for (const FGameplayTag& Tag : RuleTags)
	{
		Tags.Add(Tag);
	}

	// Collect tags from required and excluded cell tags
	for (const FGameplayTag& Tag : RequiredCellTags)
	{
		Tags.Add(Tag);
	}
	for (const FGameplayTag& Tag : ExcludedCellTags)
	{
		Tags.Add(Tag);
	}

	// Collect tags from conditions
	for (const FPropagationCondition& Condition : Conditions)
	{
		if (Condition.RequiredTag.IsValid())
		{
			Tags.Add(Condition.RequiredTag);
		}
		for (const FGameplayTag& Tag : Condition.ExcludedTags)
		{
			Tags.Add(Tag);
		}
	}

	// Collect tags from actions
	for (const FPropagationAction& Action : Actions)
	{
		for (const FGameplayTag& Tag : Action.TagsToAdd)
		{
			Tags.Add(Tag);
		}
		for (const FGameplayTag& Tag : Action.TagsToRemove)
		{
			Tags.Add(Tag);
		}
	}

	return Tags;
}

void UPropagationRuleAsset::CheckPerformanceIssues(TArray<FString>& OutWarnings) const
{
	OutWarnings.Empty();

	// Check for too many conditions
	if (Conditions.Num() > 10)
	{
		OutWarnings.Add(FString::Printf(TEXT("Rule has %d conditions, consider simplifying"), Conditions.Num()));
	}

	// Check for too many actions
	if (Actions.Num() > 5)
	{
		OutWarnings.Add(FString::Printf(TEXT("Rule has %d actions, consider splitting into multiple rules"), Actions.Num()));
	}

	// Check for very high execution frequency
	if (ExecutionMode == EPropagationRuleMode::Interval && ExecutionInterval < 0.1f)
	{
		OutWarnings.Add(TEXT("Very high execution frequency may impact performance"));
	}

	// Check for complex conditions
	for (const FPropagationCondition& Condition : Conditions)
	{
		if (Condition.ConditionType == EPropagationConditionType::Custom)
		{
			OutWarnings.Add(TEXT("Custom conditions may have unpredictable performance"));
		}
	}
}

TArray<FPropagationContext> UPropagationRuleAsset::GenerateTestCases() const
{
	TArray<FPropagationContext> TestCases;

	// Generate basic test case
	FPropagationContext BasicTest;
	BasicTest.CellPosition = FVector::ZeroVector;
	BasicTest.CurrentTime = 0.0f;
	BasicTest.DeltaTime = 1.0f / 60.0f;
	BasicTest.TimeSinceLastExecution = 1.0f;
	TestCases.Add(BasicTest);

	// Generate test cases based on conditions
	for (const FPropagationCondition& Condition : Conditions)
	{
		FPropagationContext ConditionTest = BasicTest;
		
		// Set up test data based on condition type
		switch (Condition.ConditionType)
		{
		case EPropagationConditionType::ValueThreshold:
			ConditionTest.SetCellValue(Condition.ValueKey, Condition.Threshold + 10.0f);
			break;
		case EPropagationConditionType::TimeThreshold:
			ConditionTest.TimeSinceLastExecution = Condition.TimeThreshold + 1.0f;
			break;
		case EPropagationConditionType::TagPresence:
			if (Condition.RequiredTag.IsValid())
			{
				ConditionTest.AddCellTag(Condition.RequiredTag);
			}
			break;
		default:
			break;
		}
		
		TestCases.Add(ConditionTest);
	}

	return TestCases;
}
#endif
