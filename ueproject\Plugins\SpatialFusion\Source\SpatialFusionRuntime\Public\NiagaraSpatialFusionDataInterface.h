// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "NiagaraDataInterface.h"
#include "NiagaraCommon.h"
#include "VectorVM.h"
#include "NiagaraSpatialFusionDataInterface.generated.h"

// Forward declarations
class UInfluenceMapManagerBase;
class UFlowFieldManagerBase;
class FNiagaraSystemInstance;

/**
 * Niagara Data Interface for SpatialFusion
 * Provides access to influence maps and flow fields from Niagara particle systems
 */
UCLASS(EditInlineNew, Category = "SpatialFusion", meta = (DisplayName = "SpatialFusion Data Interface"))
class SPATIALFUSIONRUNTIME_API UNiagaraSpatialFusionDataInterface : public UNiagaraDataInterface
{
	GENERATED_BODY()

public:
	UNiagaraSpatialFusionDataInterface();

	//~ UNiagaraDataInterface interface
	virtual void PostInitProperties() override;
	virtual void GetFunctions(TArray<FNiagaraFunctionSignature>& OutFunctions) override;
	virtual void GetVMExternalFunction(const FVMExternalFunctionBindingInfo& BindingInfo, void* InstanceData, FVMExternalFunction &OutFunc) override;
	virtual bool InitPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance) override;
	virtual void DestroyPerInstanceData(void* PerInstanceData, FNiagaraSystemInstance* SystemInstance) override;
	virtual int32 PerInstanceDataSize() const override;
	virtual bool PerInstanceDataPassedToRenderThread() const override { return false; }
	virtual bool HasPreSimulateTick() const override { return true; }
	virtual bool HasPostSimulateTick() const override { return false; }
	virtual void PreSimulateTick(void* PerInstanceData, float DeltaSeconds) override;
	virtual bool Equals(const UNiagaraDataInterface* Other) const override;
	virtual bool CanExecuteOnTarget(ENiagaraSimTarget Target) const override { return Target == ENiagaraSimTarget::CPUSim; }

#if WITH_EDITORONLY_DATA
	virtual void GetParameterDefinitionHLSL(const FNiagaraDataInterfaceGPUParamInfo& ParamInfo, FString& OutHLSL) override;
	virtual bool GetFunctionHLSL(const FNiagaraDataInterfaceGPUParamInfo& ParamInfo, const FNiagaraDataInterfaceGeneratedFunction& FunctionInfo, int FunctionInstanceIndex, FString& OutHLSL) override;
#endif

	// Configuration properties

	/** Reference to the influence map manager */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	TObjectPtr<UInfluenceMapManagerBase> InfluenceMapManager;

	/** Reference to the flow field manager */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	TObjectPtr<UFlowFieldManagerBase> FlowFieldManager;

	/** Whether to use world space coordinates (true) or grid coordinates (false) */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	bool bUseWorldSpace = true;

	/** Default value to return when sampling outside grid bounds */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	float DefaultValue = 0.0f;

	/** Whether to use bilinear interpolation for sampling */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	bool bUseInterpolation = true;

	/** Maximum distance for flow field queries */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	float MaxFlowDistance = 10000.0f;

	/** Whether to normalize flow directions */
	UPROPERTY(EditAnywhere, Category = "SpatialFusion")
	bool bNormalizeFlowDirections = true;

	// Function names for Niagara
	static const FName GetInfluenceValueName;
	static const FName GetInfluenceGradientName;
	static const FName GetFlowDirectionName;
	static const FName GetFlowDistanceName;
	static const FName IsPositionBlockedName;
	static const FName GetGridDimensionsName;
	static const FName GetCellSizeName;
	static const FName GetWorldOriginName;
	static const FName SampleInfluenceAtPositionsName;
	static const FName SampleFlowAtPositionsName;
	static const FName GetInfluenceValueWithKeyName;
	static const FName GetNearestGoalPositionName;
	static const FName GetDistanceToNearestGoalName;

protected:
	/**
	 * Per-instance data for the data interface
	 */
	struct FNiagaraSpatialFusionInstanceData
	{
		/** Cached reference to influence map manager */
		TWeakObjectPtr<UInfluenceMapManagerBase> CachedInfluenceMapManager;

		/** Cached reference to flow field manager */
		TWeakObjectPtr<UFlowFieldManagerBase> CachedFlowFieldManager;

		/** Cached grid dimensions */
		FVector GridDimensions = FVector::ZeroVector;

		/** Cached cell size */
		float CellSize = 100.0f;

		/** Cached world origin */
		FVector WorldOrigin = FVector::ZeroVector;

		/** Whether the cache is valid */
		bool bCacheValid = false;

		/** Last update time */
		float LastUpdateTime = 0.0f;

		/** Update the cache */
		void UpdateCache(UNiagaraSpatialFusionDataInterface* Interface);

		/** Check if cache needs updating */
		bool NeedsCacheUpdate(float CurrentTime) const;
	};

	// VM function implementations

	/** Get influence value at position */
	void GetInfluenceValue(FVectorVMExternalFunctionContext& Context);

	/** Get influence gradient at position */
	void GetInfluenceGradient(FVectorVMExternalFunctionContext& Context);

	/** Get flow direction at position */
	void GetFlowDirection(FVectorVMExternalFunctionContext& Context);

	/** Get distance to nearest goal */
	void GetFlowDistance(FVectorVMExternalFunctionContext& Context);

	/** Check if position is blocked */
	void IsPositionBlocked(FVectorVMExternalFunctionContext& Context);

	/** Get grid dimensions */
	void GetGridDimensions(FVectorVMExternalFunctionContext& Context);

	/** Get cell size */
	void GetCellSize(FVectorVMExternalFunctionContext& Context);

	/** Get world origin */
	void GetWorldOrigin(FVectorVMExternalFunctionContext& Context);

	/** Sample influence at multiple positions */
	void SampleInfluenceAtPositions(FVectorVMExternalFunctionContext& Context);

	/** Sample flow at multiple positions */
	void SampleFlowAtPositions(FVectorVMExternalFunctionContext& Context);

	/** Get influence value with specific key */
	void GetInfluenceValueWithKey(FVectorVMExternalFunctionContext& Context);

	/** Get nearest goal position */
	void GetNearestGoalPosition(FVectorVMExternalFunctionContext& Context);

	/** Get distance to nearest goal */
	void GetDistanceToNearestGoal(FVectorVMExternalFunctionContext& Context);

	// Helper functions

	/**
	 * Convert world position to grid position if needed
	 * @param WorldPos - World position
	 * @param InstanceData - Per-instance data
	 * @return Grid position
	 */
	FVector WorldToGridPosition(const FVector& WorldPos, const FNiagaraSpatialFusionInstanceData* InstanceData) const;

	/**
	 * Sample influence value with interpolation
	 * @param Position - Position to sample at
	 * @param ValueKey - Value key to sample
	 * @param InstanceData - Per-instance data
	 * @return Sampled value
	 */
	float SampleInfluenceValueInterpolated(const FVector& Position, const FName& ValueKey, const FNiagaraSpatialFusionInstanceData* InstanceData) const;

	/**
	 * Sample flow direction with interpolation
	 * @param Position - Position to sample at
	 * @param InstanceData - Per-instance data
	 * @return Sampled flow direction
	 */
	FVector SampleFlowDirectionInterpolated(const FVector& Position, const FNiagaraSpatialFusionInstanceData* InstanceData) const;

	/**
	 * Check if position is within grid bounds
	 * @param Position - Position to check
	 * @param InstanceData - Per-instance data
	 * @return True if within bounds
	 */
	bool IsWithinGridBounds(const FVector& Position, const FNiagaraSpatialFusionInstanceData* InstanceData) const;

	/**
	 * Get function signature for a specific function
	 * @param FunctionName - Name of the function
	 * @return Function signature
	 */
	FNiagaraFunctionSignature GetFunctionSignature(const FName& FunctionName) const;

	/**
	 * Create input parameter for function signature
	 * @param Name - Parameter name
	 * @param Type - Parameter type
	 * @return Parameter definition
	 */
	FNiagaraVariable CreateInputParam(const FName& Name, const FNiagaraTypeDefinition& Type) const;

	/**
	 * Create output parameter for function signature
	 * @param Name - Parameter name
	 * @param Type - Parameter type
	 * @return Parameter definition
	 */
	FNiagaraVariable CreateOutputParam(const FName& Name, const FNiagaraTypeDefinition& Type) const;
};
