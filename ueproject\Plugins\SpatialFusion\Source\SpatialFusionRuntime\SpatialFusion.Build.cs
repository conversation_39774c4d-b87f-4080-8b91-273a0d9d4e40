// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class SpatialFusionRuntime : ModuleRules
{
    public SpatialFusionRuntime(ReadOnlyTargetRules Target)
        : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicIncludePaths.AddRange(
            new string[]
            {
                // ... add public include paths required here ...
            }
        );

        PrivateIncludePaths.AddRange(
            new string[]
            {
                // ... add other private include paths required here ...
            }
        );

        PublicDependencyModuleNames.AddRange(
            new string[] { "Core", "CoreUObject", "Engine", "GameplayTags" }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[] { "Slate", "SlateCore", "RenderCore", "RHI" }
        );

        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {
                // ... add any modules that your module loads dynamically here ...
            }
        );
    }
}
