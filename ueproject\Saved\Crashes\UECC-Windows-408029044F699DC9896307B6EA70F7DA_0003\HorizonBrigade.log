Stats thread started at 5.472507
By default, prioritizing project plugin (E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealMC5a95177e2afcV1/UnrealMCPython.uplugin) over the corresponding engine version (../../../Engine/Plugins/Marketplace/UnrealMC5a95177e2afcV1/UnrealMCPython.uplugin).
By default, prioritizing project plugin (E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealSharp/UnrealSharp.uplugin) over the corresponding engine version (../../../Engine/Plugins/Marketplace/UnrealSharp/UnrealSharp.uplugin).
Metadata set : systemresolution.resx="1280"
Metadata set : systemresolution.resy="720"
ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-408029044F699DC9896307B6EA70F7DA
         Session CrashGUID >====================================================
No local boot hotfix file found at: [E:/Unreal Projects/HorizonBrigade/ueproject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
Pre-Initializing Audio Device Manager...
AudioInfo: 'OPUS' Registered
Lib vorbis DLL was dynamically loaded.
AudioInfo: 'OGG' Registered
AudioInfo: 'ADPCM' Registered
AudioInfo: 'PCM' Registered
AudioInfo: 'BINKA' Registered
AudioInfo: 'RADA' Registered
Audio Device Manager Pre-Initialized
Looking for build plugins target receipt
Found matching target receipt: E:/Unreal Projects/HorizonBrigade/ueproject/Binaries/Win64/HorizonBrigadeEditor.target
Looking for enabled plugins target receipt
Loading Linux ini files took 0.78 seconds
Found matching target receipt: E:/Unreal Projects/HorizonBrigade/ueproject/Binaries/Win64/HorizonBrigadeEditor.target
Asset registry cache read as 125.3 MiB from E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/CachedAssetRegistry_0.bin.
Loading Mac ini files took 0.21 seconds
Mounting Engine plugin ChaosCloth
Mounting Engine plugin ChaosInsights
Mounting Engine plugin ChaosVD
Mounting Engine plugin Bridge
Mounting Engine plugin Chooser
Mounting Engine plugin CmdLinkServer
Mounting Engine plugin EnhancedInput
Mounting Engine plugin Fab
Mounting Engine plugin FastBuildController
Mounting Engine plugin MassInsights
Mounting Engine plugin MeshPainting
Mounting Engine plugin PCG
Mounting Engine plugin IoStoreInsights
Mounting Engine plugin RenderGraphInsights
Mounting Engine plugin TraceUtilities
Mounting Engine plugin UbaController
Mounting Engine plugin WorldMetrics
Mounting Engine plugin XGEController
Mounting Engine plugin AISupport
Mounting Engine plugin EnvironmentQueryEditor
Mounting Engine plugin MassAI
Mounting Engine plugin OodleNetwork
Mounting Engine plugin AssetManagerEditor
Mounting Engine plugin AssetReferenceRestrictions
Mounting Engine plugin BlueprintHeaderView
Mounting Engine plugin BlueprintMaterialTextureNodes
Mounting Engine plugin ChangelistReview
Mounting Engine plugin ColorGrading
Mounting Engine plugin CurveEditorTools
Mounting Engine plugin DataValidation
Mounting Engine plugin CryptoKeys
Mounting Engine plugin EditorDebugTools
Mounting Engine plugin EditorScriptingUtilities
Mounting Engine plugin FacialAnimation
Mounting Engine plugin EngineAssetDefinitions
Mounting Engine plugin GeometryMode
Mounting Engine plugin GameplayTagsEditor
Mounting Engine plugin MacGraphicsSwitching
Mounting Engine plugin MaterialAnalyzer
Mounting Engine plugin MeshLODToolset
Mounting Engine plugin MobileLauncherProfileWizard
Mounting Engine plugin ModelingToolsEditorMode
Mounting Engine plugin ProxyLODPlugin
Mounting Engine plugin PluginBrowser
Mounting Engine plugin SequencerAnimTools
Mounting Engine plugin StylusInput
Mounting Engine plugin UVEditor
Mounting Engine plugin UMGWidgetPreview
Mounting Engine plugin WorldPartitionHLODUtilities
Mounting Engine plugin Cascade
Mounting Engine plugin Niagara
Mounting Engine plugin SpeedTreeImporter
Mounting Engine plugin NiagaraFluids
Mounting Engine plugin NiagaraSimCaching
Mounting Engine plugin AlembicImporter
Mounting Project plugin UnrealMCPython
Mounting Project plugin UnrealSharp
Mounting Engine plugin AndroidMedia
Mounting Engine plugin AvfMedia
Mounting Engine plugin ImgMedia
Mounting Engine plugin MediaCompositing
Mounting Engine plugin MediaPlate
Mounting Engine plugin MediaPlayerEditor
Mounting Engine plugin WebMMedia
Mounting Engine plugin WmfMedia
Mounting Engine plugin DatasmithContent
Loading TVOS ini files took 0.18 seconds
Mounting Engine plugin GLTFExporter
Mounting Engine plugin VariantManager
Mounting Engine plugin VariantManagerContent
Mounting Engine plugin InterchangeEditor
Mounting Engine plugin InterchangeAssets
Mounting Engine plugin Interchange
Mounting Engine plugin TcpMessaging
Mounting Engine plugin UdpMessaging
Mounting Engine plugin EOSShared
Mounting Engine plugin OnlineBase
Mounting Engine plugin OnlineServices
Mounting Engine plugin OnlineSubsystemNull
Mounting Engine plugin OnlineSubsystemUtils
Mounting Engine plugin MetaHumanSDK
Loading Unix ini files took 0.08 seconds
Mounting Engine plugin PCGExternalDataInterop
Mounting Engine plugin PCGGeometryScriptInterop
Mounting Engine plugin LauncherChunkInstaller
Mounting Engine plugin ActorLayerUtilities
Mounting Engine plugin AndroidDeviceProfileSelector
Mounting Engine plugin AndroidFileServer
Mounting Engine plugin AndroidMoviePlayer
Mounting Engine plugin AndroidPermission
Mounting Engine plugin AppleImageUtils
Mounting Engine plugin AppleMoviePlayer
Mounting Engine plugin ArchVisCharacter
Mounting Engine plugin AudioCapture
Mounting Engine plugin AssetTags
Mounting Engine plugin AudioSynesthesia
Mounting Engine plugin AudioWidgets
Mounting Engine plugin CableComponent
Mounting Engine plugin ChunkDownloader
Mounting Engine plugin ComputeFramework
Loading VulkanPC ini files took 0.05 seconds
Mounting Engine plugin CustomMeshComponent
Mounting Engine plugin DataRegistry
Mounting Engine plugin ExampleDeviceProfileSelector
Mounting Engine plugin GameFeatures
Mounting Engine plugin GameplayAbilities
Mounting Engine plugin GameplayStateTree
Mounting Engine plugin GeometryCache
Mounting Engine plugin GeometryProcessing
Mounting Engine plugin GeometryScripting
Mounting Engine plugin GooglePAD
Mounting Engine plugin GoogleCloudMessaging
Mounting Engine plugin HairStrands
Loading Windows ini files took 0.10 seconds
Mounting Engine plugin InputDebugging
Mounting Engine plugin InstancedActors
Mounting Engine plugin IOSDeviceProfileSelector
Mounting Engine plugin LinuxDeviceProfileSelector
Mounting Engine plugin LocationServicesBPLibrary
Mounting Engine plugin MassGameplay
Mounting Engine plugin MeshModelingToolset
Mounting Engine plugin Metasound
Mounting Engine plugin MobilePatchingUtils
Mounting Engine plugin ModularGameplay
Mounting Engine plugin MsQuic
Mounting Engine plugin NavCorridor
Mounting Engine plugin ProceduralMeshComponent
Mounting Engine plugin PropertyAccessEditor
Mounting Engine plugin PropertyBindingUtils
Mounting Engine plugin ResonanceAudio
Mounting Engine plugin RigVM
Mounting Engine plugin SignificanceManager
Mounting Engine plugin SmartObjects
Mounting Engine plugin SoundFields
Mounting Engine plugin StateTree
Mounting Engine plugin Synthesis
Mounting Engine plugin WaveTable
Mounting Engine plugin WebMMoviePlayer
Mounting Engine plugin WindowsDeviceProfileSelector
Mounting Engine plugin WindowsMoviePlayer
Mounting Engine plugin WorldConditions
Mounting Engine plugin ZoneGraph
Mounting Engine plugin ZoneGraphAnnotations
Mounting Engine plugin CameraCalibrationCore
Mounting Engine plugin Takes
Mounting Engine plugin ActorSequence
Mounting Engine plugin OnlineSubsystem
Mounting Engine plugin SequencerScripting
Mounting Engine plugin TemplateSequence
Mounting Engine plugin NNEDenoiser
Mounting Engine plugin Paper2D
Mounting Engine plugin NNERuntimeORT
Mounting Engine plugin ACLPlugin
Mounting Engine plugin AnimationData
Mounting Engine plugin AnimationModifierLibrary
Mounting Engine plugin AnimationWarping
Mounting Engine plugin BlendStack
Mounting Engine plugin BlendSpaceMotionAnalysis
Mounting Engine plugin ControlRig
Mounting Engine plugin DeformerGraph
Mounting Engine plugin ControlRigModules
Mounting Engine plugin ControlRigSpline
Mounting Engine plugin GameplayInsights
Mounting Engine plugin IKRig
Mounting Engine plugin PoseSearch
Mounting Engine plugin RigLogic
Loading VisionOS ini files took 0.22 seconds
Mounting Engine plugin TweeningUtils
Mounting Engine plugin CameraShakePreviewer
Mounting Engine plugin EngineCameras
Mounting Engine plugin SkeletalMeshModelingTools
Mounting Engine plugin GameplayCameras
Mounting Engine plugin AnimationSharing
Mounting Engine plugin CLionSourceCodeAccess
Mounting Engine plugin CodeLiteSourceCodeAccess
Mounting Engine plugin KDevelopSourceCodeAccess
Mounting Engine plugin N10XSourceCodeAccess
Mounting Engine plugin GitSourceControl
Mounting Engine plugin NamingTokens
Mounting Engine plugin NullSourceCodeAccess
Mounting Engine plugin PerforceSourceControl
Mounting Engine plugin PixWinPlugin
Mounting Engine plugin PlasticSourceControl
Mounting Engine plugin PluginUtils
Mounting Engine plugin ProjectLauncher
Mounting Engine plugin PropertyAccessNode
Mounting Engine plugin RenderDocPlugin
Mounting Engine plugin RiderSourceCodeAccess
Mounting Engine plugin TextureFormatOodle
Mounting Engine plugin SubversionSourceControl
Mounting Engine plugin VisualStudioSourceCodeAccess
Mounting Engine plugin UObjectPlugin
Mounting Engine plugin VisualStudioCodeSourceCodeAccess
Mounting Engine plugin XCodeSourceCodeAccess
Mounting Engine plugin AdvancedRenamer
Mounting Engine plugin AutomationUtils
Mounting Engine plugin DumpGPUServices
Mounting Engine plugin BackChannel
Mounting Engine plugin ChaosCaching
Mounting Engine plugin Buoyancy
Mounting Engine plugin ChaosEditor
Mounting Engine plugin ChaosSolverPlugin
Mounting Engine plugin ChaosUserDataPT
Mounting Engine plugin CharacterAI
Mounting Engine plugin ChaosNiagara
Mounting Engine plugin Dataflow
Mounting Engine plugin DaySequence
Mounting Engine plugin EditorDataStorage
Mounting Engine plugin EditorDataStorageFeatures
Mounting Engine plugin EditorPerformance
Mounting Engine plugin EditorTelemetry
Mounting Engine plugin Fracture
Mounting Engine plugin FullBodyIK
Mounting Engine plugin TargetingSystem
Mounting Engine plugin GeometryCollectionPlugin
Mounting Engine plugin GeometryDataflow
Mounting Engine plugin GeometryFlow
Mounting Engine plugin Landmass
Mounting Engine plugin LevelSequenceNavigatorBridge
Mounting Engine plugin LocalizableMessage
Mounting Engine plugin LowLevelNetTrace
Mounting Engine plugin MeshModelingToolsetExp
Mounting Engine plugin NFORDenoise
Mounting Engine plugin PCGBiomeCore
Mounting Engine plugin PCGBiomeSample
Mounting Engine plugin PlanarCut
Mounting Engine plugin PlatformCrypto
Mounting Engine plugin PythonScriptPlugin
Mounting Engine plugin RuntimeTelemetry
Mounting Engine plugin SequenceNavigator
Mounting Engine plugin SkeletalReduction
Mounting Engine plugin ToolPresets
Mounting Engine plugin Water
Mounting Engine plugin WaterAdvanced
Mounting Engine plugin WaterExtras
Mounting Engine plugin InterchangeTests
Mounting Engine plugin LevelSequenceEditor
Mounting Engine plugin ContentBrowserAssetDataSource
Mounting Engine plugin ContentBrowserClassDataSource
Mounting Engine plugin ContentBrowserFileDataSource
Mounting Engine plugin BaseCharacterFXEditor
Mounting Engine plugin CompositeCore
Mounting Engine plugin LightMixer
Mounting Engine plugin ObjectMixer
Mounting Engine plugin MotionTrajectory
Mounting Engine plugin PortableObjectFileDataSource
Mounting Engine plugin PCGInstancedActorsInterop
Mounting Engine plugin PCGNiagaraInterop
Mounting Engine plugin PCGWaterInterop
Mounting Engine plugin OnlineSubsystemGooglePlay
Mounting Engine plugin OnlineSubsystemIOS
Mounting Engine plugin XInputDevice
Mounting Project plugin LyraGame
Mounting Project plugin RGOAP
Mounting Project plugin SpatialFusion
Mounting Project plugin GenerativeAISupport
Loading IOS ini files took 2.15 seconds
Loading Android ini files took 2.19 seconds
Revision control is disabled
Revision control is disabled
Revision control is disabled
Loaded "D:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
Using libcurl 8.12.1
 - built for Windows
 - supports SSL with OpenSSL/1.1.1t
 - supports HTTP deflate (compression) using libz 1.3
 - other features:
     CURL_VERSION_SSL
     CURL_VERSION_LIBZ
     CURL_VERSION_IPV6
     CURL_VERSION_ASYNCHDNS
     CURL_VERSION_LARGEFILE
     CURL_VERSION_TLSAUTH_SRP
     CURL_VERSION_HTTP2
 CurlRequestOptions (configurable via config and command line):
 - bVerifyPeer = true  - Libcurl will verify peer certificate
 - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
 - bDontReuseConnections = false  - Libcurl will reuse connections
 - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
 - LocalHostAddr = Default
 - BufferSize = 65536
CreateHttpThread using FCurlMultiPollEventLoopHttpThread
Creating http thread with maximum 256 concurrent requests
WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
OSS: Created online subsystem instance for: NULL
OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
File 'WinPixGpuCapturer.dll' does not exist
PIX capture plugin failed to initialize! Check that the process is launched from PIX.
Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
Started StudioTelemetry Session
NFORDenoise function starting up
Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
ExecutableName: UnrealEditor.exe
Build: ++UE5+Release-5.6-***********
Platform=WindowsEditor
MachineId=30312e0b49ed30022bc720a613935ce6
DeviceId=
Engine Version: 5.6.0-43139311+++UE5+Release-5.6
Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
Net CL: 43139311
OS: Windows 11 (22H2) [10.0.22621.4317] (), CPU: Intel(R) Core(TM) i5-9400F CPU @ 2.90GHz, GPU: NVIDIA GeForce RTX 2080 Ti
Compiled (64-bit): May 31 2025 16:29:58
Architecture: x64
Compiled with Visual C++: 19.38.33130.00
Build Configuration: Development
Branch Name: ++UE5+Release-5.6
Command Line: 
Base Directory: D:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
Allocator: Mimalloc
Installed Engine Build: 1
This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
Number of dev versions registered: 37
  Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
  Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
  Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
  Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
  Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
  Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
  Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
  Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
  Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
  Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
  Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
  Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
  Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
  Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
  Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
  Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
  Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
  Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
  Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
  FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
  FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
  FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
  FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
  Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
  Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
  Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
  Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
  Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
  Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
  Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
  UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
  UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
  UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
  Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
  Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
  Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
  LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
Presizing for max 25165824 objects, including 0 objects not considered by GC.
Object subsystem initialized
Set CVar [[con.DebugEarlyDefault:1]]
CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
Set CVar [[r.setres:1280x720]]
CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
CVar [[QualityLevelMapping:high]] deferred - dummy variable created
CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
Set CVar [[r.PSOPrecache.GlobalShaders:1]]
Set CVar [[r.VRS.EnableSoftware:1]]
Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
Set CVar [[r.VSync:0]]
Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
Set CVar [[r.GPUCrashDebugging:0]]
CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
Set CVar [[r.Mobile.ShadingPath:0]]
Set CVar [[r.Mobile.AllowDeferredShadingOpenGL:0]]
Set CVar [[r.Mobile.SupportGPUScene:1]]
Set CVar [[r.Mobile.AntiAliasing:1]]
Set CVar [[r.Mobile.FloatPrecisionMode:0]]
Set CVar [[r.Mobile.AllowDitheredLODTransition:0]]
Set CVar [[r.Mobile.VirtualTextures:0]]
Set CVar [[r.DiscardUnusedQuality:0]]
Set CVar [[r.AllowOcclusionQueries:1]]
Set CVar [[r.MinScreenRadiusForLights:0.030000]]
Set CVar [[r.MinScreenRadiusForDepthPrepass:0.030000]]
Set CVar [[r.PrecomputedVisibilityWarning:0]]
Set CVar [[r.TextureStreaming:1]]
Set CVar [[Compat.UseDXT5NormalMaps:0]]
Set CVar [[r.VirtualTextures:1]]
Set CVar [[r.VirtualTexturedLightmaps:0]]
Set CVar [[r.VT.TileSize:128]]
Set CVar [[r.VT.TileBorderSize:4]]
Set CVar [[r.VT.AnisotropicFiltering:0]]
Set CVar [[r.VT.EnableAutoImport:0]]
CVar [[bEnableVirtualTextureOpacityMask:0]] deferred - dummy variable created
Set CVar [[r.MeshPaintVirtualTexture.Support:1]]
Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
Set CVar [[r.StaticMesh.DefaultMeshPaintTextureSupport:1]]
Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:4]]
Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
Set CVar [[r.vt.rvt.EnableBaseColor:1]]
Set CVar [[r.vt.rvt.EnableBaseColorRoughness:1]]
Set CVar [[r.vt.rvt.EnableBaseColorSpecular:1]]
Set CVar [[r.vt.rvt.EnableMask4:1]]
Set CVar [[r.vt.rvt.EnableWorldHeight:1]]
Set CVar [[r.vt.rvt.EnableDisplacement:1]]
Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
CVar [[WorkingColorSpaceChoice:sRGB]] deferred - dummy variable created
CVar [[RedChromaticityCoordinate:(X=0.640000,Y=0.330000)]] deferred - dummy variable created
CVar [[GreenChromaticityCoordinate:(X=0.300000,Y=0.600000)]] deferred - dummy variable created
CVar [[BlueChromaticityCoordinate:(X=0.150000,Y=0.060000)]] deferred - dummy variable created
CVar [[WhiteChromaticityCoordinate:(X=0.312700,Y=0.329000)]] deferred - dummy variable created
Set CVar [[r.LegacyLuminanceFactors:0]]
Set CVar [[r.ClearCoatNormal:0]]
Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
Set CVar [[r.ReflectionMethod:1]]
Set CVar [[r.ReflectionCaptureResolution:128]]
Set CVar [[r.ReflectionEnvironmentLightmapMixBasedOnRoughness:1]]
Set CVar [[r.Lumen.HardwareRayTracing:1]]
Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
Set CVar [[r.Lumen.TraceMeshSDFs:0]]
Set CVar [[r.Lumen.ScreenTracingSource:0]]
Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
Set CVar [[r.MegaLights.EnableForProject:0]]
Set CVar [[r.RayTracing.Shadows:0]]
Set CVar [[r.Shadow.Virtual.Enable:1]]
Set CVar [[r.RayTracing:0]]
Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
Set CVar [[r.RayTracing.UseTextureLod:0]]
Set CVar [[r.PathTracing:1]]
Set CVar [[r.GenerateMeshDistanceFields:1]]
Set CVar [[r.DistanceFields.DefaultVoxelDensity:0.200000]]
Set CVar [[r.Nanite.ProjectEnabled:1]]
Set CVar [[r.AllowStaticLighting:0]]
Set CVar [[r.NormalMapsForStaticLighting:0]]
Set CVar [[r.ForwardShading:0]]
Set CVar [[r.VertexFoggingForOpaque:1]]
Set CVar [[r.SeparateTranslucency:1]]
Set CVar [[r.TranslucentSortPolicy:0]]
CVar [[TranslucentSortAxis:(X=0.000000,Y=-1.000000,Z=0.000000)]] deferred - dummy variable created
Set CVar [[r.LocalFogVolume.ApplyOnTranslucent:0]]
Set CVar [[xr.VRS.FoveationLevel:0]]
Set CVar [[xr.VRS.DynamicFoveation:0]]
Set CVar [[r.CustomDepth:1]]
Set CVar [[r.CustomDepthTemporalAAJitter:1]]
Set CVar [[r.PostProcessing.PropagateAlpha:0]]
Set CVar [[r.Deferred.SupportPrimitiveAlphaHoldout:0]]
Set CVar [[r.DefaultFeature.Bloom:1]]
Set CVar [[r.DefaultFeature.AmbientOcclusion:1]]
Set CVar [[r.DefaultFeature.AmbientOcclusionStaticFraction:1]]
Set CVar [[r.DefaultFeature.AutoExposure:0]]
Set CVar [[r.DefaultFeature.AutoExposure.Method:0]]
Set CVar [[r.DefaultFeature.AutoExposure.Bias:1.000000]]
Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.800000]]
Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.800000]]
Set CVar [[r.DefaultFeature.MotionBlur:0]]
Set CVar [[r.DefaultFeature.LensFlare:0]]
Set CVar [[r.TemporalAA.Upsampling:1]]
Set CVar [[r.AntiAliasingMethod:4]]
Set CVar [[r.MSAACount:4]]
Set CVar [[r.DefaultFeature.LightUnits:1]]
Set CVar [[r.DefaultBackBufferPixelFormat:4]]
Set CVar [[r.ScreenPercentage.Default:100.000000]]
Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:1]]
Set CVar [[r.ScreenPercentage.Default.Mobile.Mode:0]]
Set CVar [[r.ScreenPercentage.Default.VR.Mode:0]]
Set CVar [[r.ScreenPercentage.Default.PathTracer.Mode:0]]
Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
Set CVar [[r.StencilForLODDither:0]]
Set CVar [[r.EarlyZPass:3]]
Set CVar [[r.EarlyZPassOnlyMaterialMasking:0]]
Set CVar [[r.Shadow.CSMCaching:0]]
Set CVar [[r.DBuffer:1]]
Set CVar [[r.ClearSceneMethod:1]]
Set CVar [[r.VelocityOutputPass:0]]
Set CVar [[r.Velocity.EnableVertexDeformation:2]]
Set CVar [[r.SelectiveBasePassOutputs:0]]
CVar [[bDefaultParticleCutouts:0]] deferred - dummy variable created
Set CVar [[fx.GPUSimulationTextureSizeX:1024]]
Set CVar [[fx.GPUSimulationTextureSizeY:1024]]
Set CVar [[r.AllowGlobalClipPlane:0]]
Set CVar [[r.GBufferFormat:1]]
Set CVar [[r.MorphTarget.Mode:1]]
Set CVar [[r.MorphTarget.MaxBlendWeight:5.000000]]
Set CVar [[r.SupportSkyAtmosphere:1]]
Set CVar [[r.SupportSkyAtmosphereAffectsHeightFog:1]]
Set CVar [[r.SupportExpFogMatchesVolumetricFog:0]]
Set CVar [[r.SupportLocalFogVolumes:1]]
Set CVar [[r.SupportCloudShadowOnForwardLitTranslucent:0]]
Set CVar [[r.LightFunctionAtlas.Format:0]]
Set CVar [[r.VolumetricFog.LightFunction:1]]
Set CVar [[r.Deferred.UsesLightFunctionAtlas:1]]
Set CVar [[r.SingleLayerWater.UsesLightFunctionAtlas:0]]
Set CVar [[r.Translucent.UsesLightFunctionAtlas:0]]
Set CVar [[r.Translucent.UsesIESProfiles:0]]
Set CVar [[r.Translucent.UsesRectLights:0]]
Set CVar [[r.Translucent.UsesShadowedLocalLights:0]]
Set CVar [[vr.InstancedStereo:0]]
Set CVar [[r.MobileHDR:1]]
Set CVar [[vr.MobileMultiView:0]]
Set CVar [[r.Mobile.UseHWsRGBEncoding:0]]
Set CVar [[vr.RoundRobinOcclusion:0]]
Set CVar [[r.MeshStreaming:0]]
Set CVar [[r.HeterogeneousVolumes:1]]
Set CVar [[r.HeterogeneousVolumes.Shadows:0]]
Set CVar [[r.Translucency.HeterogeneousVolumes:0]]
Set CVar [[r.WireframeCullThreshold:5.000000]]
Set CVar [[r.SupportStationarySkylight:1]]
Set CVar [[r.SupportLowQualityLightmaps:1]]
Set CVar [[r.SupportPointLightWholeSceneShadows:1]]
Set CVar [[r.Shadow.TranslucentPerObject.ProjectEnabled:0]]
Set CVar [[r.Water.SingleLayerWater.SupportCloudShadow:0]]
Set CVar [[r.Substrate:0]]
Set CVar [[r.Substrate.OpaqueMaterialRoughRefraction:0]]
Set CVar [[r.Refraction.Blur:1]]
Set CVar [[r.Substrate.Debug.AdvancedVisualizationShaders:0]]
Set CVar [[r.Substrate.EnableLayerSupport:0]]
Set CVar [[r.Material.RoughDiffuse:0]]
Set CVar [[r.Material.EnergyConservation:0]]
Set CVar [[r.Material.DefaultAutoMaterialUsage:1]]
Set CVar [[r.OIT.SortedPixels:0]]
Set CVar [[r.HairStrands.LODMode:1]]
Set CVar [[r.SkinCache.CompileShaders:1]]
Set CVar [[r.VRS.Support:1]]
Set CVar [[r.SkinCache.SkipCompilingGPUSkinVF:0]]
Set CVar [[r.SkinCache.DefaultBehavior:1]]
Set CVar [[r.SkinCache.SceneMemoryLimitInMB:128.000000]]
Set CVar [[r.Mobile.EnableStaticAndCSMShadowReceivers:1]]
Set CVar [[r.Mobile.EnableMovableLightCSMShaderCulling:1]]
Set CVar [[r.Mobile.Forward.EnableLocalLights:1]]
Set CVar [[r.Mobile.Forward.EnableClusteredReflections:0]]
Set CVar [[r.Mobile.AllowDistanceFieldShadows:1]]
Set CVar [[r.Mobile.EnableMovableSpotlightsShadow:0]]
Set CVar [[r.GPUSkin.Support16BitBoneIndex:0]]
Set CVar [[r.GPUSkin.Limit2BoneInfluences:0]]
Set CVar [[r.SupportDepthOnlyIndexBuffers:0]]
Set CVar [[r.SupportReversedIndexBuffers:0]]
Set CVar [[r.Mobile.AmbientOcclusion:0]]
Set CVar [[r.Mobile.DBuffer:0]]
Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:0]]
Set CVar [[r.GPUSkin.AlwaysUseDeformerForUnlimitedBoneInfluences:0]]
Set CVar [[r.GPUSkin.UnlimitedBoneInfluencesThreshold:8]]
CVar [[DefaultBoneInfluenceLimit:(Default=0,PerPlatform=())]] deferred - dummy variable created
Set CVar [[r.Mobile.ScreenSpaceReflections:0]]
Set CVar [[r.Mobile.SupportsGen4TAA:1]]
CVar [[bStreamSkeletalMeshLODs:(Default=False,PerPlatform=())]] deferred - dummy variable created
CVar [[bDiscardSkeletalMeshOptionalLODs:(Default=False,PerPlatform=())]] deferred - dummy variable created
CVar [[VisualizeCalibrationCustomMaterialPath:None]] deferred - dummy variable created
Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
Set CVar [[s.AsyncLoadingThreadEnabled:1]]
Set CVar [[s.EventDrivenLoaderEnabled:1]]
Set CVar [[s.WarnIfTimeLimitExceeded:0]]
Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
Set CVar [[s.TimeLimitExceededMinTime:0.005]]
Set CVar [[s.UseBackgroundLevelStreaming:1]]
Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
Set CVar [[s.FlushStreamingOnExit:1]]
CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
Set CVar [[gc.FlushStreamingOnGC:0]]
Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
Set CVar [[gc.AllowParallelGC:1]]
Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
Set CVar [[gc.MaxObjectsInEditor:25165824]]
Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
Set CVar [[gc.CreateGCClusters:1]]
Set CVar [[gc.MinGCClusterSize:5]]
Set CVar [[gc.AssetClustreringEnabled:0]]
Set CVar [[gc.ActorClusteringEnabled:0]]
Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
Set CVar [[gc.GarbageEliminationEnabled:1]]
Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
Metadata set : systemresolution.resx="2560"
Metadata set : systemresolution.resy="1080"
Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
Set CVar [[r.SkeletalMeshLODBias:0]]
Set CVar [[r.ViewDistanceScale:1.0]]
Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
Set CVar [[r.FXAA.Quality:4]]
Set CVar [[r.TemporalAA.Quality:2]]
Set CVar [[r.TSR.History.R11G11B10:1]]
Set CVar [[r.TSR.History.ScreenPercentage:200]]
Set CVar [[r.TSR.History.UpdateQuality:3]]
Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
Set CVar [[r.TSR.ReprojectionField:1]]
Set CVar [[r.TSR.Resurrection:1]]
Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
Set CVar [[r.LightFunctionQuality:1]]
Set CVar [[r.ShadowQuality:5]]
Set CVar [[r.Shadow.CSM.MaxCascades:10]]
Set CVar [[r.Shadow.MaxResolution:2048]]
Set CVar [[r.Shadow.MaxCSMResolution:2048]]
Set CVar [[r.Shadow.RadiusThreshold:0.01]]
Set CVar [[r.Shadow.DistanceScale:1.0]]
Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
Set CVar [[r.DistanceFieldShadowing:1]]
Set CVar [[r.VolumetricFog:1]]
Set CVar [[r.VolumetricFog.GridPixelSize:8]]
Set CVar [[r.VolumetricFog.GridSizeZ:128]]
Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
Set CVar [[r.LightMaxDrawDistanceScale:1]]
Set CVar [[r.CapsuleShadows:1]]
Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
Set CVar [[r.DistanceFieldAO:1]]
Set CVar [[r.SkylightIntensityMultiplier:1.0]]
Set CVar [[r.AOQuality:2]]
Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
Set CVar [[r.RayTracing.Scene.BuildMode:1]]
Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
Set CVar [[r.SSR.Quality:3]]
Set CVar [[r.SSR.HalfResSceneColor:0]]
Set CVar [[r.Lumen.Reflections.Allow:1]]
Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
Set CVar [[r.MotionBlurQuality:4]]
Set CVar [[r.MotionBlur.HalfResGather:0]]
Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
Set CVar [[r.AmbientOcclusionMaxQuality:100]]
Set CVar [[r.AmbientOcclusionLevels:-1]]
Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
Set CVar [[r.DepthOfFieldQuality:2]]
Set CVar [[r.RenderTargetPoolMin:400]]
Set CVar [[r.LensFlareQuality:2]]
Set CVar [[r.SceneColorFringeQuality:1]]
Set CVar [[r.EyeAdaptationQuality:2]]
Set CVar [[r.BloomQuality:5]]
Set CVar [[r.Bloom.ScreenPercentage:50.000]]
Set CVar [[r.FastBlurThreshold:100]]
Set CVar [[r.Upscale.Quality:3]]
Set CVar [[r.LightShaftQuality:1]]
Set CVar [[r.Filter.SizeScale:1]]
Set CVar [[r.Tonemapper.Quality:5]]
Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
Applying CVar settings from Section [TextureQuality@3] File [Scalability]
Set CVar [[r.Streaming.MipBias:0]]
Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
Set CVar [[r.Streaming.Boost:1]]
Set CVar [[r.MaxAnisotropy:8]]
Set CVar [[r.VT.MaxAnisotropy:8]]
Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
Set CVar [[r.Streaming.PoolSize:1000]]
Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
Set CVar [[r.TranslucencyLightingVolumeDim:64]]
Set CVar [[r.RefractionQuality:2]]
Set CVar [[r.SceneColorFormat:4]]
Set CVar [[r.DetailMode:3]]
Set CVar [[r.TranslucencyVolumeBlur:1]]
Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
Set CVar [[r.SSS.Scale:1]]
Set CVar [[r.SSS.SampleSet:2]]
Set CVar [[r.SSS.Quality:1]]
Set CVar [[r.SSS.HalfRes:0]]
Set CVar [[r.SSGI.Quality:3]]
Set CVar [[r.EmitterSpawnRateScale:1.0]]
Set CVar [[r.ParticleLightQuality:2]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
Set CVar [[fx.Niagara.QualityLevel:3]]
Set CVar [[r.Refraction.OffsetQuality:1]]
Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
Set CVar [[foliage.DensityScale:1.0]]
Set CVar [[grass.DensityScale:1.0]]
Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
Set CVar [[r.AnisotropicMaterials:1]]
Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
Using Default RHI: D3D12
Using Highest Feature Level of D3D12: SM6
Loading RHI module D3D12RHI
Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
Found D3D12 adapter 0: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 12fa10de, Revision: 00a1
  Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
  Adapter has 22230MB of dedicated video memory, 0MB of dedicated system memory, and 16349MB of shared system memory, 1 output[s], UMA:false
  Driver Version: 566.03 (internal:32.0.15.6603, unified:566.03)
     Driver Date: 10-15-2024
Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
  Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
  Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16349MB of shared system memory, 0 output[s], UMA:true
DirectX Agility SDK runtime found.
Chosen D3D12 Adapter Id = 0
RHI D3D12 with Feature Level SM6 is supported and will be used.
Selected Device Profile: [WindowsEditor]
Platform has ~ 32 GB [34288091136 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
Going up to parent DeviceProfile [Windows]
Going up to parent DeviceProfile []
Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
Set CVar [[r.DumpShaderDebugInfo:2]]
Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
Applying CVar settings from Section [ConsoleVariables] File [Engine]
Applying CVar settings from Section [ConsoleVariables] File [E:/Unreal Projects/HorizonBrigade/ueproject/Saved/Config/WindowsEditor/Editor.ini]
Computer: PINEAPPLE-PC-2
User: pineapple
CPU Page size=4096, Cores=6
High frequency timer resolution =10.000000 MHz
Process is running as part of a Windows Job with separate resource limits
Memory total: Physical=31.9GB (32GB approx) Virtual=75.9GB
Platform Memory Stats for WindowsEditor
Process Physical Memory: 718.45 MB used, 739.14 MB peak
Process Virtual Memory: 707.35 MB used, 707.35 MB peak
Physical Memory: 24867.64 MB used,  7832.03 MB free, 32699.67 MB total
Virtual Memory: 61131.01 MB used,  16624.66 MB free, 77755.67 MB total
Metadata set : extradevelopmentmemorymb="0"
WindowsPlatformFeatures enabled
Chaos Debug Draw Startup
Physics initialised using underlying interface: Chaos
Overriding language with editor language configuration option (en).
Using OS detected locale (zh-CN).
Setting process to per monitor DPI aware
Available input methods:
  - 中文(简体，中国) - 微软拼音 (TSF IME).
  - 英语(美国) - (Keyboard).
Activated input method: 中文(简体，中国) - 微软拼音 (TSF IME).
CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
Slate User Registered.  User Index 0, Is Virtual User: 0
Using Default RHI: D3D12
Using Highest Feature Level of D3D12: SM6
Loading RHI module D3D12RHI
Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
RHI D3D12 with Feature Level SM6 is supported and will be used.
Integrated GPU (iGPU): false
Creating D3D12 RHI with Max Feature Level SM6
Attached monitors:
    resolution: 2560x1080, work area: (0, 0) -> (2560, 1032), device: '\\.\DISPLAY1' [PRIMARY]
Found 1 attached monitors.
Gathering driver information using Windows Setup API
RHI Adapter Info:
            Name: NVIDIA GeForce RTX 2080 Ti
  Driver Version: 566.03 (internal:32.0.15.6603, unified:566.03)
     Driver Date: 10-15-2024
    GPU DeviceId: 0x1e07 (for the marketing name, search the web for "GPU Device Id")
InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
Aftermath initialized
Emitting draw events for PIX profiling.
Aftermath enabled. Active feature flags: 
 - Feature: EnableResourceTracking
ID3D12Device1 is supported.
ID3D12Device2 is supported.
ID3D12Device3 is supported.
ID3D12Device4 is supported.
ID3D12Device5 is supported.
ID3D12Device6 is supported.
ID3D12Device7 is supported.
ID3D12Device8 is supported.
ID3D12Device9 is supported.
ID3D12Device10 is supported.
ID3D12Device11 is supported.
ID3D12Device12 is supported.
Bindless resources are supported
Stencil ref from pixel shader is not supported
Raster order views are supported
Wave Operations are supported (wave size: min=32 max=32).
D3D12 ray tracing tier 1.1 and bindless resources are supported.
Mesh shader tier 1.0 is supported
AtomicInt64OnTypedResource is supported
AtomicInt64OnGroupShared is supported
AtomicInt64OnDescriptorHeapResource is supported
Shader Model 6.6 atomic64 is supported
[GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001BCA3F29AC0)
[GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001BCA3F29D80)
[GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001BCA3F2A040)
Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
NVIDIA Shader Execution Reordering NOT supported!
Batched command list execution is disabled for async queues due to known bugs in the current driver.
Texture pool is 13521 MB (70% of 19315 MB)
Async texture creation enabled
RHI has support for 64 bit atomics
Current RHI supports per-draw and screenspace Variable Rate Shading
Metadata set : verbatimrhiname="D3D12"
Metadata set : rhiname="D3D12"
Metadata set : rhifeaturelevel="SM6"
Metadata set : shaderplatform="PCD3D_SM6"
Initializing FReadOnlyCVARCache
Running Turnkey SDK detection: ' -ScriptsForProject="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyReport_0.log" -log="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyLog_0.log" -project="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject"  -platform=all'
Running Serialized UAT: [ cmd.exe /c ""D:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyReport_0.log" -log="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyLog_0.log" -project="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject"  -platform=all" ]
ASTCEnc version 5.0.1 library loaded
Loaded Base TextureFormat: TextureFormatASTC
Loaded Base TextureFormat: TextureFormatDXT
Loaded Base TextureFormat: TextureFormatETC2
Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
Loaded Base TextureFormat: TextureFormatUncompressed
Oodle Texture TFO init; latest sdk version = 2.9.13
Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
Loaded Base TextureFormat: TextureFormatOodle
Loaded TargetPlatform 'Android'
Loaded TargetPlatform 'Android_ASTC'
Loaded TargetPlatform 'Android_DXT'
Loaded TargetPlatform 'Android_ETC2'
Loaded TargetPlatform 'Android_OpenXR'
Loaded TargetPlatform 'AndroidClient'
Loaded TargetPlatform 'Android_ASTCClient'
Loaded TargetPlatform 'Android_DXTClient'
Loaded TargetPlatform 'Android_ETC2Client'
Loaded TargetPlatform 'Android_OpenXRClient'
Loaded TargetPlatform 'Android_Multi'
Loaded TargetPlatform 'Android_MultiClient'
Loaded TargetPlatform 'IOS'
Loaded TargetPlatform 'IOSClient'
Loaded TargetPlatform 'Linux'
Loaded TargetPlatform 'LinuxEditor'
Loaded TargetPlatform 'LinuxServer'
Loaded TargetPlatform 'LinuxClient'
Loaded TargetPlatform 'Mac'
Loaded TargetPlatform 'MacEditor'
Loaded TargetPlatform 'MacServer'
Loaded TargetPlatform 'MacClient'
Loaded TargetPlatform 'TVOS'
Loaded TargetPlatform 'TVOSClient'
Loaded TargetPlatform 'Windows'
Loaded TargetPlatform 'WindowsEditor'
Loaded TargetPlatform 'WindowsServer'
Loaded TargetPlatform 'WindowsClient'
Building Assets For WindowsEditor
Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
Loaded format module MetalShaderFormat
  SF_METAL_ES3_1_IOS
  SF_METAL_SM5_IOS
  SF_METAL_ES3_1_TVOS
  SF_METAL_SM5_TVOS
  SF_METAL_SM5
  SF_METAL_SM6
  SF_METAL_SIM
  SF_METAL_ES3_1
Loaded format module ShaderFormatD3D
  PCD3D_SM6
  PCD3D_SM5
  PCD3D_ES31
Loaded format module ShaderFormatOpenGL
  GLSL_150_ES31
  GLSL_ES3_1_ANDROID
Loaded format module ShaderFormatVectorVM
  VVM_1_0
Loaded format module VulkanShaderFormat
  SF_VULKAN_SM5
  SF_VULKAN_ES31_ANDROID
  SF_VULKAN_ES31
  SF_VULKAN_SM5_ANDROID
  SF_VULKAN_SM6
Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
Memory: Max Cache Size: -1 MB
FDerivedDataBackendGraph: Pak pak cache file E:/Unreal Projects/HorizonBrigade/ueproject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
Unable to find inner node Pak for hierarchy Hierarchy.
FDerivedDataBackendGraph: CompressedPak pak cache file E:/Unreal Projects/HorizonBrigade/ueproject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
Unable to find inner node CompressedPak for hierarchy Hierarchy.
../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
Unable to find inner node EnterprisePak for hierarchy Hierarchy.
Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
InTree version at 'D:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
No current process using the data dir found, launching a new instance
Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 30320  --child-id Zen_30320_Startup'
Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
Local ZenServer AutoLaunch initialization completed in 1.112 seconds
ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.02 seconds.
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.36ms. RandomReadSpeed=195.46MBs, RandomWriteSpeed=129.53MBs. Assigned SpeedClass 'Local'
Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
ZenShared: Disabled because Host is set to 'None'
Unable to find inner node ZenShared for hierarchy Hierarchy.
Shared: Disabled because no path is configured.
Unable to find inner node Shared for hierarchy Hierarchy.
Cloud: Disabled because Host is set to 'None'
Unable to find inner node Cloud for hierarchy Hierarchy.
Guid format shader working directory is 6 characters bigger than the processId version (E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/Shaders/WorkingDirectory/30320/).
Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/742D905642E1F51B2009A3BCF7F58764/'.
Cannot use XGE Controller as Incredibuild is not installed on this machine.
UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
Using 3 local workers for shader compilation
Compiling shader autogen file: E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
Autogen file is unchanged, skipping write.
Using FreeType 2.10.0
SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
FAssetRegistry took 0.0247 seconds to start up
EditorDomain is Disabled
Deviceprofile LinuxArm64Editor not found.
Deviceprofile LinuxArm64 not found.
Active device profile: [000001BCD9C87300][000001BCC6065000 66] WindowsEditor
Metadata set : deviceprofile="WindowsEditor"
FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/WhiteSquareTexture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/WhiteSquareTexture'.
AssetDataGatherer spent 0.006s loading caches E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/CachedAssetRegistry_*.bin.
Texture Encode Speed: FinalIfAvailable (editor).
Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
Shared linear texture encoding: Disabled
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/GradientTexture0, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/GradientTexture0'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/Black, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/Black'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Actor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Actor'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_PortalActorIcon2, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_PortalActorIcon2'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ReflActorIcon, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ReflActorIcon'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineFonts/RobotoDistanceField, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineFonts/RobotoDistanceField'.
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_M, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_M'.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_N, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_N'.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/DefaultDiffuse, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/DefaultDiffuse'.
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TextRenderActorIcon, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TextRenderActorIcon'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/MatineeCam_D, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/MatineeCam_D'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/MatineeCam_SM, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/MatineeCam_SM'.
Using QuadricMeshReduction for automatic static mesh reduction
Using SkeletalMeshReduction for automatic skeletal mesh reduction
Using ProxyLODMeshReduction for automatic mesh merging
No distributed automatic mesh merging module available
No distributed automatic mesh merging module available
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_CameraShakeSource, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_CameraShakeSource'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_DecalActorIcon, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_DecalActorIcon'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/T_EditorHelp, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/T_EditorHelp'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/flipbook, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/flipbook'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/DebugNumberStrip, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/DebugNumberStrip'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/DebugNumberPeriod, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/DebugNumberPeriod'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTexture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTexture'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/MatInstActSprite, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/MatInstActSprite'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_NavP, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_NavP'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/Bad, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/Bad'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Note, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Note'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Emitter, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Emitter'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TargetPoint, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TargetPoint'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Trigger, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Trigger'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VectorFieldVol, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VectorFieldVol'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_BoxReflectionCapture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_BoxReflectionCapture'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectional, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectional'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectionalMove, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectionalMove'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ExpoHeightFog, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ExpoHeightFog'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_SkyAtmosphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_SkyAtmosphere'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPoint, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPoint'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPointMove, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPointMove'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_RadForce, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_RadForce'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/SkyLight, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/SkyLight'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerBox, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerBox'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerCapsule, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerCapsule'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerSphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerSphere'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_AtmosphericHeightFog, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_AtmosphericHeightFog'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VolumetricCloud, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VolumetricCloud'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMeshes/Sphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMeshes/Sphere'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Player, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Player'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightRect, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightRect'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpot, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpot'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpotMove, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpotMove'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_WindDirectional, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_WindDirectional'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTextureCube, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTextureCube'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture2D, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture2D'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCube, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCube'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSphere'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCylinder, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCylinder'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorPlane, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorPlane'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSkySphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSkySphere'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/MapTemplates/Sky/DaylightAmbientCubemap, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/MapTemplates/Sky/DaylightAmbientCubemap'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/SM_Sequencer_Node, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/SM_Sequencer_Node'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/AI/S_NavLink, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/AI/S_NavLink'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Solver, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Solver'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineDebugMaterials/VolumeToRender, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineDebugMaterials/VolumeToRender'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Base, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Base'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Arm, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Arm'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Mount, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Mount'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Body, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Body'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Track, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Track'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Mount, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Mount'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_LevelSequence, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_LevelSequence'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Terrain, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Terrain'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorLandscapeResources/SplineEditorMesh, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorLandscapeResources/SplineEditorMesh'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairColorMap, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairColorMap'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairDebugColor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairDebugColor'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/BasicShapes/Cube, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/BasicShapes/Cube'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterBodyIslandSprite, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterBodyIslandSprite'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterZoneActorSprite, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterZoneActorSprite'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/SnapGrid/SnapGridPlaneMesh, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/SnapGrid/SnapGridPlaneMesh'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/LaserPointer/VR_LaserPower_01, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/LaserPointer/VR_LaserPower_01'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/PlaneTranslationHandle, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/PlaneTranslationHandle'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleFull, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleFull'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleIndicator, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleIndicator'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleQuarter, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleQuarter'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/StartRotationHandleIndicator, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/StartRotationHandleIndicator'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TransformGizmoFreeRotation, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TransformGizmoFreeRotation'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TranslateArrowHandle, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TranslateArrowHandle'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/UniformScaleHandle, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/UniformScaleHandle'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxCorner, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxCorner'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxEdge, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxEdge'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CineCam, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CineCam'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/ArtTools/RenderToTexture/Meshes/S_1_Unit_Plane, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/ArtTools/RenderToTexture/Meshes/S_1_Unit_Plane'.
Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_NoImage, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_NoImage'.
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_OOD, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_OOD'.
VirtualizationSystem name found in ini file: None
FNullVirtualizationSystem mounted, virtualization will be disabled
FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
Starting LiveCoding
LiveCodingConsole Arguments: UnrealEditor Win64 Development
First instance in process group "UE_HorizonBrigade_0x892c1eec", spawning console
Border
BreadcrumbButton
Brushes.Title
ColorPicker.ColorThemes
Default
Icons.Save
Icons.Toolbar.Settings
ListView
SoftwareCursor_CardinalCross
SoftwareCursor_Grab
TableView.DarkRow
TableView.Row
TreeView
Waiting for server
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_FTest, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_FTest'.
FWorldPartitionClassDescRegistry::Initialize started...
FWorldPartitionClassDescRegistry::Initialize took 25.780 ms
XR: Instanced Stereo Rendering is Disabled
XR: MultiViewport is Disabled
XR: Mobile Multiview is Disabled
UGameplayTagsManager::InitializeManager -  0.002 s
FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/MiniFont, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/MiniFont'.
Niagara Debugger Client Initialized | Session: 0B5D8C08709F42D08000000000001200 | Instance: 4FDF0B6F4DB0F1A15C9AEDA5AD8137B1 (PINEAPPLE-PC-2-30320).
Initializing TcpMessaging bridge
Work queue size set to 1024.
Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
Unicast socket bound to '0.0.0.0:60695'.
Added local interface '192.168.2.101' to multicast group '230.0.0.1:6666'
Added local interface '192.168.195.212' to multicast group '230.0.0.1:6666'
Added local interface '172.30.112.1' to multicast group '230.0.0.1:6666'
Using asynchronous task graph for message deserialization.
MetaSound Page Target Initialized to 'Default'
Registering Engine Module Parameter Interfaces...
MetaSound Engine Initialized
Successfully initialized, removing startup thread
Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
Available graphics and compute adapters:
No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
Available graphics and compute adapters:
0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
1: Microsoft Basic Render Driver (Compute, Graphics)
No NPU adapter found!
MakeRuntimeORTDml:
  DirectML:  yes
  RHI D3D12: yes
  D3D12:     yes
  NPU:       no
Interface availability:
  GPU: yes
  RDG: yes
  NPU: no
Available graphics and compute adapters:
No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
Available graphics and compute adapters:
0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
1: Microsoft Basic Render Driver (Compute, Graphics)
No NPU adapter found!
[FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
Initialize
OnSessionChanged
Initialize
OnSessionChanged
Initialize
OnSessionChanged
Initialize
OnSessionChanged
Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
Completed SDK detection: ExitCode = 0
UAndroidPermissionCallbackProxy::GetInstance
No Audio Capture implementations found. Audio input will be silent.
No Audio Capture implementations found. Audio input will be silent.
Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
CreateProc failed: 系统找不到指定的文件。 (0x00000002)
URL: C:/Users/<USER>/AppData/Local/GitHubDesktop/app-3.4.19/resources/app/git/cmd/git.exe version
Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
CreateProc failed: 系统找不到指定的文件。 (0x00000002)
URL: C:/Users/<USER>/AppData/Local/GitHubDesktop/app-3.4.19/resources/app/git/cmd/git.exe version
Loaded 0 collections in 0.002855 seconds
Scanning file cache for directory 'E:/Unreal Projects/HorizonBrigade/ueproject/Saved/Collections/' took 0.03s
Scanning file cache for directory 'E:/Unreal Projects/HorizonBrigade/ueproject/Content/Developers/pineapple/Collections/' took 0.09s
Scanning file cache for directory 'E:/Unreal Projects/HorizonBrigade/ueproject/Content/Collections/' took 0.00s
FPlatformStackWalk::StackWalkAndDump -  1.090 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
Requested Gameplay Tag SpatialFusion.Strategy.Base was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[Callstack] 0x000001bd380d1d8f UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyBase::UPropagationStrategyBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:16]
[Callstack] 0x000001bd380d19ef UnrealEditor-SpatialFusionRuntime.dll!UDiffusionPropagationStrategy::UDiffusionPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:57]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d2f5c UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:257]
[Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
                   SendNewReport - 18.433 s
            FDebug::EnsureFailed - 22.859 s
FPlatformStackWalk::StackWalkAndDump -  0.027 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
Requested Gameplay Tag SpatialFusion.Strategy.Diffusion was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[Callstack] 0x000001bd380d1a5a UnrealEditor-SpatialFusionRuntime.dll!UDiffusionPropagationStrategy::UDiffusionPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:59]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d2f5c UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:257]
[Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
                   SendNewReport - 33.839 s
            FDebug::EnsureFailed - 33.872 s
FPlatformStackWalk::StackWalkAndDump -  0.021 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
Requested Gameplay Tag SpatialFusion.Strategy.Directional was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[Callstack] 0x000001bd380d1bfa UnrealEditor-SpatialFusionRuntime.dll!UDirectionalPropagationStrategy::UDirectionalPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:91]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d308d UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:262]
[Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.001 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
                   SendNewReport - 49.603 s
            FDebug::EnsureFailed - 49.625 s
FPlatformStackWalk::StackWalkAndDump -  0.022 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
Requested Gameplay Tag SpatialFusion.Strategy.Radial was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[Callstack] 0x000001bd380d1f4a UnrealEditor-SpatialFusionRuntime.dll!URadialPropagationStrategy::URadialPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:123]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d31b9 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:267]
[Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
