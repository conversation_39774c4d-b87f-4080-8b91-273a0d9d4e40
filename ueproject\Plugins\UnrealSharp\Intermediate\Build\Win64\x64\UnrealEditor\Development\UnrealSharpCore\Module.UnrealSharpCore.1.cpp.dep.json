{"Version": "1.2", "Data": {"Source": "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpcore\\module.unrealsharpcore.1.cpp", "ProvidedModule": "", "PCH": "e:\\unreal projects\\horizonbrigade\\ueproject\\intermediate\\build\\win64\\x64\\horizonbrigadeeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealsharpcore\\definitions.unrealsharpcore.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\asyncexporter.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\export\\asyncexporter.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csbindsmanager.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\csexportedfunction.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedgchandle.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\csmanagedcallbackscache.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csmanagedgchandle.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\asyncexporter.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\asyncexporter.unrealsharp.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpbinds\\public\\unrealsharpbinds.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csactorcomponentextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csactorcomponentextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csactorcomponentextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csactorextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csactorextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csactorextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csarraypropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csarraypropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cspropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cspropertymetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csmembermetadata.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\csunrealtype.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\register\\metadata\\cspropertytype.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cspropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csarraypropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csblueprint.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csblueprint.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csblueprint.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csblueprintasyncactionbase.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\blueprintactions\\csblueprintasyncactionbase.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\blueprintasyncactionbase.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\blueprintasyncactionbase.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csblueprintasyncactionbase.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cscancellableasyncaction.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\blueprintactions\\cscancellableasyncaction.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\cancellableasyncaction.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cancellableasyncaction.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cscancellableasyncaction.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csclass.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csclass.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\utils\\csmacros.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csclass.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csclasspropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csclasspropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\cscommonpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cscommonpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csclasspropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\cscommonpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdatatableextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csdatatableextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdatatableextensions.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdefaultcomponentpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csdefaultcomponentpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdefaultcomponentpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdelegatebasepropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csdelegatebasepropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdelegatebasepropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdelegatepropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csdelegatepropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdelegatepropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdevelopersettings.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\developersettings\\csdevelopersettings.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csdevelopersettings.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenginesubsystem.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\subsystems\\csenginesubsystem.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenginesubsystem.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenum.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\csenum.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\managedreferencescollection.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\managedreferencescollection.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\userdefinedenum.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\userdefinedenum.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenum.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenumpropertygenerator.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\factories\\propertygenerators\\csenumpropertygenerator.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csenumpropertygenerator.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csfunction.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\functions\\csfunction.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csfunction.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csfunction_params.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\typegenerator\\functions\\csfunction_params.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csfunction_params.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csgameinstancesubsystem.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\subsystems\\csgameinstancesubsystem.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csgameinstancesubsystem.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csgameplaytagcontainerextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csgameplaytagcontainerextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csgameplaytagcontainerextensions.generated.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "d:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "d:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csgameplaytagextensions.gen.cpp", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\source\\unrealsharpcore\\extensions\\libraries\\csgameplaytagextensions.h", "e:\\unreal projects\\horizonbrigade\\ueproject\\plugins\\unrealsharp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealsharpcore\\uht\\csgameplaytagextensions.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}