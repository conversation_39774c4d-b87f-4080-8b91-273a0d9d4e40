#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\FWeakObjectPtrExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UFWeakObjectPtrExporter
{
    static const FCSExportedFunction UnrealSharpBind_SetObject;
    static const FCSExportedFunction UnrealSharpBind_GetObject;
    static const FCSExportedFunction UnrealSharpBind_IsValid;
    static const FCSExportedFunction UnrealSharpBind_IsStale;
    static const FCSExportedFunction UnrealSharpBind_NativeEquals;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFWeakObjectPtrExporter::UnrealSharpBind_SetObject = FCSExportedFunction("FWeakObjectPtrExporter", "SetObject", (void*)&UFWeakObjectPtrExporter::SetObject, GetFunctionSize(UFWeakObjectPtrExporter::SetObject));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFWeakObjectPtrExporter::UnrealSharpBind_GetObject = FCSExportedFunction("FWeakObjectPtrExporter", "GetObject", (void*)&UFWeakObjectPtrExporter::GetObject, GetFunctionSize(UFWeakObjectPtrExporter::GetObject));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFWeakObjectPtrExporter::UnrealSharpBind_IsValid = FCSExportedFunction("FWeakObjectPtrExporter", "IsValid", (void*)&UFWeakObjectPtrExporter::IsValid, GetFunctionSize(UFWeakObjectPtrExporter::IsValid));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFWeakObjectPtrExporter::UnrealSharpBind_IsStale = FCSExportedFunction("FWeakObjectPtrExporter", "IsStale", (void*)&UFWeakObjectPtrExporter::IsStale, GetFunctionSize(UFWeakObjectPtrExporter::IsStale));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFWeakObjectPtrExporter::UnrealSharpBind_NativeEquals = FCSExportedFunction("FWeakObjectPtrExporter", "NativeEquals", (void*)&UFWeakObjectPtrExporter::NativeEquals, GetFunctionSize(UFWeakObjectPtrExporter::NativeEquals));

