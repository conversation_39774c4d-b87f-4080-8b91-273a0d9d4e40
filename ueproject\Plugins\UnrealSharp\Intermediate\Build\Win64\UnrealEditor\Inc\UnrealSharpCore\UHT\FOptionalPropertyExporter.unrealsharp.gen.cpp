#include "UnrealSharpBinds.h"
#include "E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Source\UnrealSharpCore\Export\FOptionalPropertyExporter.h"
struct Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter
{
    static const FCSExportedFunction UnrealSharpBind_IsSet;
    static const FCSExportedFunction UnrealSharpBind_MarkSetAndGetInitializedValuePointerToReplace;
    static const FCSExportedFunction UnrealSharpBind_MarkUnset;
    static const FCSExportedFunction UnrealSharpBind_GetValuePointerForRead;
    static const FCSExportedFunction UnrealSharpBind_GetValuePointerForReplace;
    static const FCSExportedFunction UnrealSharpBind_GetValuePointerForReadIfSet;
    static const FCSExportedFunction UnrealSharpBind_GetValuePointerForReplaceIfSet;
    static const FCSExportedFunction UnrealSharpBind_GetValuePointerForReadOrReplace;
    static const FCSExportedFunction UnrealSharpBind_GetValuePointerForReadOrReplaceIfSet;
    static const FCSExportedFunction UnrealSharpBind_CalcSize;
};
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_IsSet = FCSExportedFunction("FOptionalPropertyExporter", "IsSet", (void*)&UFOptionalPropertyExporter::IsSet, GetFunctionSize(UFOptionalPropertyExporter::IsSet));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_MarkSetAndGetInitializedValuePointerToReplace = FCSExportedFunction("FOptionalPropertyExporter", "MarkSetAndGetInitializedValuePointerToReplace", (void*)&UFOptionalPropertyExporter::MarkSetAndGetInitializedValuePointerToReplace, GetFunctionSize(UFOptionalPropertyExporter::MarkSetAndGetInitializedValuePointerToReplace));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_MarkUnset = FCSExportedFunction("FOptionalPropertyExporter", "MarkUnset", (void*)&UFOptionalPropertyExporter::MarkUnset, GetFunctionSize(UFOptionalPropertyExporter::MarkUnset));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_GetValuePointerForRead = FCSExportedFunction("FOptionalPropertyExporter", "GetValuePointerForRead", (void*)&UFOptionalPropertyExporter::GetValuePointerForRead, GetFunctionSize(UFOptionalPropertyExporter::GetValuePointerForRead));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_GetValuePointerForReplace = FCSExportedFunction("FOptionalPropertyExporter", "GetValuePointerForReplace", (void*)&UFOptionalPropertyExporter::GetValuePointerForReplace, GetFunctionSize(UFOptionalPropertyExporter::GetValuePointerForReplace));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_GetValuePointerForReadIfSet = FCSExportedFunction("FOptionalPropertyExporter", "GetValuePointerForReadIfSet", (void*)&UFOptionalPropertyExporter::GetValuePointerForReadIfSet, GetFunctionSize(UFOptionalPropertyExporter::GetValuePointerForReadIfSet));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_GetValuePointerForReplaceIfSet = FCSExportedFunction("FOptionalPropertyExporter", "GetValuePointerForReplaceIfSet", (void*)&UFOptionalPropertyExporter::GetValuePointerForReplaceIfSet, GetFunctionSize(UFOptionalPropertyExporter::GetValuePointerForReplaceIfSet));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_GetValuePointerForReadOrReplace = FCSExportedFunction("FOptionalPropertyExporter", "GetValuePointerForReadOrReplace", (void*)&UFOptionalPropertyExporter::GetValuePointerForReadOrReplace, GetFunctionSize(UFOptionalPropertyExporter::GetValuePointerForReadOrReplace));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_GetValuePointerForReadOrReplaceIfSet = FCSExportedFunction("FOptionalPropertyExporter", "GetValuePointerForReadOrReplaceIfSet", (void*)&UFOptionalPropertyExporter::GetValuePointerForReadOrReplaceIfSet, GetFunctionSize(UFOptionalPropertyExporter::GetValuePointerForReadOrReplaceIfSet));
const FCSExportedFunction Z_Construct_UClass_UnrealSharp_Binds_UFOptionalPropertyExporter::UnrealSharpBind_CalcSize = FCSExportedFunction("FOptionalPropertyExporter", "CalcSize", (void*)&UFOptionalPropertyExporter::CalcSize, GetFunctionSize(UFOptionalPropertyExporter::CalcSize));

