﻿Log file open, 07/18/25 21:56:35
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=39132)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: HorizonBrigade
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (22H2) [10.0.22621.4317] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|Intel(R) Core(TM) i5-9400F CPU @ 2.90GHz"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" "E:\Unreal Projects\HorizonBrigade\ueproject\HorizonBrigade.uproject"""
LogCsvProfiler: Display: Metadata set : loginid="30312e0b49ed30022bc720a613935ce6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 5.472507
LogPluginManager: Display: By default, prioritizing project plugin (E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealMC5a95177e2afcV1/UnrealMCPython.uplugin) over the corresponding engine version (../../../Engine/Plugins/Marketplace/UnrealMC5a95177e2afcV1/UnrealMCPython.uplugin).
LogPluginManager: Display: By default, prioritizing project plugin (E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealSharp/UnrealSharp.uplugin) over the corresponding engine version (../../../Engine/Plugins/Marketplace/UnrealSharp/UnrealSharp.uplugin).
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-408029044F699DC9896307B6EA70F7DA
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [E:/Unreal Projects/HorizonBrigade/ueproject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: E:/Unreal Projects/HorizonBrigade/ueproject/Binaries/Win64/HorizonBrigadeEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Linux ini files took 0.78 seconds
LogPluginManager: Found matching target receipt: E:/Unreal Projects/HorizonBrigade/ueproject/Binaries/Win64/HorizonBrigadeEditor.target
LogAssetRegistry: Display: Asset registry cache read as 125.3 MiB from E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading Mac ini files took 0.21 seconds
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin MassAI
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin BlueprintMaterialTextureNodes
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Project plugin UnrealMCPython
LogPluginManager: Mounting Project plugin UnrealSharp
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin DatasmithContent
LogConfig: Display: Loading TVOS ini files took 0.18 seconds
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogConfig: Display: Loading Unix ini files took 0.08 seconds
LogPluginManager: Mounting Engine plugin PCGExternalDataInterop
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogConfig: Display: Loading VulkanPC ini files took 0.05 seconds
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GameplayStateTree
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogConfig: Display: Loading Windows ini files took 0.10 seconds
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin InstancedActors
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MassGameplay
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin NavCorridor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SmartObjects
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WorldConditions
LogPluginManager: Mounting Engine plugin ZoneGraph
LogPluginManager: Mounting Engine plugin ZoneGraphAnnotations
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogConfig: Display: Loading VisionOS ini files took 0.22 seconds
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin Buoyancy
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin DaySequence
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin TargetingSystem
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin Landmass
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PCGBiomeCore
LogPluginManager: Mounting Engine plugin PCGBiomeSample
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Water
LogPluginManager: Mounting Engine plugin WaterAdvanced
LogPluginManager: Mounting Engine plugin WaterExtras
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin PCGInstancedActorsInterop
LogPluginManager: Mounting Engine plugin PCGNiagaraInterop
LogPluginManager: Mounting Engine plugin PCGWaterInterop
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Project plugin LyraGame
LogPluginManager: Mounting Project plugin RGOAP
LogPluginManager: Mounting Project plugin SpatialFusion
LogPluginManager: Mounting Project plugin GenerativeAISupport
LogConfig: Display: Loading IOS ini files took 2.15 seconds
LogConfig: Display: Loading Android ini files took 2.19 seconds
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSShared: Loaded "D:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=30312e0b49ed30022bc720a613935ce6
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (22H2) [10.0.22621.4317] (), CPU: Intel(R) Core(TM) i5-9400F CPU @ 2.90GHz, GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: D:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.07.18-13.56.46:875][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.ShadingPath:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.AllowDeferredShadingOpenGL:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.SupportGPUScene:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.AntiAliasing:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.FloatPrecisionMode:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.AllowDitheredLODTransition:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.Mobile.VirtualTextures:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.DiscardUnusedQuality:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.AllowOcclusionQueries:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MinScreenRadiusForLights:0.030000]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MinScreenRadiusForDepthPrepass:0.030000]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.PrecomputedVisibilityWarning:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.TextureStreaming:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[Compat.UseDXT5NormalMaps:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.VT.TileSize:128]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.VT.AnisotropicFiltering:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.VT.EnableAutoImport:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[bEnableVirtualTextureOpacityMask:0]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.Support:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.StaticMesh.DefaultMeshPaintTextureSupport:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:4]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.EnableBaseColor:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.EnableBaseColorRoughness:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.EnableBaseColorSpecular:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.EnableMask4:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.EnableWorldHeight:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.EnableDisplacement:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[WorkingColorSpaceChoice:sRGB]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[RedChromaticityCoordinate:(X=0.640000,Y=0.330000)]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[GreenChromaticityCoordinate:(X=0.300000,Y=0.600000)]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[BlueChromaticityCoordinate:(X=0.150000,Y=0.060000)]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: CVar [[WhiteChromaticityCoordinate:(X=0.312700,Y=0.329000)]] deferred - dummy variable created
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.LegacyLuminanceFactors:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.ClearCoatNormal:0]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:128]]
[2025.07.18-13.56.46:876][  0]LogConfig: Set CVar [[r.ReflectionEnvironmentLightmapMixBasedOnRoughness:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.RayTracing:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.PathTracing:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DistanceFields.DefaultVoxelDensity:0.200000]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Nanite.ProjectEnabled:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.ForwardShading:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.VertexFoggingForOpaque:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.SeparateTranslucency:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.TranslucentSortPolicy:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: CVar [[TranslucentSortAxis:(X=0.000000,Y=-1.000000,Z=0.000000)]] deferred - dummy variable created
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.LocalFogVolume.ApplyOnTranslucent:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[xr.VRS.FoveationLevel:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[xr.VRS.DynamicFoveation:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.CustomDepth:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.PostProcessing.PropagateAlpha:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Deferred.SupportPrimitiveAlphaHoldout:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.Bloom:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.AmbientOcclusion:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.AmbientOcclusionStaticFraction:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:1.000000]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.800000]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.800000]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.MotionBlur:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.LensFlare:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.TemporalAA.Upsampling:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.MSAACount:4]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultFeature.LightUnits:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.DefaultBackBufferPixelFormat:4]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:100.000000]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Mobile.Mode:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.VR.Mode:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.PathTracer.Mode:0]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.07.18-13.56.46:877][  0]LogConfig: Set CVar [[r.StencilForLODDither:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.EarlyZPass:3]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.EarlyZPassOnlyMaterialMasking:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.DBuffer:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.ClearSceneMethod:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.VelocityOutputPass:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Velocity.EnableVertexDeformation:2]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SelectiveBasePassOutputs:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[bDefaultParticleCutouts:0]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[fx.GPUSimulationTextureSizeX:1024]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[fx.GPUSimulationTextureSizeY:1024]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.GBufferFormat:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.MorphTarget.Mode:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.MorphTarget.MaxBlendWeight:5.000000]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportSkyAtmosphere:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportSkyAtmosphereAffectsHeightFog:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportExpFogMatchesVolumetricFog:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportLocalFogVolumes:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportCloudShadowOnForwardLitTranslucent:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.LightFunctionAtlas.Format:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.VolumetricFog.LightFunction:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Deferred.UsesLightFunctionAtlas:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SingleLayerWater.UsesLightFunctionAtlas:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Translucent.UsesLightFunctionAtlas:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Translucent.UsesIESProfiles:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Translucent.UsesRectLights:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Translucent.UsesShadowedLocalLights:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[vr.InstancedStereo:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.MobileHDR:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[vr.MobileMultiView:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.UseHWsRGBEncoding:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[vr.RoundRobinOcclusion:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.MeshStreaming:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Translucency.HeterogeneousVolumes:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.WireframeCullThreshold:5.000000]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportStationarySkylight:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportLowQualityLightmaps:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportPointLightWholeSceneShadows:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Shadow.TranslucentPerObject.ProjectEnabled:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Water.SingleLayerWater.SupportCloudShadow:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Substrate:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Substrate.OpaqueMaterialRoughRefraction:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Refraction.Blur:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Substrate.Debug.AdvancedVisualizationShaders:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Substrate.EnableLayerSupport:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Material.RoughDiffuse:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Material.EnergyConservation:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Material.DefaultAutoMaterialUsage:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.OIT.SortedPixels:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.HairStrands.LODMode:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.VRS.Support:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SkinCache.SkipCompilingGPUSkinVF:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SkinCache.DefaultBehavior:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SkinCache.SceneMemoryLimitInMB:128.000000]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.EnableStaticAndCSMShadowReceivers:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.EnableMovableLightCSMShaderCulling:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.Forward.EnableLocalLights:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.Forward.EnableClusteredReflections:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.AllowDistanceFieldShadows:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.EnableMovableSpotlightsShadow:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.GPUSkin.Limit2BoneInfluences:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportDepthOnlyIndexBuffers:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.SupportReversedIndexBuffers:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.AmbientOcclusion:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.DBuffer:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.GPUSkin.AlwaysUseDeformerForUnlimitedBoneInfluences:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluencesThreshold:8]]
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[DefaultBoneInfluenceLimit:(Default=0,PerPlatform=())]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.ScreenSpaceReflections:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[r.Mobile.SupportsGen4TAA:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[bStreamSkeletalMeshLODs:(Default=False,PerPlatform=())]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[bDiscardSkeletalMeshOptionalLODs:(Default=False,PerPlatform=())]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[VisualizeCalibrationCustomMaterialPath:None]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.07.18-13.56.46:878][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.07.18-13.56.46:878][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.07.18-13.56.46:878][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.07.18-13.56.46:878][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.07.18-13.56.46:879][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.07.18-13.56.46:883][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="2560"
[2025.07.18-13.56.46:883][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.07.18-13.56.46:886][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.07.18-13.56.46:886][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.07.18-13.56.46:887][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.07.18-13.56.46:887][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.07.18-13.56.46:887][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.07.18-13.56.46:896][  0]LogRHI: Using Default RHI: D3D12
[2025.07.18-13.56.46:897][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.07.18-13.56.46:897][  0]LogRHI: Loading RHI module D3D12RHI
[2025.07.18-13.56.46:949][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.07.18-13.56.46:949][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.07.18-13.56.47:602][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 12fa10de, Revision: 00a1
[2025.07.18-13.56.47:602][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.07.18-13.56.47:602][  0]LogD3D12RHI:   Adapter has 22230MB of dedicated video memory, 0MB of dedicated system memory, and 16349MB of shared system memory, 1 output[s], UMA:false
[2025.07.18-13.56.47:602][  0]LogD3D12RHI:   Driver Version: 566.03 (internal:32.0.15.6603, unified:566.03)
[2025.07.18-13.56.47:602][  0]LogD3D12RHI:      Driver Date: 10-15-2024
[2025.07.18-13.56.47:630][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.07.18-13.56.47:630][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.07.18-13.56.47:630][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16349MB of shared system memory, 0 output[s], UMA:true
[2025.07.18-13.56.47:630][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.07.18-13.56.47:630][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.07.18-13.56.47:630][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.07.18-13.56.47:631][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.07.18-13.56.47:631][  0]LogHAL: Display: Platform has ~ 32 GB [34288091136 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.07.18-13.56.47:633][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.07.18-13.56.47:634][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.07.18-13.56.47:635][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.07.18-13.56.47:635][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.07.18-13.56.47:635][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.18-13.56.47:644][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.07.18-13.56.47:644][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.07.18-13.56.47:644][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.07.18-13.56.47:644][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.07.18-13.56.47:644][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.07.18-13.56.47:644][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.18-13.56.47:644][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.07.18-13.56.47:644][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [E:/Unreal Projects/HorizonBrigade/ueproject/Saved/Config/WindowsEditor/Editor.ini]
[2025.07.18-13.56.47:645][  0]LogInit: Computer: PINEAPPLE-PC-2
[2025.07.18-13.56.47:645][  0]LogInit: User: pineapple
[2025.07.18-13.56.47:645][  0]LogInit: CPU Page size=4096, Cores=6
[2025.07.18-13.56.47:645][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.07.18-13.56.47:645][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.07.18-13.56.47:645][  0]LogMemory: Memory total: Physical=31.9GB (32GB approx) Virtual=75.9GB
[2025.07.18-13.56.47:645][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.07.18-13.56.47:645][  0]LogMemory: Process Physical Memory: 718.45 MB used, 739.14 MB peak
[2025.07.18-13.56.47:645][  0]LogMemory: Process Virtual Memory: 707.35 MB used, 707.35 MB peak
[2025.07.18-13.56.47:645][  0]LogMemory: Physical Memory: 24867.64 MB used,  7832.03 MB free, 32699.67 MB total
[2025.07.18-13.56.47:645][  0]LogMemory: Virtual Memory: 61131.01 MB used,  16624.66 MB free, 77755.67 MB total
[2025.07.18-13.56.47:645][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.07.18-13.56.47:656][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.07.18-13.56.47:785][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.07.18-13.56.47:786][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.07.18-13.56.47:788][  0]LogInit: Overriding language with editor language configuration option (en).
[2025.07.18-13.56.47:788][  0]LogInit: Using OS detected locale (zh-CN).
[2025.07.18-13.56.47:802][  0]LogInit: Setting process to per monitor DPI aware
[2025.07.18-13.56.49:202][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.07.18-13.56.49:202][  0]LogWindowsTextInputMethodSystem:   - 中文(简体，中国) - 微软拼音 (TSF IME).
[2025.07.18-13.56.49:202][  0]LogWindowsTextInputMethodSystem:   - 英语(美国) - (Keyboard).
[2025.07.18-13.56.49:202][  0]LogWindowsTextInputMethodSystem: Activated input method: 中文(简体，中国) - 微软拼音 (TSF IME).
[2025.07.18-13.56.49:208][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.07.18-13.56.49:289][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.07.18-13.56.49:338][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.07.18-13.56.49:338][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.07.18-13.56.54:710][  0]LogRHI: Using Default RHI: D3D12
[2025.07.18-13.56.54:710][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.07.18-13.56.54:710][  0]LogRHI: Loading RHI module D3D12RHI
[2025.07.18-13.56.54:710][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.07.18-13.56.54:710][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.07.18-13.56.54:710][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.07.18-13.56.54:710][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.07.18-13.56.54:710][  0]LogWindows: Attached monitors:
[2025.07.18-13.56.54:710][  0]LogWindows:     resolution: 2560x1080, work area: (0, 0) -> (2560, 1032), device: '\\.\DISPLAY1' [PRIMARY]
[2025.07.18-13.56.54:710][  0]LogWindows: Found 1 attached monitors.
[2025.07.18-13.56.54:710][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.07.18-13.56.54:710][  0]LogRHI: RHI Adapter Info:
[2025.07.18-13.56.54:710][  0]LogRHI:             Name: NVIDIA GeForce RTX 2080 Ti
[2025.07.18-13.56.54:710][  0]LogRHI:   Driver Version: 566.03 (internal:32.0.15.6603, unified:566.03)
[2025.07.18-13.56.54:710][  0]LogRHI:      Driver Date: 10-15-2024
[2025.07.18-13.56.54:710][  0]LogD3D12RHI:     GPU DeviceId: 0x1e07 (for the marketing name, search the web for "GPU Device Id")
[2025.07.18-13.56.54:710][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.07.18-13.56.55:273][  0]LogNvidiaAftermath: Aftermath initialized
[2025.07.18-13.56.55:274][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.07.18-13.56.55:367][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.07.18-13.56.55:367][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: Bindless resources are supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: Raster order views are supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.07.18-13.56.55:367][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.07.18-13.56.55:466][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001BCA3F29AC0)
[2025.07.18-13.56.55:466][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001BCA3F29D80)
[2025.07.18-13.56.55:466][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001BCA3F2A040)
[2025.07.18-13.56.55:466][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.07.18-13.56.55:466][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.07.18-13.56.56:280][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.07.18-13.56.56:281][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.07.18-13.56.56:281][  0]LogRHI: Texture pool is 13521 MB (70% of 19315 MB)
[2025.07.18-13.56.56:281][  0]LogD3D12RHI: Async texture creation enabled
[2025.07.18-13.56.56:281][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.07.18-13.56.56:393][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.07.18-13.56.56:412][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.07.18-13.56.56:412][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.07.18-13.56.56:412][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM6"
[2025.07.18-13.56.56:412][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM6"
[2025.07.18-13.56.56:412][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.07.18-13.56.56:546][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyReport_0.log" -log="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyLog_0.log" -project="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject"  -platform=all'
[2025.07.18-13.56.56:547][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyReport_0.log" -log="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyLog_0.log" -project="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject"  -platform=all" ]
[2025.07.18-13.56.56:896][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.07.18-13.56.56:896][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.07.18-13.56.56:896][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.07.18-13.56.56:896][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.07.18-13.56.56:896][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.07.18-13.56.56:896][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.07.18-13.56.56:896][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.07.18-13.56.56:896][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.07.18-13.56.56:946][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.07.18-13.56.56:978][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.07.18-13.56.57:203][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.07.18-13.56.57:388][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.07.18-13.56.57:389][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.07.18-13.56.57:538][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.07.18-13.56.57:538][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.07.18-13.56.57:538][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.07.18-13.56.57:538][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.07.18-13.56.57:710][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.07.18-13.56.57:710][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.07.18-13.56.57:710][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.07.18-13.56.57:710][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.07.18-13.56.57:860][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.07.18-13.56.57:860][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.07.18-13.56.58:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.07.18-13.56.58:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.07.18-13.56.58:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.07.18-13.56.58:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.07.18-13.56.58:029][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.07.18-13.56.58:866][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   VVM_1_0
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.07.18-13.56.59:035][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.07.18-13.56.59:036][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.07.18-13.56.59:222][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.07.18-13.56.59:222][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file E:/Unreal Projects/HorizonBrigade/ueproject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.07.18-13.56.59:222][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.07.18-13.56.59:222][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file E:/Unreal Projects/HorizonBrigade/ueproject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.07.18-13.56.59:222][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.07.18-13.56.59:541][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.07.18-13.56.59:541][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.07.18-13.56.59:541][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.07.18-13.56.59:544][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.07.18-13.56.59:544][  0]LogZenServiceInstance: InTree version at 'D:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.07.18-13.56.59:545][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.07.18-13.56.59:545][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.07.18-13.56.59:545][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 30320  --child-id Zen_30320_Startup'
[2025.07.18-13.57.00:654][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.07.18-13.57.00:654][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 1.112 seconds
[2025.07.18-13.57.00:657][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.07.18-13.57.00:674][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.02 seconds.
[2025.07.18-13.57.00:674][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.36ms. RandomReadSpeed=195.46MBs, RandomWriteSpeed=129.53MBs. Assigned SpeedClass 'Local'
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.07.18-13.57.00:689][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.07.18-13.57.00:693][  0]LogShaderCompilers: Guid format shader working directory is 6 characters bigger than the processId version (E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/Shaders/WorkingDirectory/30320/).
[2025.07.18-13.57.00:693][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/742D905642E1F51B2009A3BCF7F58764/'.
[2025.07.18-13.57.00:694][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.07.18-13.57.00:695][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.07.18-13.57.00:695][  0]LogShaderCompilers: Display: Using 3 local workers for shader compilation
[2025.07.18-13.57.00:706][  0]LogShaderCompilers: Display: Compiling shader autogen file: E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.07.18-13.57.00:715][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.07.18-13.57.09:375][  0]LogSlate: Using FreeType 2.10.0
[2025.07.18-13.57.09:399][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.07.18-13.57.09:702][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.07.18-13.57.09:702][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.07.18-13.57.09:702][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.07.18-13.57.09:702][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.07.18-13.57.10:272][  0]LogAssetRegistry: FAssetRegistry took 0.0247 seconds to start up
[2025.07.18-13.57.10:281][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.07.18-13.57.10:819][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.07.18-13.57.10:819][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.07.18-13.57.10:868][  0]LogDeviceProfileManager: Active device profile: [000001BCD9C87300][000001BCC6065000 66] WindowsEditor
[2025.07.18-13.57.10:868][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.07.18-13.57.10:892][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.07.18-13.57.10:913][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/WhiteSquareTexture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/WhiteSquareTexture'.
[2025.07.18-13.57.11:435][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.006s loading caches E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/CachedAssetRegistry_*.bin.
[2025.07.18-13.57.11:435][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.07.18-13.57.11:435][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.07.18-13.57.11:435][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.07.18-13.57.11:435][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/GradientTexture0, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/GradientTexture0'.
[2025.07.18-13.57.11:549][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/Black, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/Black'.
[2025.07.18-13.57.11:571][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Actor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Actor'.
[2025.07.18-13.57.11:759][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_PortalActorIcon2, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_PortalActorIcon2'.
[2025.07.18-13.57.12:214][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ReflActorIcon, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ReflActorIcon'.
[2025.07.18-13.57.12:232][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineFonts/RobotoDistanceField, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineFonts/RobotoDistanceField'.
[2025.07.18-13.57.12:361][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:371][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.07.18-13.57.12:371][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:372][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:381][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.07.18-13.57.12:381][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:381][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:418][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_M, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_M'.
[2025.07.18-13.57.12:531][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.07.18-13.57.12:532][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_N, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/T_Default_Material_Grid_N'.
[2025.07.18-13.57.12:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.07.18-13.57.12:648][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.07.18-13.57.12:648][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:670][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:692][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:700][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/DefaultDiffuse, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/DefaultDiffuse'.
[2025.07.18-13.57.12:808][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.07.18-13.57.12:809][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.07.18-13.57.12:811][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.07.18-13.57.12:811][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.12:953][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.07.18-13.57.12:953][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.13:049][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.07.18-13.57.13:049][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.13:110][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.07.18-13.57.13:110][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.18-13.57.14:862][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TextRenderActorIcon, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TextRenderActorIcon'.
[2025.07.18-13.57.14:926][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/MatineeCam_D, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/MatineeCam_D'.
[2025.07.18-13.57.14:954][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/MatineeCam_SM, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/MatineeCam_SM'.
[2025.07.18-13.57.15:697][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.07.18-13.57.15:697][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.07.18-13.57.15:697][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.07.18-13.57.15:697][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.07.18-13.57.15:698][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.07.18-13.57.15:934][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_CameraShakeSource, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_CameraShakeSource'.
[2025.07.18-13.57.15:945][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_DecalActorIcon, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_DecalActorIcon'.
[2025.07.18-13.57.16:040][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/T_EditorHelp, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/T_EditorHelp'.
[2025.07.18-13.57.16:041][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/flipbook, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/flipbook'.
[2025.07.18-13.57.16:088][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/DebugNumberStrip, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/DebugNumberStrip'.
[2025.07.18-13.57.16:204][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/DebugNumberPeriod, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/Functions/Engine_MaterialFunctions02/ExampleContent/Textures/DebugNumberPeriod'.
[2025.07.18-13.57.16:221][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTexture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTexture'.
[2025.07.18-13.57.16:245][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/MatInstActSprite, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/MatInstActSprite'.
[2025.07.18-13.57.16:256][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_NavP, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_NavP'.
[2025.07.18-13.57.16:266][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/Bad, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/Bad'.
[2025.07.18-13.57.16:278][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Note, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Note'.
[2025.07.18-13.57.16:292][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Emitter, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Emitter'.
[2025.07.18-13.57.16:305][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TargetPoint, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TargetPoint'.
[2025.07.18-13.57.16:317][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Trigger, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Trigger'.
[2025.07.18-13.57.16:328][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VectorFieldVol, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VectorFieldVol'.
[2025.07.18-13.57.16:342][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_BoxReflectionCapture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_BoxReflectionCapture'.
[2025.07.18-13.57.16:353][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectional, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectional'.
[2025.07.18-13.57.16:365][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectionalMove, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightDirectionalMove'.
[2025.07.18-13.57.16:379][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ExpoHeightFog, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_ExpoHeightFog'.
[2025.07.18-13.57.16:389][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_SkyAtmosphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_SkyAtmosphere'.
[2025.07.18-13.57.16:406][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPoint, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPoint'.
[2025.07.18-13.57.16:416][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPointMove, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightPointMove'.
[2025.07.18-13.57.16:435][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_RadForce, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_RadForce'.
[2025.07.18-13.57.16:440][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/SkyLight, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/SkyLight'.
[2025.07.18-13.57.16:456][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerBox, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerBox'.
[2025.07.18-13.57.16:467][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerCapsule, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerCapsule'.
[2025.07.18-13.57.16:478][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerSphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_TriggerSphere'.
[2025.07.18-13.57.16:563][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_AtmosphericHeightFog, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_AtmosphericHeightFog'.
[2025.07.18-13.57.16:577][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VolumetricCloud, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_VolumetricCloud'.
[2025.07.18-13.57.16:590][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMeshes/Sphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMeshes/Sphere'.
[2025.07.18-13.57.16:610][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Player, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Player'.
[2025.07.18-13.57.16:625][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightRect, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightRect'.
[2025.07.18-13.57.16:636][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpot, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpot'.
[2025.07.18-13.57.16:647][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpotMove, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/LightIcons/S_LightSpotMove'.
[2025.07.18-13.57.16:657][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_WindDirectional, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_WindDirectional'.
[2025.07.18-13.57.16:677][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTextureCube, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultTextureCube'.
[2025.07.18-13.57.16:698][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture2D, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture2D'.
[2025.07.18-13.57.16:699][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineResources/DefaultVolumeTexture'.
[2025.07.18-13.57.16:854][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCube, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCube'.
[2025.07.18-13.57.16:875][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSphere'.
[2025.07.18-13.57.16:894][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCylinder, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorCylinder'.
[2025.07.18-13.57.16:906][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorPlane, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorPlane'.
[2025.07.18-13.57.16:980][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSkySphere, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/EditorSkySphere'.
[2025.07.18-13.57.17:075][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/MapTemplates/Sky/DaylightAmbientCubemap, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/MapTemplates/Sky/DaylightAmbientCubemap'.
[2025.07.18-13.57.18:405][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/SM_Sequencer_Node, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/SM_Sequencer_Node'.
[2025.07.18-13.57.18:433][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/AI/S_NavLink, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/AI/S_NavLink'.
[2025.07.18-13.57.18:457][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Solver, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Solver'.
[2025.07.18-13.57.18:471][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineDebugMaterials/VolumeToRender, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineDebugMaterials/VolumeToRender'.
[2025.07.18-13.57.18:528][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Base, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Base'.
[2025.07.18-13.57.18:547][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Arm, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Arm'.
[2025.07.18-13.57.18:558][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Mount, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Mount'.
[2025.07.18-13.57.18:569][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Body, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CraneRig_Body'.
[2025.07.18-13.57.18:580][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Track, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Track'.
[2025.07.18-13.57.18:594][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Mount, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_RailRig_Mount'.
[2025.07.18-13.57.18:643][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_LevelSequence, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_LevelSequence'.
[2025.07.18-13.57.18:742][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Terrain, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_Terrain'.
[2025.07.18-13.57.18:762][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorLandscapeResources/SplineEditorMesh, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorLandscapeResources/SplineEditorMesh'.
[2025.07.18-13.57.18:792][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem'.
[2025.07.18-13.57.18:793][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/FX/Niagara/Content/Icons/S_ParticleSystem'.
[2025.07.18-13.57.18:855][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairColorMap, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairColorMap'.
[2025.07.18-13.57.18:856][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairDebugColor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/HairDebugColor'.
[2025.07.18-13.57.18:965][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/BasicShapes/Cube, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/BasicShapes/Cube'.
[2025.07.18-13.57.19:006][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterBodyIslandSprite, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterBodyIslandSprite'.
[2025.07.18-13.57.19:020][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterZoneActorSprite, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Plugins/Experimental/Water/Content/Icons/WaterZoneActorSprite'.
[2025.07.18-13.57.19:330][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser'.
[2025.07.18-13.57.19:331][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked'.
[2025.07.18-13.57.19:332][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp'.
[2025.07.18-13.57.19:332][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Drop_Item_From_ContentBrowser'.
[2025.07.18-13.57.19:332][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Gizmo_Handle_Clicked'.
[2025.07.18-13.57.19:333][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor'.
[2025.07.18-13.57.19:333][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_PickUp'.
[2025.07.18-13.57.19:333][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid'.
[2025.07.18-13.57.19:333][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down'.
[2025.07.18-13.57.19:335][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up'.
[2025.07.18-13.57.19:335][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Another_Actor'.
[2025.07.18-13.57.19:335][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes'.
[2025.07.18-13.57.19:335][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Down'.
[2025.07.18-13.57.19:336][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Object_Snaps_To_Grid'.
[2025.07.18-13.57.19:336][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Scaling_Up'.
[2025.07.18-13.57.19:336][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/Sounds/UI/Selection_Changes'.
[2025.07.18-13.57.19:338][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/SnapGrid/SnapGridPlaneMesh, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/SnapGrid/SnapGridPlaneMesh'.
[2025.07.18-13.57.19:339][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/LaserPointer/VR_LaserPower_01, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/LaserPointer/VR_LaserPower_01'.
[2025.07.18-13.57.19:340][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/PlaneTranslationHandle, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/PlaneTranslationHandle'.
[2025.07.18-13.57.19:341][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleFull, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleFull'.
[2025.07.18-13.57.19:341][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleIndicator, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleIndicator'.
[2025.07.18-13.57.19:341][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleQuarter, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/RotationHandleQuarter'.
[2025.07.18-13.57.19:342][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/StartRotationHandleIndicator, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/StartRotationHandleIndicator'.
[2025.07.18-13.57.19:342][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TransformGizmoFreeRotation, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TransformGizmoFreeRotation'.
[2025.07.18-13.57.19:343][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TranslateArrowHandle, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/TranslateArrowHandle'.
[2025.07.18-13.57.19:344][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/UniformScaleHandle, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/UniformScaleHandle'.
[2025.07.18-13.57.19:423][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxCorner, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxCorner'.
[2025.07.18-13.57.19:438][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxEdge, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/VREditor/TransformGizmo/BoundingBoxEdge'.
[2025.07.18-13.57.19:474][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CineCam, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMeshes/Camera/SM_CineCam'.
[2025.07.18-13.57.19:488][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/ArtTools/RenderToTexture/Meshes/S_1_Unit_Plane, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/ArtTools/RenderToTexture/Meshes/S_1_Unit_Plane'.
[2025.07.18-13.57.19:510][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.07.18-13.57.19:531][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_NoImage, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_NoImage'.
[2025.07.18-13.57.19:540][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_OOD, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorMaterials/ParticleSystems/PSysThumbnail_OOD'.
[2025.07.18-13.57.19:729][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.07.18-13.57.19:729][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.07.18-13.57.19:731][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.07.18-13.57.19:731][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.07.18-13.57.19:775][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.07.18-13.57.19:775][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.07.18-13.57.19:777][  0]LogLiveCoding: Display: First instance in process group "UE_HorizonBrigade_0x892c1eec", spawning console
[2025.07.18-13.57.19:999][  0]LogSlate: Border
[2025.07.18-13.57.19:999][  0]LogSlate: BreadcrumbButton
[2025.07.18-13.57.19:999][  0]LogSlate: Brushes.Title
[2025.07.18-13.57.19:999][  0]LogSlate: ColorPicker.ColorThemes
[2025.07.18-13.57.19:999][  0]LogSlate: Default
[2025.07.18-13.57.19:999][  0]LogSlate: Icons.Save
[2025.07.18-13.57.19:999][  0]LogSlate: Icons.Toolbar.Settings
[2025.07.18-13.57.19:999][  0]LogSlate: ListView
[2025.07.18-13.57.19:999][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.07.18-13.57.19:999][  0]LogSlate: SoftwareCursor_Grab
[2025.07.18-13.57.19:999][  0]LogSlate: TableView.DarkRow
[2025.07.18-13.57.19:999][  0]LogSlate: TableView.Row
[2025.07.18-13.57.19:999][  0]LogSlate: TreeView
[2025.07.18-13.57.20:600][  0]LogLiveCoding: Display: Waiting for server
[2025.07.18-13.57.22:437][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EditorResources/S_FTest, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EditorResources/S_FTest'.
[2025.07.18-13.57.23:061][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.07.18-13.57.23:086][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 25.780 ms
[2025.07.18-13.57.23:499][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.07.18-13.57.23:499][  0]LogInit: XR: MultiViewport is Disabled
[2025.07.18-13.57.23:499][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.07.18-13.57.23:814][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.002 s
[2025.07.18-13.57.24:613][  0]LogAssetRegistry: Warning: FPathExistence failed to gather correct capitalization from disk for D:/Program Files/Epic Games/UE_5.6/Engine/Content/EngineMaterials/MiniFont, because GetFilenameOnDisk returned non-matching filename 'E:/Epic Games/UE_5.6/Engine/Content/EngineMaterials/MiniFont'.
[2025.07.18-13.57.25:175][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 0B5D8C08709F42D08000000000001200 | Instance: 4FDF0B6F4DB0F1A15C9AEDA5AD8137B1 (PINEAPPLE-PC-2-30320).
[2025.07.18-13.57.26:516][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.07.18-13.57.26:628][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.07.18-13.57.26:630][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.07.18-13.57.26:632][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:60695'.
[2025.07.18-13.57.26:638][  0]LogUdpMessaging: Display: Added local interface '192.168.2.101' to multicast group '230.0.0.1:6666'
[2025.07.18-13.57.26:638][  0]LogUdpMessaging: Display: Added local interface '192.168.195.212' to multicast group '230.0.0.1:6666'
[2025.07.18-13.57.26:638][  0]LogUdpMessaging: Display: Added local interface '172.30.112.1' to multicast group '230.0.0.1:6666'
[2025.07.18-13.57.26:641][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.07.18-13.57.28:821][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.07.18-13.57.28:823][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.07.18-13.57.28:868][  0]LogMetaSound: MetaSound Engine Initialized
[2025.07.18-13.57.30:343][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.07.18-13.57.33:058][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   NPU:       no
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT: Interface availability:
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   GPU: yes
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   RDG: yes
[2025.07.18-13.57.33:819][  0]LogNNERuntimeORT:   NPU: no
[2025.07.18-13.57.33:820][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.18-13.57.33:820][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.07.18-13.57.33:820][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.18-13.57.33:820][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.07.18-13.57.33:820][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.07.18-13.57.33:820][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.07.18-13.57.37:191][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.07.18-13.57.37:191][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.07.18-13.57.37:429][  0]LogTimingProfiler: Initialize
[2025.07.18-13.57.37:432][  0]LogTimingProfiler: OnSessionChanged
[2025.07.18-13.57.37:432][  0]LoadingProfiler: Initialize
[2025.07.18-13.57.37:433][  0]LoadingProfiler: OnSessionChanged
[2025.07.18-13.57.37:434][  0]LogNetworkingProfiler: Initialize
[2025.07.18-13.57.37:437][  0]LogNetworkingProfiler: OnSessionChanged
[2025.07.18-13.57.37:437][  0]LogMemoryProfiler: Initialize
[2025.07.18-13.57.37:437][  0]LogMemoryProfiler: OnSessionChanged
[2025.07.18-13.57.37:776][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.07.18-13.57.39:436][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.07.18-13.57.44:922][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.07.18-13.57.45:134][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.07.18-13.57.45:134][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.07.18-13.57.47:459][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.07.18-13.57.48:298][  0]LogWindows: Warning: CreateProc failed: 系统找不到指定的文件。 (0x00000002)
[2025.07.18-13.57.48:298][  0]LogWindows: Warning: URL: C:/Users/<USER>/AppData/Local/GitHubDesktop/app-3.4.19/resources/app/git/cmd/git.exe version
[2025.07.18-13.57.48:578][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.07.18-13.57.48:578][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.07.18-13.57.48:578][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.07.18-13.57.48:578][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.07.18-13.57.48:724][  0]LogWindows: Warning: CreateProc failed: 系统找不到指定的文件。 (0x00000002)
[2025.07.18-13.57.48:724][  0]LogWindows: Warning: URL: C:/Users/<USER>/AppData/Local/GitHubDesktop/app-3.4.19/resources/app/git/cmd/git.exe version
[2025.07.18-13.57.51:318][  0]LogCollectionManager: Loaded 0 collections in 0.002855 seconds
[2025.07.18-13.57.51:346][  0]LogFileCache: Scanning file cache for directory 'E:/Unreal Projects/HorizonBrigade/ueproject/Saved/Collections/' took 0.03s
[2025.07.18-13.57.51:432][  0]LogFileCache: Scanning file cache for directory 'E:/Unreal Projects/HorizonBrigade/ueproject/Content/Developers/pineapple/Collections/' took 0.09s
[2025.07.18-13.57.51:432][  0]LogFileCache: Scanning file cache for directory 'E:/Unreal Projects/HorizonBrigade/ueproject/Content/Collections/' took 0.00s
[2025.07.18-13.57.53:189][  0]LogStats: FPlatformStackWalk::StackWalkAndDump -  1.090 s
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: === Handled ensure: ===
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: 
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: Requested Gameplay Tag SpatialFusion.Strategy.Base was not found, tags must be loaded from config or registered as a native tag
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: Stack: 
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1d8f UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyBase::UPropagationStrategyBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:16]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d19ef UnrealEditor-SpatialFusionRuntime.dll!UDiffusionPropagationStrategy::UDiffusionPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:57]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d2f5c UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:257]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []
[2025.07.18-13.57.53:189][  0]LogOutputDevice: Error: 
[2025.07.18-13.57.56:524][  0]LogStats:                SubmitErrorReport -  0.000 s
[2025.07.18-13.58.14:957][  0]LogStats:                    SendNewReport - 18.433 s
[2025.07.18-13.58.14:957][  0]LogStats:             FDebug::EnsureFailed - 22.859 s
[2025.07.18-13.58.14:984][  0]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.027 s
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: === Handled ensure: ===
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: 
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: Requested Gameplay Tag SpatialFusion.Strategy.Diffusion was not found, tags must be loaded from config or registered as a native tag
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: Stack: 
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1a5a UnrealEditor-SpatialFusionRuntime.dll!UDiffusionPropagationStrategy::UDiffusionPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:59]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d2f5c UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:257]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []
[2025.07.18-13.58.14:984][  0]LogOutputDevice: Error: 
[2025.07.18-13.58.14:991][  0]LogStats:                SubmitErrorReport -  0.000 s
[2025.07.18-13.58.48:830][  0]LogStats:                    SendNewReport - 33.839 s
[2025.07.18-13.58.48:830][  0]LogStats:             FDebug::EnsureFailed - 33.872 s
[2025.07.18-13.58.48:851][  0]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.021 s
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: === Handled ensure: ===
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: 
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: Requested Gameplay Tag SpatialFusion.Strategy.Directional was not found, tags must be loaded from config or registered as a native tag
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: Stack: 
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1bfa UnrealEditor-SpatialFusionRuntime.dll!UDirectionalPropagationStrategy::UDirectionalPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:91]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d308d UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:262]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[2025.07.18-13.58.48:851][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []
[2025.07.18-13.58.48:852][  0]LogOutputDevice: Error: 
[2025.07.18-13.58.48:852][  0]LogStats:                SubmitErrorReport -  0.000 s
[2025.07.18-13.59.00:712][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.001 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.18-13.59.38:455][  0]LogStats:                    SendNewReport - 49.603 s
[2025.07.18-13.59.38:455][  0]LogStats:             FDebug::EnsureFailed - 49.625 s
[2025.07.18-13.59.38:477][  0]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.022 s
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: === Handled ensure: ===
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: 
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2213] 
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: Requested Gameplay Tag SpatialFusion.Strategy.Radial was not found, tags must be loaded from config or registered as a native tag
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: Stack: 
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b6eaaf UnrealEditor-GameplayTags.dll!`FMRSWRecursiveAccessDetector::AcquireWriteAccess'::`5'::<lambda_2>::operator()() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2213]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b526bd UnrealEditor-GameplayTags.dll!UGameplayTagsManager::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp:2214]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb05b51de3 UnrealEditor-GameplayTags.dll!FGameplayTag::RequestGameplayTag() [D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagContainer.cpp:1106]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1f4a UnrealEditor-SpatialFusionRuntime.dll!URadialPropagationStrategy::URadialPropagationStrategy() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:123]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d31b9 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::RegisterBuiltInStrategies() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:267]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d1eaf UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::UPropagationStrategyRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:157]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a98091 UnrealEditor-CoreUObject.dll!StaticConstructObject_Internal() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp:4958]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380d27b6 UnrealEditor-SpatialFusionRuntime.dll!UPropagationStrategyRegistry::GetGlobalRegistry() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\PropagationStrategy.cpp:247]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x000001bd380b8560 UnrealEditor-SpatialFusionRuntime.dll!UInfluenceMapManagerBase::UInfluenceMapManagerBase() [E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\SpatialFusion\Source\SpatialFusionRuntime\Private\InfluenceMapManagerBase.cpp:15]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915d18fc UnrealEditor-CoreUObject.dll!UClass::CreateDefaultObject() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5013]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa915f5b33 UnrealEditor-CoreUObject.dll!UClass::InternalCreateDefaultObjectWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\Class.cpp:5565]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91aa1dae UnrealEditor-CoreUObject.dll!UObjectLoadAllCompiledInDefaultProperties() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:953]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a84006 UnrealEditor-CoreUObject.dll!ProcessNewlyLoadedUObjects() [D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectBase.cpp:1058]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa91a71265 UnrealEditor-CoreUObject.dll!TBaseStaticDelegateInstance<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::ExecuteIfSafe() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateInstancesImpl.h:801]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954c802d UnrealEditor-Core.dll!TMulticastDelegate<void __cdecl(FName,bool),FDefaultDelegateUserPolicy>::Broadcast() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Public\Delegates\DelegateSignatureImpl.inl:1080]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffa954f491e UnrealEditor-Core.dll!FModuleManager::LoadModuleWithFailureReason() [D:\build\++UE5\Sync\Engine\Source\Runtime\Core\Private\Modules\ModuleManager.cpp:988]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4fd74 UnrealEditor-Projects.dll!FModuleDescriptor::LoadModulesForPhase() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\ModuleDescriptor.cpp:757]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a6bdc4 UnrealEditor-Projects.dll!FPluginManager::TryLoadModulesForPlugin() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2695]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb09a4f5f8 UnrealEditor-Projects.dll!FPluginManager::LoadModulesForEnabledPlugins() [D:\build\++UE5\Sync\Engine\Source\Runtime\Projects\Private\PluginManager.cpp:2832]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f23ed9 UnrealEditor.exe!FEngineLoop::LoadStartupModules() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:4425]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f27f79 UnrealEditor.exe!FEngineLoop::PreInitPostStartupScreen() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\LaunchEngineLoop.cpp:3665]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e39c UnrealEditor.exe!GuardedMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Launch.cpp:143]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f1e6ba UnrealEditor.exe!GuardedMainWrapper() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:128]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f2209e UnrealEditor.exe!LaunchWindowsStartup() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:282]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f34e44 UnrealEditor.exe!WinMain() [D:\build\++UE5\Sync\Engine\Source\Runtime\Launch\Private\Windows\LaunchWindows.cpp:339]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ff729f380fa UnrealEditor.exe!__scrt_common_main_seh() [D:\a\_work\1\s\src\vctools\crt\vcstartup\src\startup\exe_common.inl:288]
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb536c257d KERNEL32.DLL!UnknownFunction []
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: [Callstack] 0x00007ffb54c2af08 ntdll.dll!UnknownFunction []
[2025.07.18-13.59.38:477][  0]LogOutputDevice: Error: 
[2025.07.18-13.59.38:487][  0]LogStats:                SubmitErrorReport -  0.000 s
[2025.07.18-14.00.13:579][  0]LogStats:                    SendNewReport - 35.092 s
[2025.07.18-14.00.13:579][  0]LogStats:             FDebug::EnsureFailed - 35.124 s
[2025.07.18-14.00.13:579][  0]LogTemp: SpatialFusion: Default propagation strategies registered
[2025.07.18-14.00.13:657][  0]LogTemp: FGenerativeAISupportModule::StartupModule called
[2025.07.18-14.00.14:011][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.07.18-14.00.14:053][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyReport_1.log" -log="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyLog_1.log" -project="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject"  -Device=Win64@PINEAPPLE-PC-2'
[2025.07.18-14.00.14:054][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyReport_1.log" -log="E:/Unreal Projects/HorizonBrigade/ueproject/Intermediate/TurnkeyLog_1.log" -project="E:/Unreal Projects/HorizonBrigade/ueproject/HorizonBrigade.uproject"  -Device=Win64@PINEAPPLE-PC-2" -nocompile -nocompileuat ]
[2025.07.18-14.00.14:082][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.07.18-14.00.14:082][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.07.18-14.00.14:082][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.07.18-14.00.14:082][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.07.18-14.00.14:179][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.07.18-14.00.14:179][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.07.18-14.00.14:179][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.07.18-14.00.14:179][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.07.18-14.00.14:303][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-07-18T14:00:14.303Z using C
[2025.07.18-14.00.14:304][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4317.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=HorizonBrigade, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.07.18-14.00.14:310][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.07.18-14.00.14:313][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.07.18-14.00.14:391][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.07.18-14.00.14:398][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.07.18-14.00.14:398][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.07.18-14.00.14:399][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000900
[2025.07.18-14.00.24:318][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.07.18-14.00.35:106][  0]LogUnrealSharpProcHelper: Error: dotnet task failed (Args: "E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealSharp/Binaries/Managed/UnrealSharpBuildTool.dll" --Action BuildWeave --EngineDirectory "D:/Program Files/Epic Games/UE_5.6/Engine/" --ProjectDirectory "E:/Unreal Projects/HorizonBrigade/ueproject/" --ProjectName HorizonBrigade --PluginDirectory "E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealSharp" --DotNetPath "C:\Program Files\dotnet\dotnet.exe") with return code 1. Error: >>> UnrealSharpBuildTool
Command: C:\Program Files\dotnet\dotnet.exe build E:\Unreal Projects\HorizonBrigade\ueproject\Script --configuration Debug
An error occurred: Error in executing build command : 

  正在确定要还原的项目?
  所有项目均是最新的，无法还原?
E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\SpatialFusionEditor\SpatialFusionVisualization.generated.cs(85,45): error CS0234: 命名?簀㓤ƽ?间“UnrealSharp.SpatialFusionRuntime”中不存在类型或命名空间名“UFlowFieldManagerBase?是否缺少程序集引?) [E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\ProjectGlue.csproj]

生成失败?

E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\SpatialFusionEditor\SpatialFusionVisualization.generated.cs(85,45): error CS0234: 命名空间“UnrealSharp.SpatialFusionRuntime”中不存在类型或命名空间名“UFlowFieldManagerBase?是否缺少程序集引?) [E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\ProjectGlue.csproj]
    0 个警?
    1 个错?

已用时间 00:00:16.94

An error occurred: Failed to initialize action.
   at UnrealSharpBuildTool.Program.Main(String[] args) in E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Managed\UnrealSharpPrograms\UnrealSharpBuildTool\Program.cs:line 39

[2025.07.18-14.08.46:417][  0]Message dialog closed, result: Ok, title: Message, text: dotnet task failed: 
 >>> UnrealSharpBuildTool
Command: C:\Program Files\dotnet\dotnet.exe build E:\Unreal Projects\HorizonBrigade\ueproject\Script --configuration Debug
An error occurred: Error in executing build command : 

  正在确定要还原的项目?
  所有项目均是最新的，无法还原?
E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\SpatialFusionEditor\SpatialFusionVisualization.generated.cs(85,45): error CS0234: 命名?簀㓤ƽ?间“UnrealSharp.SpatialFusionRuntime”中不存在类型或命名空间名“UFlowFieldManagerBase?是否缺少程序集引?) [E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\ProjectGlue.csproj]

生成失败?

E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\SpatialFusionEditor\SpatialFusionVisualization.generated.cs(85,45): error CS0234: 命名空间“UnrealSharp.SpatialFusionRuntime”中不存在类型或命名空间名“UFlowFieldManagerBase?是否缺少程序集引?) [E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\ProjectGlue.csproj]
    0 个警?
    1 个错?

已用时间 00:00:16.94

An error occurred: Failed to initialize action.
   at UnrealSharpBuildTool.Program.Main(String[] args) in E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Managed\UnrealSharpPrograms\UnrealSharpBuildTool\Program.cs:line 39

[2025.07.18-14.08.50:683][  0]LogUnrealSharpProcHelper: Error: dotnet task failed (Args: "E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealSharp/Binaries/Managed/UnrealSharpBuildTool.dll" --Action BuildWeave --EngineDirectory "D:/Program Files/Epic Games/UE_5.6/Engine/" --ProjectDirectory "E:/Unreal Projects/HorizonBrigade/ueproject/" --ProjectName HorizonBrigade --PluginDirectory "E:/Unreal Projects/HorizonBrigade/ueproject/Plugins/UnrealSharp" --DotNetPath "C:\Program Files\dotnet\dotnet.exe") with return code 1. Error: >>> UnrealSharpBuildTool
Command: C:\Program Files\dotnet\dotnet.exe build E:\Unreal Projects\HorizonBrigade\ueproject\Script --configuration Debug
An error occurred: Error in executing build command : 

  正在确定要还原的项目?
  所有项目均是最新的，无法还原?
E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\SpatialFusionEditor\SpatialFusionVisualization.generated.cs(85,45): error CS0234: 命名?的项目?
?间“UnrealSharp.SpatialFusionRuntime”中不存在类型或命名空间名“UFlowFieldManagerBase?是否缺少程序集引?) [E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\ProjectGlue.csproj]

生成失败?

E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\SpatialFusionEditor\SpatialFusionVisualization.generated.cs(85,45): error CS0234: 命名空间“UnrealSharp.SpatialFusionRuntime”中不存在类型或命名空间名“UFlowFieldManagerBase?是否缺少程序集引?) [E:\Unreal Projects\HorizonBrigade\ueproject\Script\ProjectGlue\ProjectGlue.csproj]
    0 个警?
    1 个错?

已用时间 00:00:03.26

An error occurred: Failed to initialize action.
   at UnrealSharpBuildTool.Program.Main(String[] args) in E:\Unreal Projects\HorizonBrigade\ueproject\Plugins\UnrealSharp\Managed\UnrealSharpPrograms\UnrealSharpBuildTool\Program.cs:line 39

[2025.07.18-14.59.00:729][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.009 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.18-15.59.00:725][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
