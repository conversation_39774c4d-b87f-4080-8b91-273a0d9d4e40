{"Structs": {"BlittableTypes": {"EStreamingSourcePriority": {"Name": "EStreamingSourcePriority", "ManagedType": null}, "ETriggerEvent": {"Name": "ETriggerEvent", "ManagedType": null}, "FVector": {"Name": "FVector", "ManagedType": "UnrealSharp.CoreUObject.FVector"}, "FVector2D": {"Name": "FVector2D", "ManagedType": "UnrealSharp.CoreUObject.FVector2D"}, "FVector_NetQuantize": {"Name": "FVector_NetQuantize", "ManagedType": "UnrealSharp.CoreUObject.FVector"}, "FVector_NetQuantize10": {"Name": "FVector_NetQuantize10", "ManagedType": "UnrealSharp.CoreUObject.FVector"}, "FVector_NetQuantize100": {"Name": "FVector_NetQuantize100", "ManagedType": "UnrealSharp.CoreUObject.FVector"}, "FVector_NetQuantizeNormal": {"Name": "FVector_NetQuantizeNormal", "ManagedType": "UnrealSharp.CoreUObject.FVector"}, "FVector2f": {"Name": "FVector2f", "ManagedType": "UnrealSharp.CoreUObject.FVector2f"}, "FVector3f": {"Name": "FVector3f", "ManagedType": "UnrealSharp.CoreUObject.FVector3f"}, "FVector4f": {"Name": "FVector4f", "ManagedType": "UnrealSharp.CoreUObject.FVector4f"}, "FQuatf4": {"Name": "FQuatf4", "ManagedType": "UnrealSharp.CoreUObject.FVector4f"}, "FRotator": {"Name": "FRotator", "ManagedType": "UnrealSharp.CoreUObject.FRotator"}, "FMatrix44f": {"Name": "FMatrix44f", "ManagedType": "UnrealSharp.CoreUObject.FMatrix44f"}, "FTransform": {"Name": "FTransform", "ManagedType": "UnrealSharp.CoreUObject.FTransform"}, "FTimerHandle": {"Name": "FTimer<PERSON>andle", "ManagedType": "UnrealSharp.Engine.FTimerHandle"}, "FInputActionValue": {"Name": "FInputActionValue", "ManagedType": "UnrealSharp.EnhancedInput.FInputActionValue"}, "FRandomStream": {"Name": "FRandomStream", "ManagedType": "UnrealSharp.CoreUObject.FRandomStream"}}, "NativelyCopyableTypes": {"FMoverDataCollection": {"Name": "FMoverDataCollection", "HasDestructor": false}, "FPaintContext": {"Name": "FPaintContext", "HasDestructor": false}, "FGeometry": {"Name": "FGeometry", "HasDestructor": false}, "FGameplayEffectSpec": {"Name": "FGameplayEffectSpec", "HasDestructor": true}, "FGameplayEffectSpecHandle": {"Name": "FGameplayEffectSpecHandle", "HasDestructor": true}}}}