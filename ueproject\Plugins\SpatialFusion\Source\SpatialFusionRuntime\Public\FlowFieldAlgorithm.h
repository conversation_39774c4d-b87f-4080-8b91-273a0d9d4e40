// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "FlowFieldTypes.h"
#include "FlowFieldAlgorithm.generated.h"

// Forward declarations
class UInfluenceMapManagerBase;

/**
 * Abstract base class for flow field algorithms
 * Implements the strategy pattern for different pathfinding algorithms
 */
UCLASS(Abstract, BlueprintType)
class SPATIALFUSIONRUNTIME_API UFlowFieldAlgorithm : public UObject
{
	GENERATED_BODY()

public:
	UFlowFieldAlgorithm();

	/**
	 * Calculate flow field using this algorithm
	 * @param GridManager - Grid manager to use for calculations
	 * @param Query - Flow field query with goals and parameters
	 * @param OutFlowField - Output flow field data
	 * @return Calculation result
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Algorithm")
	virtual FFlowFieldResult CalculateFlowField(
		UInfluenceMapManagerBase* GridManager,
		const FFlowFieldQuery& Query,
		TArray<FFlowFieldCell>& OutFlowField
	);

	/**
	 * Get the algorithm name
	 * @return Algorithm name
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Algorithm")
	virtual FString GetAlgorithmName() const { return TEXT("Base Algorithm"); }

	/**
	 * Check if this algorithm supports the given parameters
	 * @param Params - Parameters to check
	 * @return True if parameters are supported
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Algorithm")
	virtual bool SupportsParameters(const FFlowFieldParams& Params) const { return true; }

	/**
	 * Get estimated complexity for given grid size
	 * @param GridSize - Size of the grid
	 * @param NumGoals - Number of goals
	 * @return Estimated time complexity (arbitrary units)
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Algorithm")
	virtual float GetEstimatedComplexity(const FVector& GridSize, int32 NumGoals) const;

protected:
	/**
	 * Internal implementation of the algorithm (to be overridden)
	 * @param GridManager - Grid manager
	 * @param Query - Flow field query
	 * @param OutFlowField - Output flow field
	 * @return Calculation result
	 */
	virtual FFlowFieldResult CalculateFlowFieldInternal(
		UInfluenceMapManagerBase* GridManager,
		const FFlowFieldQuery& Query,
		TArray<FFlowFieldCell>& OutFlowField
	) { return FFlowFieldResult(); }

	/**
	 * Initialize flow field cells
	 * @param GridManager - Grid manager
	 * @param Query - Flow field query
	 * @param OutFlowField - Flow field to initialize
	 */
	virtual void InitializeFlowField(
		UInfluenceMapManagerBase* GridManager,
		const FFlowFieldQuery& Query,
		TArray<FFlowFieldCell>& OutFlowField
	);

	/**
	 * Set goal cells in the flow field
	 * @param GridManager - Grid manager
	 * @param Goals - Goals to set
	 * @param OutFlowField - Flow field to modify
	 */
	virtual void SetGoalCells(
		UInfluenceMapManagerBase* GridManager,
		const TArray<FFlowFieldGoal>& Goals,
		TArray<FFlowFieldCell>& OutFlowField
	);

	/**
	 * Calculate movement cost for a cell
	 * @param GridManager - Grid manager
	 * @param Position - Cell position
	 * @param Params - Flow field parameters
	 * @return Movement cost
	 */
	virtual float CalculateMovementCost(
		UInfluenceMapManagerBase* GridManager,
		const FVector& Position,
		const FFlowFieldParams& Params
	) const;

	/**
	 * Get neighbors of a cell
	 * @param GridManager - Grid manager
	 * @param Position - Cell position
	 * @param bAllowDiagonal - Whether to include diagonal neighbors
	 * @return Array of neighbor positions
	 */
	virtual TArray<FVector> GetNeighbors(
		UInfluenceMapManagerBase* GridManager,
		const FVector& Position,
		bool bAllowDiagonal
	) const;

	/**
	 * Calculate distance between two positions
	 * @param From - Start position
	 * @param To - End position
	 * @param bAllowDiagonal - Whether diagonal movement is allowed
	 * @return Distance
	 */
	virtual float CalculateDistance(
		const FVector& From,
		const FVector& To,
		bool bAllowDiagonal
	) const;

	/**
	 * Generate flow directions from integration field
	 * @param GridManager - Grid manager
	 * @param OutFlowField - Flow field to generate directions for
	 * @param Params - Flow field parameters
	 */
	virtual void GenerateFlowDirections(
		UInfluenceMapManagerBase* GridManager,
		TArray<FFlowFieldCell>& OutFlowField,
		const FFlowFieldParams& Params
	);

	/**
	 * Smooth flow directions
	 * @param GridManager - Grid manager
	 * @param OutFlowField - Flow field to smooth
	 * @param SmoothingFactor - Smoothing factor (0-1)
	 */
	virtual void SmoothFlowDirections(
		UInfluenceMapManagerBase* GridManager,
		TArray<FFlowFieldCell>& OutFlowField,
		float SmoothingFactor
	);

	/**
	 * Convert world position to grid index
	 * @param GridManager - Grid manager
	 * @param WorldPosition - World position
	 * @return Grid index
	 */
	virtual int32 WorldToGridIndex(
		UInfluenceMapManagerBase* GridManager,
		const FVector& WorldPosition
	) const;

	/**
	 * Convert grid index to world position
	 * @param GridManager - Grid manager
	 * @param GridIndex - Grid index
	 * @return World position
	 */
	virtual FVector GridIndexToWorld(
		UInfluenceMapManagerBase* GridManager,
		int32 GridIndex
	) const;

	/**
	 * Check if a position is valid
	 * @param GridManager - Grid manager
	 * @param Position - Position to check
	 * @return True if position is valid
	 */
	virtual bool IsValidPosition(
		UInfluenceMapManagerBase* GridManager,
		const FVector& Position
	) const;
};

/**
 * Dijkstra algorithm implementation for flow fields
 */
UCLASS(BlueprintType)
class SPATIALFUSIONRUNTIME_API UDijkstraFlowFieldAlgorithm : public UFlowFieldAlgorithm
{
	GENERATED_BODY()

public:
	UDijkstraFlowFieldAlgorithm();

	// UFlowFieldAlgorithm interface
	virtual FString GetAlgorithmName() const override { return TEXT("Dijkstra"); }
	virtual bool SupportsParameters(const FFlowFieldParams& Params) const override;
	virtual float GetEstimatedComplexity(const FVector& GridSize, int32 NumGoals) const override;

protected:
	// UFlowFieldAlgorithm interface
	virtual FFlowFieldResult CalculateFlowFieldInternal(
		UInfluenceMapManagerBase* GridManager,
		const FFlowFieldQuery& Query,
		TArray<FFlowFieldCell>& OutFlowField
	) override;

private:
	/**
	 * Priority queue node for Dijkstra algorithm
	 */
	struct FDijkstraNode
	{
		int32 Index;
		float Distance;

		FDijkstraNode(int32 InIndex, float InDistance)
			: Index(InIndex), Distance(InDistance) {}

		bool operator<(const FDijkstraNode& Other) const
		{
			return Distance > Other.Distance; // Min-heap
		}
	};

	/**
	 * Run Dijkstra algorithm to calculate integration field
	 * @param GridManager - Grid manager
	 * @param Goals - Goal positions
	 * @param OutFlowField - Flow field to calculate
	 * @param Params - Algorithm parameters
	 * @return Number of cells processed
	 */
	int32 RunDijkstra(
		UInfluenceMapManagerBase* GridManager,
		const TArray<FFlowFieldGoal>& Goals,
		TArray<FFlowFieldCell>& OutFlowField,
		const FFlowFieldParams& Params
	);
};

/**
 * A* algorithm implementation for flow fields
 */
UCLASS(BlueprintType)
class SPATIALFUSIONRUNTIME_API UAStarFlowFieldAlgorithm : public UFlowFieldAlgorithm
{
	GENERATED_BODY()

public:
	UAStarFlowFieldAlgorithm();

	// UFlowFieldAlgorithm interface
	virtual FString GetAlgorithmName() const override { return TEXT("A*"); }
	virtual bool SupportsParameters(const FFlowFieldParams& Params) const override;
	virtual float GetEstimatedComplexity(const FVector& GridSize, int32 NumGoals) const override;

protected:
	// UFlowFieldAlgorithm interface
	virtual FFlowFieldResult CalculateFlowFieldInternal(
		UInfluenceMapManagerBase* GridManager,
		const FFlowFieldQuery& Query,
		TArray<FFlowFieldCell>& OutFlowField
	) override;

private:
	/**
	 * Calculate heuristic distance to nearest goal
	 * @param Position - Current position
	 * @param Goals - Goal positions
	 * @return Heuristic distance
	 */
	float CalculateHeuristic(
		const FVector& Position,
		const TArray<FFlowFieldGoal>& Goals
	) const;
};

/**
 * Flow field algorithm registry
 */
UCLASS(BlueprintType)
class SPATIALFUSIONRUNTIME_API UFlowFieldAlgorithmRegistry : public UObject
{
	GENERATED_BODY()

public:
	UFlowFieldAlgorithmRegistry();

	/**
	 * Register an algorithm
	 * @param Method - Algorithm method
	 * @param Algorithm - Algorithm instance
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Registry")
	void RegisterAlgorithm(EFlowFieldMethod Method, UFlowFieldAlgorithm* Algorithm);

	/**
	 * Get algorithm for method
	 * @param Method - Algorithm method
	 * @return Algorithm instance or nullptr
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Registry")
	UFlowFieldAlgorithm* GetAlgorithm(EFlowFieldMethod Method) const;

	/**
	 * Get all registered algorithms
	 * @return Map of method to algorithm
	 */
	UFUNCTION(BlueprintCallable, Category = "Flow Field Registry")
	TMap<EFlowFieldMethod, UFlowFieldAlgorithm*> GetAllAlgorithms() const;

	/**
	 * Get global registry instance
	 * @return Global registry
	 */
	static UFlowFieldAlgorithmRegistry* GetGlobalRegistry();

protected:
	/** Map of algorithms by method */
	UPROPERTY()
	TMap<EFlowFieldMethod, TObjectPtr<UFlowFieldAlgorithm>> Algorithms;

	/** Global registry instance */
	static UFlowFieldAlgorithmRegistry* GlobalRegistry;

	/**
	 * Initialize default algorithms
	 */
	void InitializeDefaultAlgorithms();
};
