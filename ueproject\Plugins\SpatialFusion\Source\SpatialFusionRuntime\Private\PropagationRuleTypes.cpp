// Copyright Epic Games, Inc. All Rights Reserved.

#include "PropagationRuleTypes.h"
#include "SpatialFusionTypes.h"
#include "InfluenceMapManagerBase.h"

//////////////////////////////////////////////////////////////////////////
// FPropagationCondition

bool FPropagationCondition::EvaluateCondition(const FPropagationContext& Context) const
{
	bool Result = false;

	switch (ConditionType)
	{
	case EPropagationConditionType::Always:
		Result = true;
		break;

	case EPropagationConditionType::ValueThreshold:
		{
			float CellValue = Context.GetCellValue(ValueKey);
			Result = CompareValues(CellValue, Threshold, ComparisonOperator);
		}
		break;

	case EPropagationConditionType::DistanceThreshold:
		{
			// Check distance to nearest neighbor with specific criteria
			float MinDistance = FLT_MAX;
			for (int32 i = 0; i < Context.NeighborPositions.Num(); i++)
			{
				float NeighborValue = Context.GetNeighborValue(i, ValueKey);
				if (CompareValues(NeighborValue, Threshold, ComparisonOperator))
				{
					float Distance = FVector::Distance(Context.CellPosition, Context.NeighborPositions[i]);
					MinDistance = FMath::Min(MinDistance, Distance);
				}
			}
			Result = MinDistance <= DistanceThreshold;
		}
		break;

	case EPropagationConditionType::TimeThreshold:
		Result = Context.TimeSinceLastExecution >= TimeThreshold;
		break;

	case EPropagationConditionType::TagPresence:
		Result = Context.HasCellTag(RequiredTag);
		break;

	case EPropagationConditionType::NeighborCount:
		{
			int32 MatchingNeighbors = Context.GetMatchingNeighborCount(ValueKey, Threshold, ComparisonOperator);
			Result = MatchingNeighbors >= MinNeighborCount && MatchingNeighbors <= MaxNeighborCount;
		}
		break;

	case EPropagationConditionType::Custom:
		// Custom conditions would be implemented by derived classes or delegates
		Result = EvaluateCustomCondition(Context);
		break;

	default:
		Result = false;
		break;
	}

	// Apply exclusion tags
	if (Result && ExcludedTags.Num() > 0)
	{
		for (const FGameplayTag& ExcludedTag : ExcludedTags)
		{
			if (Context.HasCellTag(ExcludedTag))
			{
				Result = false;
				break;
			}
		}
	}

	// Invert result if requested
	if (bInvertResult)
	{
		Result = !Result;
	}

	return Result;
}

bool FPropagationCondition::CompareValues(float Value1, float Value2, const FString& Operator) const
{
	if (Operator == TEXT(">"))
	{
		return Value1 > Value2;
	}
	else if (Operator == TEXT("<"))
	{
		return Value1 < Value2;
	}
	else if (Operator == TEXT(">="))
	{
		return Value1 >= Value2;
	}
	else if (Operator == TEXT("<="))
	{
		return Value1 <= Value2;
	}
	else if (Operator == TEXT("=="))
	{
		return FMath::IsNearlyEqual(Value1, Value2, 0.001f);
	}
	else if (Operator == TEXT("!="))
	{
		return !FMath::IsNearlyEqual(Value1, Value2, 0.001f);
	}

	// Default to greater than
	return Value1 > Value2;
}

bool FPropagationCondition::EvaluateCustomCondition(const FPropagationContext& Context) const
{
	// Custom condition evaluation would be implemented here
	// For now, return false as a safe default
	return false;
}

//////////////////////////////////////////////////////////////////////////
// FPropagationAction

bool FPropagationAction::ExecuteAction(FPropagationContext& Context) const
{
	bool bSuccess = false;

	switch (ActionType)
	{
	case EPropagationActionType::SetValue:
		{
			Context.SetCellValue(TargetValueKey, ActionValue);
			bSuccess = true;
		}
		break;

	case EPropagationActionType::AddValue:
		{
			float CurrentValue = Context.GetCellValue(TargetValueKey);
			float NewValue = CurrentValue + ActionValue;
			if (bClampResult)
			{
				NewValue = FMath::Clamp(NewValue, ClampMin, ClampMax);
			}
			Context.SetCellValue(TargetValueKey, NewValue);
			bSuccess = true;
		}
		break;

	case EPropagationActionType::MultiplyValue:
		{
			float CurrentValue = Context.GetCellValue(TargetValueKey);
			float NewValue = CurrentValue * ActionValue;
			if (bClampResult)
			{
				NewValue = FMath::Clamp(NewValue, ClampMin, ClampMax);
			}
			Context.SetCellValue(TargetValueKey, NewValue);
			bSuccess = true;
		}
		break;

	case EPropagationActionType::InterpolateValue:
		{
			float CurrentValue = Context.GetCellValue(TargetValueKey);
			float NewValue = FMath::Lerp(CurrentValue, ActionValue, InterpolationSpeed * Context.DeltaTime);
			if (bClampResult)
			{
				NewValue = FMath::Clamp(NewValue, ClampMin, ClampMax);
			}
			Context.SetCellValue(TargetValueKey, NewValue);
			bSuccess = true;
		}
		break;

	case EPropagationActionType::CopyValue:
		{
			float SourceValue = Context.GetCellValue(SourceValueKey);
			if (bClampResult)
			{
				SourceValue = FMath::Clamp(SourceValue, ClampMin, ClampMax);
			}
			Context.SetCellValue(TargetValueKey, SourceValue);
			bSuccess = true;
		}
		break;

	case EPropagationActionType::ApplyCurve:
		{
			if (TransformCurve)
			{
				float CurrentValue = Context.GetCellValue(TargetValueKey);
				float CurveValue = TransformCurve->GetFloatValue(CurrentValue);
				if (bClampResult)
				{
					CurveValue = FMath::Clamp(CurveValue, ClampMin, ClampMax);
				}
				Context.SetCellValue(TargetValueKey, CurveValue);
				bSuccess = true;
			}
		}
		break;

	case EPropagationActionType::Custom:
		bSuccess = ExecuteCustomAction(Context);
		break;

	default:
		bSuccess = false;
		break;
	}

	// Apply tag changes
	if (bSuccess)
	{
		for (const FGameplayTag& TagToAdd : TagsToAdd)
		{
			Context.AddCellTag(TagToAdd);
		}

		for (const FGameplayTag& TagToRemove : TagsToRemove)
		{
			Context.RemoveCellTag(TagToRemove);
		}
	}

	return bSuccess;
}

bool FPropagationAction::ExecuteCustomAction(FPropagationContext& Context) const
{
	// Custom action execution would be implemented here
	// For now, return false as a safe default
	return false;
}

//////////////////////////////////////////////////////////////////////////
// FPropagationContext

float FPropagationContext::GetCellValue(const FName& ValueKey) const
{
	if (const float* Value = CellValues.Find(ValueKey))
	{
		return *Value;
	}
	return 0.0f;
}

void FPropagationContext::SetCellValue(const FName& ValueKey, float Value)
{
	CellValues.Add(ValueKey, Value);
}

float FPropagationContext::GetNeighborValue(int32 NeighborIndex, const FName& ValueKey) const
{
	if (NeighborIndex >= 0 && NeighborIndex < NeighborValues.Num())
	{
		if (const float* Value = NeighborValues[NeighborIndex].Values.Find(ValueKey))
		{
			return *Value;
		}
	}
	return 0.0f;
}

bool FPropagationContext::HasCellTag(const FGameplayTag& Tag) const
{
	return CellTags.HasTag(Tag);
}

void FPropagationContext::AddCellTag(const FGameplayTag& Tag)
{
	CellTags.AddTag(Tag);
}

void FPropagationContext::RemoveCellTag(const FGameplayTag& Tag)
{
	CellTags.RemoveTag(Tag);
}

int32 FPropagationContext::GetMatchingNeighborCount(const FName& ValueKey, float Threshold, const FString& ComparisonOperator) const
{
	int32 MatchingCount = 0;

	for (int32 i = 0; i < NeighborValues.Num(); i++)
	{
		float NeighborValue = GetNeighborValue(i, ValueKey);
		
		bool bMatches = false;
		if (ComparisonOperator == TEXT(">"))
		{
			bMatches = NeighborValue > Threshold;
		}
		else if (ComparisonOperator == TEXT("<"))
		{
			bMatches = NeighborValue < Threshold;
		}
		else if (ComparisonOperator == TEXT(">="))
		{
			bMatches = NeighborValue >= Threshold;
		}
		else if (ComparisonOperator == TEXT("<="))
		{
			bMatches = NeighborValue <= Threshold;
		}
		else if (ComparisonOperator == TEXT("=="))
		{
			bMatches = FMath::IsNearlyEqual(NeighborValue, Threshold, 0.001f);
		}
		else if (ComparisonOperator == TEXT("!="))
		{
			bMatches = !FMath::IsNearlyEqual(NeighborValue, Threshold, 0.001f);
		}

		if (bMatches)
		{
			MatchingCount++;
		}
	}

	return MatchingCount;
}
