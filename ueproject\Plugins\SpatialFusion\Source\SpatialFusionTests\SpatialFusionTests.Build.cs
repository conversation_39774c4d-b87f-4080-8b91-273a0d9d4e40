// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class SpatialFusionTests : ModuleRules
{
	public SpatialFusionTests(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"SpatialFusionRuntime",
				"SpatialFusionAI"
			}
		);
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"UnrealEd",
				"AutomationController",
				"FunctionalTesting"
			}
		);
	}
}
