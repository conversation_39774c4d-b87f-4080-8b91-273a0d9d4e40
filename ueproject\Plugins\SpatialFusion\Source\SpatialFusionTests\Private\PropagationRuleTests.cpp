// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "PropagationRuleTypes.h"
#include "PropagationRuleAsset.h"
#include "PropagationRuleSet.h"
#include "PropagationRuleEngine.h"
#include "InfluenceMapManager2D.h"

/**
 * Test suite for propagation rule core functionality
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPropagationRuleCoreTest, "SpatialFusion.Rules.Core", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FPropagationRuleCoreTest::RunTest(const FString& Parameters)
{
	// Test FPropagationCondition
	{
		FPropagationCondition Condition;
		Condition.ConditionType = EPropagationConditionType::ValueThreshold;
		Condition.ValueKey = TEXT("Test");
		Condition.Threshold = 50.0f;
		Condition.ComparisonOperator = TEXT(">");
		
		// Create test context
		FPropagationContext Context;
		Context.SetCellValue(TEXT("Test"), 75.0f);
		
		TestTrue("Condition should pass when value > threshold", Condition.EvaluateCondition(Context));
		
		Context.SetCellValue(TEXT("Test"), 25.0f);
		TestFalse("Condition should fail when value < threshold", Condition.EvaluateCondition(Context));
		
		// Test inversion
		Condition.bInvertResult = true;
		TestTrue("Inverted condition should pass when value < threshold", Condition.EvaluateCondition(Context));
	}

	// Test FPropagationAction
	{
		FPropagationAction Action;
		Action.ActionType = EPropagationActionType::AddValue;
		Action.TargetValueKey = TEXT("Test");
		Action.ActionValue = 25.0f;
		
		FPropagationContext Context;
		Context.SetCellValue(TEXT("Test"), 50.0f);
		
		bool bSuccess = Action.ExecuteAction(Context);
		TestTrue("Action should execute successfully", bSuccess);
		TestEqual("Value should be added", Context.GetCellValue(TEXT("Test")), 75.0f);
		
		// Test clamping
		Action.bClampResult = true;
		Action.ClampMax = 80.0f;
		Action.ActionValue = 20.0f;
		
		Action.ExecuteAction(Context);
		TestEqual("Value should be clamped", Context.GetCellValue(TEXT("Test")), 80.0f);
	}

	// Test FPropagationContext
	{
		FPropagationContext Context;
		
		// Test value operations
		Context.SetCellValue(TEXT("Health"), 100.0f);
		TestEqual("Set value should work", Context.GetCellValue(TEXT("Health")), 100.0f);
		TestEqual("Non-existent value should return 0", Context.GetCellValue(TEXT("NonExistent")), 0.0f);
		
		// Test tag operations
		FGameplayTag TestTag = FGameplayTag::RequestGameplayTag(TEXT("Test.Tag"));
		Context.AddCellTag(TestTag);
		TestTrue("Cell should have tag", Context.HasCellTag(TestTag));
		
		Context.RemoveCellTag(TestTag);
		TestFalse("Cell should not have tag after removal", Context.HasCellTag(TestTag));
		
		// Test neighbor operations
		Context.NeighborPositions.Add(FVector(100, 100, 0));
		Context.NeighborValues.AddDefaulted();
		Context.NeighborValues[0].Add(TEXT("Test"), 42.0f);
		
		TestEqual("Neighbor value should be retrievable", Context.GetNeighborValue(0, TEXT("Test")), 42.0f);
		TestEqual("Invalid neighbor should return 0", Context.GetNeighborValue(1, TEXT("Test")), 0.0f);
		
		// Test neighbor counting
		Context.NeighborPositions.Add(FVector(200, 100, 0));
		Context.NeighborValues.AddDefaulted();
		Context.NeighborValues[1].Add(TEXT("Test"), 60.0f);
		
		int32 MatchingCount = Context.GetMatchingNeighborCount(TEXT("Test"), 50.0f, TEXT(">"));
		TestEqual("Should have 1 neighbor > 50", MatchingCount, 1);
		
		MatchingCount = Context.GetMatchingNeighborCount(TEXT("Test"), 30.0f, TEXT(">"));
		TestEqual("Should have 2 neighbors > 30", MatchingCount, 2);
	}

	return true;
}

/**
 * Test suite for propagation rule assets
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPropagationRuleAssetTest, "SpatialFusion.Rules.Asset", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FPropagationRuleAssetTest::RunTest(const FString& Parameters)
{
	// Create test rule
	UPropagationRuleAsset* TestRule = NewObject<UPropagationRuleAsset>();
	TestRule->RuleName = TEXT("TestRule");
	TestRule->Priority = EPropagationRulePriority::High;
	TestRule->bIsEnabled = true;
	
	// Add condition
	FPropagationCondition Condition;
	Condition.ConditionType = EPropagationConditionType::ValueThreshold;
	Condition.ValueKey = TEXT("Temperature");
	Condition.Threshold = 80.0f;
	Condition.ComparisonOperator = TEXT(">");
	TestRule->Conditions.Add(Condition);
	
	// Add action
	FPropagationAction Action;
	Action.ActionType = EPropagationActionType::AddValue;
	Action.TargetValueKey = TEXT("Fire");
	Action.ActionValue = 10.0f;
	TestRule->Actions.Add(Action);
	
	// Test rule evaluation
	{
		FPropagationContext Context;
		Context.SetCellValue(TEXT("Temperature"), 90.0f);
		
		TestTrue("Rule should execute when conditions are met", TestRule->ShouldExecute(Context));
		
		Context.SetCellValue(TEXT("Temperature"), 70.0f);
		TestFalse("Rule should not execute when conditions are not met", TestRule->ShouldExecute(Context));
	}
	
	// Test rule execution
	{
		FPropagationContext Context;
		Context.SetCellValue(TEXT("Temperature"), 90.0f);
		Context.SetCellValue(TEXT("Fire"), 5.0f);
		
		bool bExecuted = TestRule->ExecuteRule(Context);
		TestTrue("Rule should execute successfully", bExecuted);
		TestEqual("Fire value should be increased", Context.GetCellValue(TEXT("Fire")), 15.0f);
	}
	
	// Test priority value
	{
		int32 PriorityValue = TestRule->GetPriorityValue();
		TestTrue("High priority should have high value", PriorityValue > 50);
		
		TestRule->Priority = EPropagationRulePriority::Low;
		int32 LowPriorityValue = TestRule->GetPriorityValue();
		TestTrue("Low priority should have lower value", LowPriorityValue < PriorityValue);
	}
	
	// Test rule validation
	{
		TArray<FString> Errors;
		bool bValid = TestRule->ValidateRule(Errors);
		TestTrue("Valid rule should pass validation", bValid);
		TestEqual("Valid rule should have no errors", Errors.Num(), 0);
		
		// Create invalid rule
		UPropagationRuleAsset* InvalidRule = NewObject<UPropagationRuleAsset>();
		InvalidRule->RuleName = TEXT("");
		InvalidRule->Conditions.Empty();
		InvalidRule->Actions.Empty();
		
		TArray<FString> InvalidErrors;
		bool bInvalid = InvalidRule->ValidateRule(InvalidErrors);
		TestFalse("Invalid rule should fail validation", bInvalid);
		TestTrue("Invalid rule should have errors", InvalidErrors.Num() > 0);
	}
	
	// Test rule dependencies
	{
		UPropagationRuleAsset* PrereqRule = NewObject<UPropagationRuleAsset>();
		PrereqRule->RuleName = TEXT("PrereqRule");
		
		TestRule->PrerequisiteRules.Add(PrereqRule);
		TestTrue("Rule should depend on prerequisite", TestRule->DependsOn(PrereqRule));
		TestFalse("Prerequisite should not depend on rule", PrereqRule->DependsOn(TestRule));
		
		UPropagationRuleAsset* ConflictRule = NewObject<UPropagationRuleAsset>();
		ConflictRule->RuleName = TEXT("ConflictRule");
		
		TestRule->ConflictingRules.Add(ConflictRule);
		TestTrue("Rule should conflict with conflict rule", TestRule->ConflictsWith(ConflictRule));
		TestFalse("Rule should not conflict with prerequisite", TestRule->ConflictsWith(PrereqRule));
	}

	return true;
}

/**
 * Test suite for propagation rule sets
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPropagationRuleSetTest, "SpatialFusion.Rules.RuleSet", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FPropagationRuleSetTest::RunTest(const FString& Parameters)
{
	// Create test rule set
	UPropagationRuleSet* RuleSet = NewObject<UPropagationRuleSet>();
	RuleSet->RuleSetName = TEXT("TestRuleSet");
	RuleSet->ExecutionStrategy = ERuleExecutionStrategy::Priority;
	RuleSet->ConflictResolution = ERuleConflictResolution::Priority;
	
	// Create test rules
	UPropagationRuleAsset* HighPriorityRule = NewObject<UPropagationRuleAsset>();
	HighPriorityRule->RuleName = TEXT("HighPriorityRule");
	HighPriorityRule->Priority = EPropagationRulePriority::High;
	HighPriorityRule->bIsEnabled = true;
	
	UPropagationRuleAsset* LowPriorityRule = NewObject<UPropagationRuleAsset>();
	LowPriorityRule->RuleName = TEXT("LowPriorityRule");
	LowPriorityRule->Priority = EPropagationRulePriority::Low;
	LowPriorityRule->bIsEnabled = true;
	
	UPropagationRuleAsset* DisabledRule = NewObject<UPropagationRuleAsset>();
	DisabledRule->RuleName = TEXT("DisabledRule");
	DisabledRule->Priority = EPropagationRulePriority::Normal;
	DisabledRule->bIsEnabled = false;
	
	// Add rules to set
	RuleSet->Rules.Add(LowPriorityRule);
	RuleSet->Rules.Add(HighPriorityRule);
	RuleSet->Rules.Add(DisabledRule);
	
	// Test rule management
	{
		TestTrue("Should add rule successfully", RuleSet->AddRule(NewObject<UPropagationRuleAsset>()));
		TestEqual("Should have 4 rules", RuleSet->Rules.Num(), 4);
		
		TestTrue("Should remove rule successfully", RuleSet->RemoveRule(DisabledRule));
		TestEqual("Should have 3 rules after removal", RuleSet->Rules.Num(), 3);
		
		TArray<UPropagationRuleAsset*> EnabledRules = RuleSet->GetEnabledRules();
		TestEqual("Should have 2 enabled rules", EnabledRules.Num(), 2);
	}
	
	// Test execution order
	{
		TArray<UPropagationRuleAsset*> ExecutionOrder = RuleSet->GetRulesInExecutionOrder();
		TestEqual("Should have rules in execution order", ExecutionOrder.Num(), 3);
		
		// High priority should come first
		TestEqual("First rule should be high priority", ExecutionOrder[0]->Priority, EPropagationRulePriority::High);
		TestEqual("Second rule should be low priority", ExecutionOrder[1]->Priority, EPropagationRulePriority::Low);
	}
	
	// Test rule filtering
	{
		FGameplayTag FilterTag = FGameplayTag::RequestGameplayTag(TEXT("Filter.Test"));
		UPropagationRuleSet* FilteredSet = RuleSet->CreateFilteredRuleSet(
			FGameplayTagContainer(FilterTag), false);
		
		TestNotNull("Filtered set should be created", FilteredSet);
		// Since our test rules don't have tags, filtered set should be empty
		TestEqual("Filtered set should have no matching rules", FilteredSet->Rules.Num(), 0);
	}
	
	// Test rule set validation
	{
		TArray<FString> Errors, Warnings;
		bool bValid = RuleSet->ValidateRuleSet(Errors, Warnings);
		TestTrue("Valid rule set should pass validation", bValid);
	}

	return true;
}

/**
 * Test suite for propagation rule engine
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPropagationRuleEngineTest, "SpatialFusion.Rules.Engine", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FPropagationRuleEngineTest::RunTest(const FString& Parameters)
{
	// Create test components
	UInfluenceMapManager2D* GridManager = NewObject<UInfluenceMapManager2D>();
	GridManager->Initialize(FVector(10, 10, 1), 100.0f, FVector::ZeroVector);
	
	UPropagationRuleEngine* RuleEngine = NewObject<UPropagationRuleEngine>();
	RuleEngine->GridManager = GridManager;
	
	// Create test rule set
	UPropagationRuleSet* RuleSet = NewObject<UPropagationRuleSet>();
	
	// Create fire spread rule
	UPropagationRuleAsset* FireSpreadRule = NewObject<UPropagationRuleAsset>();
	FireSpreadRule->RuleName = TEXT("FireSpread");
	FireSpreadRule->Priority = EPropagationRulePriority::High;
	FireSpreadRule->bIsEnabled = true;
	
	// Condition: neighbor has fire > 50
	FPropagationCondition FireCondition;
	FireCondition.ConditionType = EPropagationConditionType::NeighborCount;
	FireCondition.ValueKey = TEXT("Fire");
	FireCondition.Threshold = 50.0f;
	FireCondition.ComparisonOperator = TEXT(">");
	FireCondition.MinNeighborCount = 1;
	FireSpreadRule->Conditions.Add(FireCondition);
	
	// Action: add fire
	FPropagationAction FireAction;
	FireAction.ActionType = EPropagationActionType::AddValue;
	FireAction.TargetValueKey = TEXT("Fire");
	FireAction.ActionValue = 25.0f;
	FireSpreadRule->Actions.Add(FireAction);
	
	RuleSet->Rules.Add(FireSpreadRule);
	RuleEngine->SetRuleSet(RuleSet);
	
	// Test single cell execution
	{
		// Set up initial fire
		FVector FireSource(250, 250, 0);
		GridManager->SetCellValueAtWorldPosition(FireSource, TEXT("Fire"), 100.0f);
		
		// Execute rules for adjacent cell
		FVector AdjacentCell(350, 250, 0);
		int32 RulesExecuted = RuleEngine->ExecuteRulesForCell(AdjacentCell, 1.0f);
		
		TestTrue("Should execute at least one rule", RulesExecuted > 0);
		
		float AdjacentFire = GridManager->GetCellValueAtWorldPosition(AdjacentCell, TEXT("Fire"));
		TestTrue("Adjacent cell should have fire", AdjacentFire > 0.0f);
	}
	
	// Test bulk execution
	{
		TArray<FVector> CellPositions;
		for (int32 X = 0; X < 10; X++)
		{
			for (int32 Y = 0; Y < 10; Y++)
			{
				CellPositions.Add(FVector(X * 100 + 50, Y * 100 + 50, 0));
			}
		}
		
		FRuleExecutionStats Stats = RuleEngine->ExecuteRulesForCells(CellPositions, 1.0f);
		TestTrue("Should execute some rules", Stats.TotalRulesExecuted > 0);
		TestEqual("Should process cells", Stats.CellsProcessed, CellPositions.Num());
		TestTrue("Should have reasonable execution time", Stats.TotalExecutionTime < 1000.0f); // Less than 1 second
	}
	
	// Test performance statistics
	{
		FRuleExecutionStats Stats = RuleEngine->GetExecutionStatistics();
		TestTrue("Should have execution statistics", Stats.TotalRulesExecuted > 0);
		
		TMap<FName, float> RuleMetrics = RuleEngine->GetRulePerformanceMetrics();
		TestTrue("Should have rule performance metrics", RuleMetrics.Num() > 0);
	}
	
	// Test rule preview
	{
		FVector PreviewCell(450, 450, 0);
		FPropagationContext PreviewContext = RuleEngine->PreviewRuleExecution(PreviewCell, 1.0f);
		
		TestEqual("Preview context should have correct position", PreviewContext.CellPosition, PreviewCell);
		TestTrue("Preview context should have neighbor data", PreviewContext.NeighborPositions.Num() > 0);
	}
	
	// Test cache functionality
	{
		RuleEngine->ClearExecutionCache();
		
		// Execute same cell multiple times to test caching
		FVector TestCell(550, 550, 0);
		double StartTime = FPlatformTime::Seconds();
		
		for (int32 i = 0; i < 100; i++)
		{
			RuleEngine->ExecuteRulesForCell(TestCell, 1.0f);
		}
		
		double CachedTime = FPlatformTime::Seconds() - StartTime;
		TestTrue("Cached execution should be fast", CachedTime < 0.1); // Less than 100ms
	}

	return true;
}

/**
 * Integration test for complete rule system workflow
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPropagationRuleIntegrationTest, "SpatialFusion.Rules.Integration", 
	EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FPropagationRuleIntegrationTest::RunTest(const FString& Parameters)
{
	// Create complete fire simulation
	UInfluenceMapManager2D* GridManager = NewObject<UInfluenceMapManager2D>();
	GridManager->Initialize(FVector(20, 20, 1), 50.0f, FVector::ZeroVector);
	
	UPropagationRuleEngine* RuleEngine = NewObject<UPropagationRuleEngine>();
	RuleEngine->GridManager = GridManager;
	
	UPropagationRuleSet* FireRuleSet = NewObject<UPropagationRuleSet>();
	FireRuleSet->RuleSetName = TEXT("FireSimulation");
	
	// Create fire spread rule
	UPropagationRuleAsset* SpreadRule = NewObject<UPropagationRuleAsset>();
	SpreadRule->RuleName = TEXT("FireSpread");
	SpreadRule->Priority = EPropagationRulePriority::High;
	
	FPropagationCondition SpreadCondition;
	SpreadCondition.ConditionType = EPropagationConditionType::NeighborCount;
	SpreadCondition.ValueKey = TEXT("Temperature");
	SpreadCondition.Threshold = 80.0f;
	SpreadCondition.ComparisonOperator = TEXT(">");
	SpreadCondition.MinNeighborCount = 1;
	SpreadRule->Conditions.Add(SpreadCondition);
	
	FPropagationAction SpreadAction;
	SpreadAction.ActionType = EPropagationActionType::AddValue;
	SpreadAction.TargetValueKey = TEXT("Temperature");
	SpreadAction.ActionValue = 20.0f;
	SpreadRule->Actions.Add(SpreadAction);
	
	// Create fire decay rule
	UPropagationRuleAsset* DecayRule = NewObject<UPropagationRuleAsset>();
	DecayRule->RuleName = TEXT("FireDecay");
	DecayRule->Priority = EPropagationRulePriority::Normal;
	
	FPropagationCondition DecayCondition;
	DecayCondition.ConditionType = EPropagationConditionType::ValueThreshold;
	DecayCondition.ValueKey = TEXT("Temperature");
	DecayCondition.Threshold = 10.0f;
	DecayCondition.ComparisonOperator = TEXT(">");
	DecayRule->Conditions.Add(DecayCondition);
	
	FPropagationAction DecayAction;
	DecayAction.ActionType = EPropagationActionType::MultiplyValue;
	DecayAction.TargetValueKey = TEXT("Temperature");
	DecayAction.ActionValue = 0.95f; // 5% decay per step
	DecayRule->Actions.Add(DecayAction);
	
	FireRuleSet->Rules.Add(SpreadRule);
	FireRuleSet->Rules.Add(DecayRule);
	RuleEngine->SetRuleSet(FireRuleSet);
	
	// Initialize fire source
	FVector FireSource(500, 500, 0); // Center of grid
	GridManager->SetCellValueAtWorldPosition(FireSource, TEXT("Temperature"), 100.0f);
	
	// Run simulation for multiple steps
	float InitialTotalHeat = 0.0f;
	float FinalTotalHeat = 0.0f;
	
	// Calculate initial total heat
	for (int32 X = 0; X < 20; X++)
	{
		for (int32 Y = 0; Y < 20; Y++)
		{
			FVector CellPos(X * 50 + 25, Y * 50 + 25, 0);
			InitialTotalHeat += GridManager->GetCellValueAtWorldPosition(CellPos, TEXT("Temperature"));
		}
	}
	
	// Run simulation steps
	for (int32 Step = 0; Step < 10; Step++)
	{
		FRuleExecutionStats Stats = RuleEngine->ExecuteRulesForAllCells(1.0f);
		TestTrue("Each step should execute rules", Stats.TotalRulesExecuted > 0);
	}
	
	// Calculate final total heat
	for (int32 X = 0; X < 20; X++)
	{
		for (int32 Y = 0; Y < 20; Y++)
		{
			FVector CellPos(X * 50 + 25, Y * 50 + 25, 0);
			FinalTotalHeat += GridManager->GetCellValueAtWorldPosition(CellPos, TEXT("Temperature"));
		}
	}
	
	// Verify simulation behavior
	TestTrue("Fire should have spread", FinalTotalHeat > InitialTotalHeat);
	
	// Check that fire spread to adjacent cells
	FVector AdjacentCell(550, 500, 0);
	float AdjacentTemp = GridManager->GetCellValueAtWorldPosition(AdjacentCell, TEXT("Temperature"));
	TestTrue("Adjacent cell should have heat", AdjacentTemp > 0.0f);
	
	// Check that distant cells have less or no heat
	FVector DistantCell(100, 100, 0);
	float DistantTemp = GridManager->GetCellValueAtWorldPosition(DistantCell, TEXT("Temperature"));
	TestTrue("Distant cell should have less heat", DistantTemp < AdjacentTemp);
	
	return true;
}
