// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "FlowFieldTypes.generated.h"

/**
 * Flow field calculation method
 */
UENUM(BlueprintType)
enum class EFlowFieldMethod : uint8
{
	/** Standard Dijkstra algorithm */
	Dijkstra UMETA(DisplayName = "Dijkstra"),
	
	/** A* with multiple goals */
	AStar UMETA(DisplayName = "A*"),
	
	/** Jump Point Search for uniform cost grids */
	JPS UMETA(DisplayName = "Jump Point Search"),
	
	/** Hierarchical pathfinding */
	Hierarchical UMETA(DisplayName = "Hierarchical"),
	
	/** Custom algorithm */
	Custom UMETA(DisplayName = "Custom")
};

/**
 * Flow field update frequency
 */
UENUM(BlueprintType)
enum class EFlowFieldUpdateFrequency : uint8
{
	/** Update every frame */
	EveryFrame UMETA(DisplayName = "Every Frame"),
	
	/** Update at fixed intervals */
	FixedInterval UMETA(DisplayName = "Fixed Interval"),
	
	/** Update only when goals change */
	OnGoalChange UMETA(DisplayName = "On Goal Change"),
	
	/** Update only when obstacles change */
	OnObstacleChange UMETA(DisplayName = "On Obstacle Change"),
	
	/** Manual update only */
	Manual UMETA(DisplayName = "Manual")
};

/**
 * Flow field cost calculation mode
 */
UENUM(BlueprintType)
enum class EFlowFieldCostMode : uint8
{
	/** Uniform cost (all cells have same cost) */
	Uniform UMETA(DisplayName = "Uniform"),
	
	/** Terrain-based cost */
	Terrain UMETA(DisplayName = "Terrain"),
	
	/** Influence-based cost */
	Influence UMETA(DisplayName = "Influence"),
	
	/** Custom cost function */
	Custom UMETA(DisplayName = "Custom")
};

/**
 * Single cell in a flow field
 */
USTRUCT(BlueprintType)
struct SPATIALFUSIONRUNTIME_API FFlowFieldCell
{
	GENERATED_BODY()

	/** Distance to nearest goal (in grid units) */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	float Distance = FLT_MAX;

	/** Flow direction vector (normalized) */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	FVector FlowDirection = FVector::ZeroVector;

	/** Movement cost for this cell */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	float MovementCost = 1.0f;

	/** Whether this cell is blocked */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	bool bIsBlocked = false;

	/** Whether this cell is a goal */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	bool bIsGoal = false;

	/** Integration field value (used during calculation) */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	float IntegrationValue = FLT_MAX;

	/** Whether this cell has been visited during calculation */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	bool bVisited = false;

	/** Custom metadata for this cell */
	UPROPERTY(BlueprintReadWrite, Category = "Flow Field")
	TMap<FName, float> Metadata;

	FFlowFieldCell()
	{
		Reset();
	}

	/** Reset cell to default state */
	void Reset()
	{
		Distance = FLT_MAX;
		FlowDirection = FVector::ZeroVector;
		MovementCost = 1.0f;
		bIsBlocked = false;
		bIsGoal = false;
		IntegrationValue = FLT_MAX;
		bVisited = false;
		Metadata.Empty();
	}

	/** Check if this cell is passable */
	bool IsPassable() const
	{
		return !bIsBlocked && MovementCost < FLT_MAX;
	}

	/** Check if this cell has valid flow data */
	bool HasValidFlow() const
	{
		return Distance < FLT_MAX && !FlowDirection.IsZero();
	}
};

/**
 * Flow field goal definition
 */
USTRUCT(BlueprintType)
struct SPATIALFUSIONRUNTIME_API FFlowFieldGoal
{
	GENERATED_BODY()

	/** Goal position in world coordinates */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	FVector WorldPosition = FVector::ZeroVector;

	/** Goal position in grid coordinates */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	FVector GridPosition = FVector::ZeroVector;

	/** Goal weight/priority (higher = more attractive) */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	float Weight = 1.0f;

	/** Goal radius in grid units */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	float Radius = 1.0f;

	/** Goal tag for identification */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	FGameplayTag GoalTag;

	/** Whether this goal is currently active */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	bool bIsActive = true;

	/** Custom goal metadata */
	UPROPERTY(BlueprintReadWrite, Category = "Goal")
	TMap<FName, float> Metadata;

	FFlowFieldGoal()
	{
	}

	FFlowFieldGoal(const FVector& InWorldPosition, float InWeight = 1.0f, float InRadius = 1.0f)
		: WorldPosition(InWorldPosition)
		, Weight(InWeight)
		, Radius(InRadius)
	{
	}

	/** Check if this goal is valid and active */
	bool IsValid() const
	{
		return bIsActive && Weight > 0.0f;
	}
};

/**
 * Flow field calculation parameters
 */
USTRUCT(BlueprintType)
struct SPATIALFUSIONRUNTIME_API FFlowFieldParams
{
	GENERATED_BODY()

	/** Calculation method to use */
	UPROPERTY(BlueprintReadWrite, Category = "Calculation")
	EFlowFieldMethod Method = EFlowFieldMethod::Dijkstra;

	/** Cost calculation mode */
	UPROPERTY(BlueprintReadWrite, Category = "Calculation")
	EFlowFieldCostMode CostMode = EFlowFieldCostMode::Uniform;

	/** Update frequency */
	UPROPERTY(BlueprintReadWrite, Category = "Update")
	EFlowFieldUpdateFrequency UpdateFrequency = EFlowFieldUpdateFrequency::OnGoalChange;

	/** Update interval for fixed frequency updates */
	UPROPERTY(BlueprintReadWrite, Category = "Update")
	float UpdateInterval = 0.1f;

	/** Maximum distance to calculate (optimization) */
	UPROPERTY(BlueprintReadWrite, Category = "Optimization")
	float MaxDistance = 10000.0f;

	/** Whether to allow diagonal movement */
	UPROPERTY(BlueprintReadWrite, Category = "Movement")
	bool bAllowDiagonal = true;

	/** Cost multiplier for diagonal movement */
	UPROPERTY(BlueprintReadWrite, Category = "Movement")
	float DiagonalCostMultiplier = 1.414f; // sqrt(2)

	/** Smoothing factor for flow directions */
	UPROPERTY(BlueprintReadWrite, Category = "Smoothing")
	float FlowSmoothingFactor = 0.5f;

	/** Whether to use line-of-sight optimization */
	UPROPERTY(BlueprintReadWrite, Category = "Optimization")
	bool bUseLineOfSight = false;

	/** Whether to use hierarchical pathfinding */
	UPROPERTY(BlueprintReadWrite, Category = "Optimization")
	bool bUseHierarchical = false;

	/** Hierarchical cluster size */
	UPROPERTY(BlueprintReadWrite, Category = "Optimization")
	int32 ClusterSize = 8;

	/** Custom cost function tag */
	UPROPERTY(BlueprintReadWrite, Category = "Custom")
	FGameplayTag CustomCostTag;

	/** Custom algorithm tag */
	UPROPERTY(BlueprintReadWrite, Category = "Custom")
	FGameplayTag CustomAlgorithmTag;

	FFlowFieldParams()
	{
	}
};

/**
 * Flow field calculation result
 */
USTRUCT(BlueprintType)
struct SPATIALFUSIONRUNTIME_API FFlowFieldResult
{
	GENERATED_BODY()

	/** Whether the calculation was successful */
	UPROPERTY(BlueprintReadWrite, Category = "Result")
	bool bSuccess = false;

	/** Number of cells processed */
	UPROPERTY(BlueprintReadWrite, Category = "Result")
	int32 CellsProcessed = 0;

	/** Calculation time in milliseconds */
	UPROPERTY(BlueprintReadWrite, Category = "Result")
	float CalculationTime = 0.0f;

	/** Maximum distance found */
	UPROPERTY(BlueprintReadWrite, Category = "Result")
	float MaxDistance = 0.0f;

	/** Number of goals reached */
	UPROPERTY(BlueprintReadWrite, Category = "Result")
	int32 GoalsReached = 0;

	/** Error message if calculation failed */
	UPROPERTY(BlueprintReadWrite, Category = "Result")
	FString ErrorMessage;

	FFlowFieldResult()
	{
	}
};

/**
 * Flow field pathfinding query
 */
USTRUCT(BlueprintType)
struct SPATIALFUSIONRUNTIME_API FFlowFieldQuery
{
	GENERATED_BODY()

	/** Start position in world coordinates */
	UPROPERTY(BlueprintReadWrite, Category = "Query")
	FVector StartPosition = FVector::ZeroVector;

	/** Goal positions */
	UPROPERTY(BlueprintReadWrite, Category = "Query")
	TArray<FFlowFieldGoal> Goals;

	/** Calculation parameters */
	UPROPERTY(BlueprintReadWrite, Category = "Query")
	FFlowFieldParams Params;

	/** Query tag for identification */
	UPROPERTY(BlueprintReadWrite, Category = "Query")
	FGameplayTag QueryTag;

	/** Whether this query is active */
	UPROPERTY(BlueprintReadWrite, Category = "Query")
	bool bIsActive = true;

	FFlowFieldQuery()
	{
	}

	/** Add a goal to this query */
	void AddGoal(const FFlowFieldGoal& Goal)
	{
		Goals.Add(Goal);
	}

	/** Remove goals with specific tag */
	void RemoveGoalsByTag(const FGameplayTag& Tag)
	{
		Goals.RemoveAll([&Tag](const FFlowFieldGoal& Goal)
		{
			return Goal.GoalTag == Tag;
		});
	}

	/** Get all active goals */
	TArray<FFlowFieldGoal> GetActiveGoals() const
	{
		TArray<FFlowFieldGoal> ActiveGoals;
		for (const FFlowFieldGoal& Goal : Goals)
		{
			if (Goal.IsValid())
			{
				ActiveGoals.Add(Goal);
			}
		}
		return ActiveGoals;
	}
};
