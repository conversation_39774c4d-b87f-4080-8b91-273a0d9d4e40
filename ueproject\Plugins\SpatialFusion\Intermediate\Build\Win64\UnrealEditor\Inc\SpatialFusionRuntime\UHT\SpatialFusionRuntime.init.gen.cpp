// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeSpatialFusionRuntime_init() {}
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_SpatialFusionRuntime_CustomPropagationDelegate__DelegateSignature();
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_SpatialFusionRuntime_OnRuleExecutionCompleted__DelegateSignature();
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_SpatialFusionRuntime_OnRuleExecutionFailed__DelegateSignature();
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_SpatialFusionRuntime_OnRuleExecutionStarted__DelegateSignature();
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_UInfluenceMapManagerBase_OnPropagationUpdated__DelegateSignature();
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_UInfluenceMapManagerBase_OnRuleAdded__DelegateSignature();
	SPATIALFUSIONRUNTIME_API UFunction* Z_Construct_UDelegateFunction_UInfluenceMapManagerBase_OnRuleRemoved__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_SpatialFusionRuntime;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_SpatialFusionRuntime()
	{
		if (!Z_Registration_Info_UPackage__Script_SpatialFusionRuntime.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_SpatialFusionRuntime_CustomPropagationDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_SpatialFusionRuntime_OnRuleExecutionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_SpatialFusionRuntime_OnRuleExecutionFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_SpatialFusionRuntime_OnRuleExecutionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UInfluenceMapManagerBase_OnPropagationUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UInfluenceMapManagerBase_OnRuleAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UInfluenceMapManagerBase_OnRuleRemoved__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/SpatialFusionRuntime",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x75C10319,
				0x0727745E,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_SpatialFusionRuntime.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_SpatialFusionRuntime.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_SpatialFusionRuntime(Z_Construct_UPackage__Script_SpatialFusionRuntime, TEXT("/Script/SpatialFusionRuntime"), Z_Registration_Info_UPackage__Script_SpatialFusionRuntime, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x75C10319, 0x0727745E));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
